{"name": "test", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@arco-design/web-vue": "^2.57.0", "@guolao/vue-monaco-editor": "^1.5.5", "@monaco-editor/loader": "^1.5.0", "@tdesign-vue-next/chat": "^0.2.4", "@vue-flow/background": "^1.3.2", "@vue-flow/controls": "^1.1.2", "@vue-flow/core": "^1.42.4", "@vue-flow/minimap": "^1.5.2", "axios": "^1.8.4", "diff-match-patch": "^1.0.5", "highlight.js": "^11.11.1", "js-yaml": "^4.1.0", "lodash-es": "^4.17.21", "marked": "^16.1.2", "nanoid": "^5.1.5", "path": "^0.12.7", "pinia": "^3.0.2", "tdesign-vue-next": "^1.11.5", "uuid": "^11.1.0", "v-code-diff": "^1.13.1", "vue": "^3.5.13", "vue-json-diff": "^1.0.1", "vue-json-pretty": "^2.4.0", "vue-router": "4", "vue3-json-viewer": "^2.3.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "unplugin-vue-components": "^28.4.1", "vite": "^6.2.0"}}