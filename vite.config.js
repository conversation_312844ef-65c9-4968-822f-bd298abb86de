import {defineConfig, loadEnv} from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
// 添加 Arco Design UI 的按需引入插件
import Components from 'unplugin-vue-components/vite'
import {ArcoResolver} from 'unplugin-vue-components/resolvers'

// https://vite.dev/config/
export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd())
  
  return {
    plugins: [
      vue(),
      // 添加 Arco Design UI 组件自动导入
      Components({
        resolvers: [
          ArcoResolver({
            sideEffect: true
          })
        ]
      })
    ],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        'v-code-diff': path.resolve(__dirname, 'node_modules/v-code-diff/dist/v3/index.es.js')
      }
    },
    optimizeDeps: {
      include: ['v-code-diff']
    },
    server: {
      host: '0.0.0.0', // 允许内网访问
      port: 5173, // 开发服务器端口
      open: true, // 自动打开浏览器
      cors: true, // 允许跨域
      proxy: {
        // 代理配置
        '/api': {
          target: "http://cookery-dev.myroki.com",// 开发环境后端服务器地址
          changeOrigin: true, // 允许跨域
        },
        '/abp/manager': {
          target: "https://ai-test-platform.myroki.com",// 开发环境后端服务器地址
          changeOrigin: true,
          pathRewrite: {
            '^/abp/manager':''
          }
        }
      }
    }
  }
})
