{"edges": [{"id": "1728373706400-source-1728373743598-target", "data": {"sourceType": "code", "targetType": "if-else", "isInIteration": false}, "type": "custom", "source": "1728373706400", "target": "1728373743598", "zIndex": 0, "selected": false, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1728373743598-true-1728373759655-target", "data": {"sourceType": "if-else", "targetType": "answer", "isInIteration": false}, "type": "custom", "source": "1728373743598", "target": "1728373759655", "zIndex": 0, "selected": false, "sourceHandle": "true", "targetHandle": "target"}, {"id": "1728552934508-source-1728553083494-target", "data": {"sourceType": "code", "targetType": "if-else", "isInIteration": false}, "type": "custom", "source": "1728552934508", "target": "1728553083494", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1728553083494-true-1728553106110-target", "data": {"sourceType": "if-else", "targetType": "answer", "isInIteration": false}, "type": "custom", "source": "1728553083494", "target": "1728553106110", "zIndex": 0, "sourceHandle": "true", "targetHandle": "target"}, {"id": "1728553083494-false-1728553167033-target", "data": {"sourceType": "if-else", "targetType": "llm", "isInIteration": false}, "type": "custom", "source": "1728553083494", "target": "1728553167033", "zIndex": 0, "sourceHandle": "false", "targetHandle": "target"}, {"id": "1728553167033-source-1728553250189-target", "data": {"sourceType": "llm", "targetType": "answer", "isInIteration": false}, "type": "custom", "source": "1728553167033", "target": "1728553250189", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1728613209040-source-1728613254355-target", "data": {"sourceType": "llm", "targetType": "answer", "isInIteration": false}, "type": "custom", "source": "1728613209040", "target": "1728613254355", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1728373706400-source-1729149220338-target", "data": {"sourceType": "code", "targetType": "if-else", "isInIteration": false}, "type": "custom", "source": "1728373706400", "target": "1729149220338", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1729149220338-true-1729149258024-target", "data": {"sourceType": "if-else", "targetType": "code", "isInIteration": false}, "type": "custom", "source": "1729149220338", "target": "1729149258024", "zIndex": 0, "sourceHandle": "true", "targetHandle": "target"}, {"id": "1728373832821-source-1730269324857-target", "data": {"sourceType": "answer", "targetType": "code", "isInIteration": false}, "type": "custom", "source": "1728373832821", "target": "1730269324857", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1730269324857-source-1730269343323-target", "data": {"sourceType": "code", "targetType": "answer", "isInIteration": false}, "type": "custom", "source": "1730269324857", "target": "1730269343323", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1728373743598-be2f4ad1-1923-4291-936c-2f7c9496a221-1731923680317-target", "data": {"sourceType": "if-else", "targetType": "llm", "isInIteration": false}, "type": "custom", "source": "1728373743598", "target": "1731923680317", "zIndex": 0, "sourceHandle": "be2f4ad1-1923-4291-936c-2f7c9496a221", "targetHandle": "target"}, {"id": "1731923680317-source-1731923706494-target", "data": {"sourceType": "llm", "targetType": "answer", "isInIteration": false}, "type": "custom", "source": "1731923680317", "target": "1731923706494", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1731923706494-source-1730269435968-target", "data": {"sourceType": "answer", "targetType": "code", "isInIteration": false}, "type": "custom", "source": "1731923706494", "target": "1730269435968", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1730269435968-source-1730269454434-target", "data": {"sourceType": "code", "targetType": "answer", "isInIteration": false}, "type": "custom", "source": "1730269435968", "target": "1730269454434", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1723454925753-source-answer-target", "data": {"sourceType": "llm", "targetType": "answer", "isInIteration": false}, "type": "custom", "source": "1723454925753", "target": "answer", "zIndex": 0, "selected": false, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1724835194813-false-1724374762933-target", "data": {"sourceType": "if-else", "targetType": "code", "isInIteration": false}, "type": "custom", "source": "1724835194813", "target": "1724374762933", "zIndex": 0, "selected": false, "sourceHandle": "false", "targetHandle": "target"}, {"id": "1725414397888-source-1724374762993-target", "data": {"sourceType": "code", "targetType": "code", "isInIteration": false}, "type": "custom", "source": "1725414397888", "target": "1724374762993", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1724374762993-source-1725414565233-target", "data": {"sourceType": "code", "targetType": "llm", "isInIteration": false}, "type": "custom", "source": "1724374762993", "target": "1725414565233", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1725414565233-source-1724835282480-target", "data": {"sourceType": "llm", "targetType": "answer", "isInIteration": false}, "type": "custom", "source": "1725414565233", "target": "1724835282480", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1724835194813-a70736fe-f5cc-40a7-8914-be099f14ead0-1725418930599-target", "data": {"sourceType": "if-else", "targetType": "code", "isInIteration": false}, "type": "custom", "source": "1724835194813", "target": "1725418930599", "zIndex": 0, "sourceHandle": "a70736fe-f5cc-40a7-8914-be099f14ead0", "targetHandle": "target"}, {"id": "1724835194813-false-1725419225999-target", "data": {"sourceType": "if-else", "targetType": "if-else", "isInIteration": false}, "type": "custom", "source": "1724835194813", "target": "1725419225999", "zIndex": 0, "sourceHandle": "false", "targetHandle": "target"}, {"id": "1724835194813-true-1725414397888-target", "data": {"sourceType": "if-else", "targetType": "code", "isInIteration": false}, "type": "custom", "source": "1724835194813", "target": "1725414397888", "zIndex": 0, "sourceHandle": "true", "targetHandle": "target"}, {"id": "1725419225999-20473c02-14cf-4efa-a21e-c30e339fe084-1725353208949-target", "data": {"sourceType": "if-else", "targetType": "code", "isInIteration": false}, "type": "custom", "source": "1725419225999", "target": "1725353208949", "zIndex": 0, "sourceHandle": "20473c02-14cf-4efa-a21e-c30e339fe084", "targetHandle": "target"}, {"id": "1725420216737-source-17248354180280-target", "data": {"sourceType": "llm", "targetType": "answer", "isInIteration": false}, "type": "custom", "source": "1725420216737", "target": "17248354180280", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1725419225999-false-1725443133415-target", "data": {"sourceType": "if-else", "targetType": "answer", "isInIteration": false}, "type": "custom", "source": "1725419225999", "target": "1725443133415", "zIndex": 0, "sourceHandle": "false", "targetHandle": "target"}, {"id": "1724374762933-source-1725444253811-target", "data": {"sourceType": "code", "targetType": "if-else", "isInIteration": false}, "type": "custom", "source": "1724374762933", "target": "1725444253811", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1725444253811-false-1723454925753-target", "data": {"sourceType": "if-else", "targetType": "llm", "isInIteration": false}, "type": "custom", "source": "1725444253811", "target": "1723454925753", "zIndex": 0, "sourceHandle": "false", "targetHandle": "target"}, {"id": "1725444253811-true-1725444275896-target", "data": {"sourceType": "if-else", "targetType": "answer", "isInIteration": false}, "type": "custom", "source": "1725444253811", "target": "1725444275896", "zIndex": 0, "sourceHandle": "true", "targetHandle": "target"}, {"id": "1725853672695-source-1724835336615-target", "data": {"sourceType": "llm", "targetType": "answer", "isInIteration": false}, "type": "custom", "source": "1725853672695", "target": "1724835336615", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1725418930599-source-1726022983015-target", "data": {"sourceType": "code", "targetType": "if-else", "isInIteration": false}, "type": "custom", "source": "1725418930599", "target": "1726022983015", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1726022983015-false-1725420216737-target", "data": {"sourceType": "if-else", "targetType": "llm", "isInIteration": false}, "type": "custom", "source": "1726022983015", "target": "1725420216737", "zIndex": 0, "sourceHandle": "false", "targetHandle": "target"}, {"id": "1726022983015-true-1726023033474-target", "data": {"sourceType": "if-else", "targetType": "answer", "isInIteration": false}, "type": "custom", "source": "1726022983015", "target": "1726023033474", "zIndex": 0, "sourceHandle": "true", "targetHandle": "target"}, {"id": "1725418930599-source-1726810485421-target", "data": {"sourceType": "code", "targetType": "code", "isInIteration": false}, "type": "custom", "source": "1725418930599", "target": "1726810485421", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1725353208949-source-1729495898459-target", "data": {"sourceType": "code", "targetType": "if-else", "isInIteration": false}, "type": "custom", "source": "1725353208949", "target": "1729495898459", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1729495898459-false-1725853672695-target", "data": {"sourceType": "if-else", "targetType": "llm", "isInIteration": false}, "type": "custom", "source": "1729495898459", "target": "1725853672695", "zIndex": 0, "sourceHandle": "false", "targetHandle": "target"}, {"id": "1729495898459-true-1729495941028-target", "data": {"sourceType": "if-else", "targetType": "answer", "isInIteration": false}, "type": "custom", "source": "1729495898459", "target": "1729495941028", "zIndex": 0, "sourceHandle": "true", "targetHandle": "target"}, {"id": "1728373955721-r3ae07d0-e265-4b69-9989-r5b3f18b8725-1722935422791-target", "data": {"sourceType": "if-else", "targetType": "if-else", "isInIteration": false}, "type": "custom", "source": "1728373955721", "target": "1722935422791", "zIndex": 0, "sourceHandle": "r3ae07d0-e265-4b69-9989-r5b3f18b8725", "targetHandle": "target"}, {"id": "1722935422791-source-1724835194813-target", "data": {"sourceType": "code", "targetType": "if-else", "isInIteration": false}, "type": "custom", "source": "1722935422791", "target": "1724835194813", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1728373955721-false-1728373706400-target", "data": {"sourceType": "if-else", "targetType": "code", "isInIteration": false}, "type": "custom", "source": "1728373955721", "target": "1728373706400", "zIndex": 0, "sourceHandle": "false", "targetHandle": "target"}, {"id": "1724835282480-source-1730269854268-target", "data": {"sourceType": "answer", "targetType": "code", "isInIteration": false}, "type": "custom", "source": "1724835282480", "target": "1730269854268", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1730269854268-source-1730267425941-target", "data": {"sourceType": "code", "targetType": "answer", "isInIteration": false}, "type": "custom", "source": "1730269854268", "target": "1730267425941", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "answer-source-1730264125478-target", "data": {"sourceType": "answer", "targetType": "code", "isInIteration": false}, "type": "custom", "source": "answer", "target": "1730264125478", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1730264125478-source-1730268411247-target", "data": {"sourceType": "code", "targetType": "answer", "isInIteration": false}, "type": "custom", "source": "1730264125478", "target": "1730268411247", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1728373743598-false-1735868660061-target", "data": {"sourceType": "if-else", "targetType": "if-else", "isInIteration": false}, "type": "custom", "source": "1728373743598", "target": "1735868660061", "zIndex": 0, "selected": false, "sourceHandle": "false", "targetHandle": "target"}, {"id": "1735868660061-true-1728373825523-target", "data": {"sourceType": "if-else", "targetType": "llm", "isInIteration": false}, "type": "custom", "source": "1735868660061", "target": "1728373825523", "zIndex": 0, "selected": false, "sourceHandle": "true", "targetHandle": "target"}, {"id": "1735868660061-false-1735868814427-target", "data": {"sourceType": "if-else", "targetType": "llm", "isInIteration": false}, "type": "custom", "source": "1735868660061", "target": "1735868814427", "zIndex": 0, "selected": false, "sourceHandle": "false", "targetHandle": "target"}, {"id": "1728373825523-source-1735869161318-target", "data": {"sourceType": "llm", "targetType": "variable-aggregator", "isInIteration": false}, "type": "custom", "source": "1728373825523", "target": "1735869161318", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1735868814427-source-1735869161318-target", "data": {"sourceType": "llm", "targetType": "variable-aggregator", "isInIteration": false}, "type": "custom", "source": "1735868814427", "target": "1735869161318", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1735869161318-source-1728373832821-target", "data": {"sourceType": "variable-aggregator", "targetType": "answer", "isInIteration": false}, "type": "custom", "source": "1735869161318", "target": "1728373832821", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1739857356836-source-1739857493593-target", "data": {"sourceType": "code", "targetType": "if-else", "isInIteration": false}, "type": "custom", "source": "1739857356836", "target": "1739857493593", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1739857493593-false-1728375675821-target", "data": {"sourceType": "if-else", "targetType": "code", "isInIteration": false}, "type": "custom", "source": "1739857493593", "target": "1728375675821", "zIndex": 0, "sourceHandle": "false", "targetHandle": "target"}, {"id": "1739857493593-true-1728375675821-target", "data": {"sourceType": "if-else", "targetType": "code", "isInIteration": false}, "type": "custom", "source": "1739857493593", "target": "1728375675821", "zIndex": 0, "sourceHandle": "true", "targetHandle": "target"}, {"id": "1739857493593-true-17266271288090-target", "data": {"sourceType": "if-else", "targetType": "llm", "isInIteration": false}, "type": "custom", "source": "1739857493593", "target": "17266271288090", "zIndex": 0, "sourceHandle": "true", "targetHandle": "target"}, {"id": "1739857493593-true-1739857618889-target", "data": {"sourceType": "if-else", "targetType": "code", "isInIteration": false}, "type": "custom", "source": "1739857493593", "target": "1739857618889", "zIndex": 0, "sourceHandle": "true", "targetHandle": "target"}, {"id": "1739857618889-source-1728373955721-target", "data": {"sourceType": "code", "targetType": "if-else", "isInIteration": false}, "type": "custom", "source": "1739857618889", "target": "1728373955721", "zIndex": 0, "selected": false, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1728373955721-59ba5acf-59db-439b-aadc-0c7505d5a318-1739864195288-target", "data": {"sourceType": "if-else", "targetType": "llm", "isInIteration": false}, "type": "custom", "source": "1728373955721", "target": "1739864195288", "zIndex": 0, "sourceHandle": "59ba5acf-59db-439b-aadc-0c7505d5a318", "targetHandle": "target"}, {"id": "1739864195288-source-1739864244544-target", "data": {"sourceType": "llm", "targetType": "answer", "isInIteration": false}, "type": "custom", "source": "1739864195288", "target": "1739864244544", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "17266271288090-source-1728373955721-target", "data": {"sourceType": "llm", "targetType": "if-else", "isInIteration": false}, "type": "custom", "source": "17266271288090", "target": "1728373955721", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1735868660061-22e19443-b1a5-4019-87f9-fca5a05299d2-1740465788413-target", "data": {"sourceType": "if-else", "targetType": "llm", "isInIteration": false}, "type": "custom", "source": "1735868660061", "target": "1740465788413", "zIndex": 0, "sourceHandle": "22e19443-b1a5-4019-87f9-fca5a05299d2", "targetHandle": "target"}, {"id": "1740465788413-source-1735869161318-target", "data": {"sourceType": "llm", "targetType": "variable-aggregator", "isInIteration": false}, "type": "custom", "source": "1740465788413", "target": "1735869161318", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1728373955721-4f74ed7c-ce66-402f-8c30-786aa7d48013-1740713258587-target", "data": {"sourceType": "if-else", "targetType": "if-else", "isInIteration": false}, "type": "custom", "source": "1728373955721", "target": "1740713258587", "zIndex": 0, "sourceHandle": "4f74ed7c-ce66-402f-8c30-786aa7d48013", "targetHandle": "target"}, {"id": "1740713258587-false-17266271379210-target", "data": {"sourceType": "if-else", "targetType": "answer", "isInIteration": false}, "type": "custom", "source": "1740713258587", "target": "17266271379210", "zIndex": 0, "sourceHandle": "false", "targetHandle": "target"}, {"id": "1740713258587-true-1740713516937-target", "data": {"sourceType": "if-else", "targetType": "llm", "isInIteration": false}, "type": "custom", "source": "1740713258587", "target": "1740713516937", "zIndex": 0, "sourceHandle": "true", "targetHandle": "target"}, {"id": "1740713516937-source-1740713583844-target", "data": {"sourceType": "llm", "targetType": "answer", "isInIteration": false}, "type": "custom", "source": "1740713516937", "target": "1740713583844", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1728373743598-d3ae07d0-e265-4b69-9989-d5b3f18b8721-1740713645824-target", "data": {"sourceType": "if-else", "targetType": "if-else", "isInIteration": false}, "type": "custom", "source": "1728373743598", "target": "1740713645824", "zIndex": 0, "sourceHandle": "d3ae07d0-e265-4b69-9989-d5b3f18b8721", "targetHandle": "target"}, {"id": "1740713645824-false-1728613209040-target", "data": {"sourceType": "if-else", "targetType": "llm", "isInIteration": false}, "type": "custom", "source": "1740713645824", "target": "1728613209040", "zIndex": 0, "sourceHandle": "false", "targetHandle": "target"}, {"id": "1740713645824-true-1740713714150-target", "data": {"sourceType": "if-else", "targetType": "llm", "isInIteration": false}, "type": "custom", "source": "1740713645824", "target": "1740713714150", "zIndex": 0, "sourceHandle": "true", "targetHandle": "target"}, {"id": "1740713714150-source-1740713748008-target", "data": {"sourceType": "llm", "targetType": "answer", "isInIteration": false}, "type": "custom", "source": "1740713714150", "target": "1740713748008", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "17266271379210-source-1740644963181-target", "data": {"sourceType": "answer", "targetType": "answer", "isInIteration": false}, "type": "custom", "source": "17266271379210", "target": "1740644963181", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1740713583844-source-1740734406522-target", "data": {"sourceType": "answer", "targetType": "variable-aggregator", "isInIteration": false}, "type": "custom", "source": "1740713583844", "target": "1740734406522", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1740644963181-source-1740734406522-target", "data": {"sourceType": "answer", "targetType": "variable-aggregator", "isInIteration": false}, "type": "custom", "source": "1740644963181", "target": "1740734406522", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1739864244544-source-1740734406522-target", "data": {"sourceType": "answer", "targetType": "variable-aggregator", "isInIteration": false}, "type": "custom", "source": "1739864244544", "target": "1740734406522", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1740734406522-source-1740734359062-target", "data": {"sourceType": "variable-aggregator", "targetType": "code", "isInIteration": false}, "type": "custom", "source": "1740734406522", "target": "1740734359062", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1740734359062-source-1740732703484-target", "data": {"sourceType": "code", "targetType": "answer", "isInIteration": false}, "type": "custom", "source": "1740734359062", "target": "1740732703484", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1728373697410-source-1740721114490-target", "data": {"sourceType": "start", "targetType": "if-else", "isInIteration": false}, "type": "custom", "source": "1728373697410", "target": "1740721114490", "zIndex": 0, "selected": false, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1740721114490-true-1740722677926-target", "data": {"sourceType": "if-else", "targetType": "code", "isInIteration": false}, "type": "custom", "source": "1740721114490", "target": "1740722677926", "zIndex": 0, "sourceHandle": "true", "targetHandle": "target"}, {"id": "1740721114490-false-1739857356836-target", "data": {"sourceType": "if-else", "targetType": "code", "isInIteration": false}, "type": "custom", "source": "1740721114490", "target": "1739857356836", "zIndex": 0, "sourceHandle": "false", "targetHandle": "target"}, {"id": "1740969899676-source-1740965806657-target", "data": {"sourceType": "llm", "targetType": "answer", "isInIteration": false}, "type": "custom", "source": "1740969899676", "target": "1740965806657", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1740713645824-9570ea90-3fb0-4c22-9f42-b1eef5110341-1741154528802-target", "data": {"sourceType": "if-else", "targetType": "llm", "isInIteration": false}, "type": "custom", "source": "1740713645824", "target": "1741154528802", "zIndex": 0, "sourceHandle": "9570ea90-3fb0-4c22-9f42-b1eef5110341", "targetHandle": "target"}, {"id": "1741154528802-source-1741154573017-target", "data": {"sourceType": "llm", "targetType": "answer", "isInIteration": false}, "type": "custom", "source": "1741154528802", "target": "1741154573017", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1740965806657-source-1741166868768-target", "data": {"sourceType": "answer", "targetType": "code", "isInIteration": false}, "type": "custom", "source": "1740965806657", "target": "1741166868768", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1741166868768-source-1740968213518-target", "data": {"sourceType": "code", "targetType": "answer", "isInIteration": false}, "type": "custom", "source": "1741166868768", "target": "1740968213518", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1739857493593-true-1741239235422-target", "data": {"sourceType": "if-else", "targetType": "code", "isInIteration": false}, "type": "custom", "source": "1739857493593", "target": "1741239235422", "zIndex": 0, "sourceHandle": "true", "targetHandle": "target"}, {"id": "1741239235422-source-1741239358087-target", "data": {"sourceType": "code", "targetType": "variable-aggregator", "isInIteration": false}, "type": "custom", "source": "1741239235422", "target": "1741239358087", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1728375675821-source-1741239358087-target", "data": {"sourceType": "code", "targetType": "variable-aggregator", "isInIteration": false}, "type": "custom", "source": "1728375675821", "target": "1741239358087", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1741239358087-source-1728373955721-target", "data": {"sourceType": "variable-aggregator", "targetType": "if-else", "isInIteration": false}, "type": "custom", "source": "1741239358087", "target": "1728373955721", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1740722677926-source-1741760448471-target", "data": {"sourceType": "code", "targetType": "if-else", "isInIteration": false}, "type": "custom", "source": "1740722677926", "target": "1741760448471", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1741760448471-true-1740969899676-target", "data": {"sourceType": "if-else", "targetType": "llm", "isInIteration": false}, "type": "custom", "source": "1741760448471", "target": "1740969899676", "zIndex": 0, "sourceHandle": "true", "targetHandle": "target"}, {"id": "1741759479689-source-1741759488335-target", "data": {"sourceType": "llm", "targetType": "answer", "isInIteration": false}, "type": "custom", "source": "1741759479689", "target": "1741759488335", "zIndex": 0, "selected": false, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1741760448471-false-1741759479689-target", "data": {"sourceType": "if-else", "targetType": "llm", "isInIteration": false}, "type": "custom", "source": "1741760448471", "target": "1741759479689", "zIndex": 0, "sourceHandle": "false", "targetHandle": "target"}, {"id": "1740713748008-source-1741945361603-target", "data": {"sourceType": "answer", "targetType": "variable-aggregator", "isInIteration": false}, "type": "custom", "source": "1740713748008", "target": "1741945361603", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1741154573017-source-1741945361603-target", "data": {"sourceType": "answer", "targetType": "variable-aggregator", "isInIteration": false}, "type": "custom", "source": "1741154573017", "target": "1741945361603", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1728613254355-source-1741945361603-target", "data": {"sourceType": "answer", "targetType": "variable-aggregator", "isInIteration": false}, "type": "custom", "source": "1728613254355", "target": "1741945361603", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1741945361603-source-1741945607974-target", "data": {"sourceType": "variable-aggregator", "targetType": "code", "isInIteration": false}, "type": "custom", "source": "1741945361603", "target": "1741945607974", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}, {"id": "1741945607974-source-1741945612944-target", "data": {"sourceType": "code", "targetType": "answer", "isInIteration": false}, "type": "custom", "source": "1741945607974", "target": "1741945612944", "zIndex": 0, "sourceHandle": "source", "targetHandle": "target"}], "nodes": [{"id": "1728373697410", "data": {"desc": "", "type": "start", "title": "开始", "selected": false, "variables": []}, "type": "custom", "width": 232, "height": 152, "dragging": false, "position": {"x": -2460.6195549401923, "y": 419.8128333044077}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": -2460.6195549401923, "y": 419.8128333044077}}, {"id": "1728373706400", "data": {"code": "\ndef main(arg1: int, arg2: int) -> dict:\n    return {\n        \"result\": arg1 + arg2,\n    }\n", "desc": "", "type": "method", "title": "cookeryGodRetrieval", "outputs": {"qaGroups": {"type": "string", "children": null}, "cookbooks": {"type": "string", "children": null}, "weatherInfo": {"type": "string", "children": null}, "cookbookCards": {"type": "string", "children": null}, "placeholderMap": {"type": "object", "children": null}, "cookeryQaAnswer": {"type": "string", "children": null}, "otherFileChunks": {"type": "string", "children": null}, "regionalDietary": {"type": "string", "children": null}, "cookbookExamples": {"type": "string", "children": null}, "cookeryFileChunks": {"type": "string", "children": null}, "productFileChunks": {"type": "string", "children": null}, "foodstuffNutrition": {"type": "string", "children": null}, "foodstuffsFileChunks": {"type": "string", "children": null}, "mealPlanningCookbooks": {"type": "string", "children": null}, "obesityNutritionFileChunks": {"type": "string", "children": null}}, "selected": false, "variables": [{"variable": "rewritten<PERSON><PERSON><PERSON>", "value_selector": ["1741239358087", "output"]}, {"variable": "productNames", "value_selector": ["1728375675821", "productNames"]}, {"variable": "otherIntents", "value_selector": ["1728375675821", "otherIntents"]}, {"variable": "otherIntentsValues", "value_selector": ["1728375675821", "otherIntentsValues"]}, {"variable": "agentId", "value_selector": ["sys", "agent_id"]}, {"variable": "intent", "value_selector": ["1728375675821", "intent"]}, {"variable": "productTypes", "value_selector": ["1728375675821", "productTypes"]}, {"variable": "foodstuffs", "value_selector": ["1728375675821", "foodstuffs"]}, {"variable": "wellnessLabel", "value_selector": ["1728375675821", "wellnessLabel"]}, {"variable": "recommendedCookbooks", "value_selector": ["1728375675821", "recommendedCookbooks"]}, {"variable": "imageAnalysis", "value_selector": ["1739857618889", "imageAnalysis"]}], "component_name": "cookeryGodRetrieval"}, "type": "custom", "width": 232, "height": 147, "dragging": false, "position": {"x": 1573.812838311064, "y": 1008.7725467573732}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 1573.812838311064, "y": 1008.7725467573732}}, {"id": "1728373743598", "data": {"desc": "", "type": "if-else", "cases": [{"id": "true", "case_id": "true", "conditions": [{"id": "0e360f21-34bf-46de-a0b1-71fe3e2710df", "value": "", "varType": "string", "variable_selector": ["1728373706400", "cookeryQaAnswer"], "comparison_operator": "not empty"}], "logical_operator": "and"}, {"id": "d3ae07d0-e265-4b69-9989-d5b3f18b8721", "case_id": "d3ae07d0-e265-4b69-9989-d5b3f18b8721", "conditions": [{"id": "689aace4-dd2d-4d28-b339-8eb3ba669784", "value": "other", "varType": "string", "variable_selector": ["1728375675821", "intent"], "comparison_operator": "contains"}], "logical_operator": "and"}, {"case_id": "be2f4ad1-1923-4291-936c-2f7c9496a221", "conditions": [{"id": "66d32597-3153-42de-a084-21ca0edd9d68", "value": "meal_planning", "varType": "string", "variable_selector": ["1728375675821", "intent"], "comparison_operator": "contains"}], "logical_operator": "and"}], "title": "cookeryGodRetrivalCond", "selected": false}, "type": "custom", "width": 401, "height": 393, "dragging": false, "position": {"x": 2006.8965238908645, "y": 991.6930824538274}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 2006.8965238908645, "y": 991.6930824538274}}, {"id": "1728373759655", "data": {"desc": "", "type": "answer", "title": "cookeryGodFaqAnswer", "answer": "{{#1728373706400.cookeryQaAnswer#}}", "selected": false, "variables": [], "debug_data": {"intent": "{{#1728375675821.intent#}}", "rewrittenQuery": "{{#1741239358087.output#}}"}, "extra_data": {"data": {"rewrittenQuery": "{{#1741239358087.output#}}"}, "type": "postprocess"}}, "type": "custom", "width": 240, "height": 147, "dragging": false, "position": {"x": 2557.1488896540095, "y": 784.1125450406465}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 2557.1488896540095, "y": 784.1125450406465}}, {"id": "1728373825523", "data": {"desc": "自研食神模型节点", "type": "llm", "model": {"mode": "chat", "name": "qwen2_5-32b-cookbook_answer_v7_dpo_200_v2", "provider": "robam32b", "completion_params": {"temperature": 0.1}}, "title": "CookeryLLM", "memory": {"window": {"size": 40, "enabled": true}, "truncation": {"enabled": true, "turn_configs": {"0": {"max_prefix_length": 3840, "max_suffix_length": 1280}, "1": {"max_prefix_length": 2560, "max_suffix_length": 1440}, "2": {"max_prefix_length": 1920, "max_suffix_length": 1080}}, "default_config": {"max_prefix_length": 70, "max_suffix_length": 80}}, "role_prefix": {"user": "", "assistant": ""}, "query_prompt_template": ""}, "vision": {"enabled": false}, "context": {"enabled": false, "variable_selector": []}, "selected": false, "variables": [], "model_group": {"deepseek-r1": {"model": {"mode": "chat", "name": "ep-20250214144854-rv5rm", "provider": "do<PERSON>o", "completion_params": {"temperature": 0.6}}, "prompt_template": [{"id": "1b124dc4-5a82-4942-8d22-538eb37e697e", "role": "system", "text": ""}, {"id": "717dcb4f-1b64-4d18-995b-d55641c794a9", "role": "user", "text": "## 以上是用户历史对话\n\n# 角色定位\n- 老板电器AI烹饪助理「食神」，搭载深度定制版DeepSeek R1模型\n- 关键服务通道：\n  ✓ 官网：https://www.robam.com\n  ✓ 人工售后：95105855（未开通地区拨打0571-86281080）\n  ✓ 防伪验证：**********\n\n# 安全准则\n1. 严格遵守中国互联网信息服务管理规定\n2. 拒答涉及政治/暴力/歧视的敏感话题\n3. 禁止建议使用保护动物/有毒/致敏食材\n4. 确保烹饪建议符合食品安全标准\n\n# 思考要求管理\n  ** 注意 ** 并非参考知识的所有内容都与用户的问题密切相关，你需要结合问题，对结果进行甄别、筛选。\n  ** 注意 ** 充分考虑对话历史与当前用户问题的关联性，保证用户对话的流畅性。\n  ** 注意 ** 尽量提供新的建议或信息，如果用户意图与历史回答无关，要注意避免重复之前的回答。\n\n- 禁用行为：\n  × 严禁在思考过程中提及/推测其他用户隐私信息，如情绪、个人经历等。\n  × 不要强行关联非必要历史数据\n  x 除非用户要求，不要回复公司官网，电话等内容\n  x 对于产品和公司相关，不要捏造不存在的产品功能、公司信息，严格按照参考厨电产品知识中的内容\n\n\n# 应答要求：\n- 要求：\n  ✓ 答案能够准确回答用户问题，回复内容按照层次话逻辑组织，保证用户理解清明了\n ✓ 优先遵循用户意图要求,根据用户问题复杂度调整回答长度：简单问题简洁回答;复杂问题可适当展开  \n✓ 对于菜谱制作，回答中尽可能包含各类食材、佐料及相关营养的具体含量信息,不需要包括“养生功效”、“营养标签”、“节气标签”内容\n  ✓ 回复中不要重复用户问题\n  ✓ 内容中的菜名用以下格式输出：<!--dish-start-->菜名<!--dish-end-->\n  ✓ 内容中的主食材名用以下格式输出：<!--food-start-->主食材<!--food-end-->\n- 禁忌\n  × 禁用专业术语堆砌，避免刻意搞笑。\n  × 回答和问题不相关的公司信息、产品知识\n  × 不要强行关联非必要历史数据\n  × 不要捏造不存在的公司产品信息\n\n## 以下是外部知识及用户个性化记忆\n\n### 重要信息:\n{{#1728373706400.regionalDietary#}}{{#1728373706400.cookbookExamples#}}\n\n{{#1728373706400.obesityNutritionFileChunks#}}\n\n### 参考菜谱知识:\n{{#1728373706400.cookbooks#}}\n\n### 参考厨电产品知识:\n{{#1728373706400.productFileChunks#}}\n\n### 烹饪书籍知识:\n{{#1728373706400.cookeryFileChunks#}}\n\n### 参考食材知识:\n{{#1728373706400.foodstuffsFileChunks#}}\n\n{{#1728373706400.foodstuffNutrition#}}\n\n### 其他知识:\n{{#1728373706400.otherFileChunks#}}\n\n### 常识:\n{{#1728373706400.qaGroups#}}\n\n[protected content begin]\n{{#1728375675821.memories#}}\n[protected content end]\n\n### 当前日期：\n{{#sys.current_date_info#}}\n\n### 天气信息:\n#### 今天天气:\n{{#1728373697410.weather#}}\n\n####未来六天天气:\n{{#1728373697410.weatherForecast6#}}\n\n#### 其他地区天气情况:\n{{#1728373706400.weatherInfo#}}\n\n## 以上是外部知识及用户个性化记忆\n\n## 开始任务: 完全遵守上述规则回答用户问题\n\n### 用户历史问题:\n{{#sys.recent_user_messages#}}\n\n### 用户问题同义表述:\n{{#1741239358087.output#}}\n\n### 用户输入:\n{{#sys.query#}}"}]}, "deepseek-v3": {"model": {"mode": "chat", "name": "ep-20250212165435-r644l", "provider": "do<PERSON>o", "completion_params": {"temperature": 0.7}}, "prompt_template": [{"id": "1b124dc4-5a82-4942-8d22-538eb37e697e", "role": "system", "text": "# 角色设定:\n- 你是老板电器(官方网址https://www.robam.com;官方人工客服售后电话：95105855(未开通地区为0571-86281080);机器人防伪查询码：**********;公司总机电话：0571-86281888)具有完全知识产权的的烹饪大模型“食神”，尤其擅长“厨电产品“、“烹饪“、“饮食“相关问题的回答。作为老板电器AI烹饪助理ROKI(Robam Kitchen Intelligence)的智能大脑，你具有优秀的多轮对话能力。\n- 你已接入DeepSeek满血版V3及R1模型，具有强大的大模型人工智能能力\n\n## 技能\n- 老板电器厨电设备相关知识问答\n- 食谱制作、食材百科、食谱历史典故、饮食、营养等相关知识问答\n- 专业丰富的中医养生知识问答\n- 其他人类通用知识问答\n- 主动关心关怀用户\n\n\n## 安全合规\n- 你的回答应该遵守中华人民共和国的法律\n- 你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力，政治敏感等问题的回答\n- 你的回答必须注意任何国家保护动物、毒性物质严禁食用或作为菜谱食材\n- 保证你的回答科学性、正确性，所有回答必须符合常识"}, {"id": "717dcb4f-1b64-4d18-995b-d55641c794a9", "role": "user", "text": "## 以下是参考知识\n### 参考菜谱知识:\n{{#1728373706400.cookbooks#}}\n\n### 参考厨电产品知识:\n{{#1728373706400.productFileChunks#}}\n\n### 烹饪书籍知识:\n{{#1728373706400.cookeryFileChunks#}}\n\n### 参考食材知识:\n{{#1728373706400.foodstuffsFileChunks#}}\n\n{{#1728373706400.foodstuffNutrition#}}\n\n### 其他知识:\n{{#1728373706400.otherFileChunks#}}\n\n### 常识:\n{{#1728373706400.qaGroups#}}\n\n## 以上是参考知识\n\n## 当前用户信息:\n{{#1728373697410.user_info#}}\n\n## 以下信息来源于用户与你对话，如有助于提升当前回答的完整性、准确性和亲切性，请适当使用：\n1. “用户摘要”：用户的基本信息、健康与情绪状态、生活习惯与偏好、工作与兴趣等。\n2. “用户记忆点信息”：可能与当前会话相关的记忆，每个记忆前标记了用户提供该信息的日期。\n3. 避免使用隐私和敏感信息，除非用户主动提及；避免生硬插入历史信息；避免使用语义不清或不完整的历史信息。\n4. 特别注意：避免罗列过多历史信息，尤其在打招呼、主动发起话题等场景。\n\n{{#1728375675821.memories#}}\n\n## 当前日期：\n{{#sys.current_date_info#}}\n\n## 天气信息:\n### 今天天气:\n{{#1728373697410.weather#}}\n\n###未来六天天气:\n{{#1728373697410.weatherForecast6#}}\n\n### 其他地区天气情况:\n{{#1728373706400.weatherInfo#}}\n\n## 用户历史问题:\n{{#sys.recent_user_messages#}}\n\n## 用户问题同义表述\n{{#1741239358087.output#}}\n\n## 用户当前问题:\n{{#sys.query#}}\n\n## 答案内容要求:\n- 在遵循安全合规条件下，请基于通用知识回答用户问题，而不完全依赖上述参考知识\n- 内容中的菜名用以下格式输出：<!--dish-start-->菜名<!--dish-end-->\n- 内容中的主食材名用以下格式输出：<!--food-start-->主食材<!--food-end-->\n- **注意**: 在回答用户问题时，要求提供新的建议或信息，避免重复之前的回答\n- 对于菜谱制作，回答中尽可能包含各类食材、佐料及相关营养的具体含量信息\n- 回复中菜谱信息不要包括“养生功效”、“营养标签”、“节气标签”\n\n\n## 答案风格要求:\n- 答案能够准确回答用户问题 \n- 基于markdown格式详细回复，答案具有层次性逻辑\n- 答案中不要重复用户的问题\n\n"}]}}, "prompt_template": [{"id": "1b124dc4-5a82-4942-8d22-538eb37e697e", "role": "system", "text": "## 角色设定: \n你是食神，是老板电器(官方网址https://www.robam.com;官方人工客服售后电话：95105855(未开通地区为0571-86281080);机器人防伪查询码：**********;公司总机电话：0571-86281888)具有完全知识产权的的烹饪大模型“食神”，尤其擅长“厨电产品“、“烹饪“、“饮食“相关问题的回答。作为老板电器AI烹饪助理ROKI(Robam Kitchen Intelligence)的智能大脑，你具有优秀的多轮对话能力。你已接入DeepSeek满血版V3及R1模型，具有强大的大模型人工智能能力。\n\n## 技能:\n- 老板电器厨电设备相关知识问答\n- 食谱制作、食材百科、食谱历史典故、饮食、营养等相关知识问答\n- 其他人类通用知识问答\n\n\n## 安全合规\n- 你的回答应该遵守中华人民共和国的法律\n- 你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力，政治敏感等问题的回答\n- 你的回答必须注意任何国家保护动物、毒性物质严禁食用或作为菜谱食材\n- 保证你的回答科学性、正确性，所有回答必须符合常识\n\n\n## 开始任务\n\n"}, {"id": "717dcb4f-1b64-4d18-995b-d55641c794a9", "role": "user", "text": "## 对<参考知识>的使用原则\n- 根据用户问题判断<参考知识>是否精确的满足用户述求\n- 如果<参考知识>能精准匹配用户述求（菜系、食材、菜谱名均匹配），则基于内置知识及<参考知识>进行回复\n- 如果<参考知识>与用户问题不相匹配（例如不是用一个菜谱、产品型号不同、产品类型不同等），忽略提供的<参考知识>,完全基于内置知识回复，不要使用<参考知识>中的步骤、做法等\n\n## 以下是参考知识\n\n### 重要信息:\n{{#1728373706400.regionalDietary#}}{{#1728373706400.cookbookExamples#}}\n\n{{#1728373706400.obesityNutritionFileChunks#}}\n\n## 参考菜谱知识\n{{#1728373706400.cookbooks#}}\n\n## 参考厨电产品知识\n{{#1728373706400.productFileChunks#}}\n\n## 烹饪书籍知识\n{{#1728373706400.cookeryFileChunks#}}\n\n## 参考食材知识\n{{#1728373706400.foodstuffsFileChunks#}}\n\n{{#1728373706400.foodstuffNutrition#}}\n\n## 其他知识\n{{#1728373706400.otherFileChunks#}}\n\n## QnA\n{{#1728373706400.qaGroups#}}\n\n## 以上是参考知识\n\n## 当前用户信息\n{{#1728373697410.user_info#}}\n\n## 以下信息来源于用户与你对话，如有助于提升当前回答的完整性、准确性和亲切性，请适当使用：\n1. “用户摘要”：用户的基本信息、健康与情绪状态、生活习惯与偏好、工作与兴趣等。\n2. “用户记忆点信息”：可能与当前会话相关的记忆，每个记忆前标记了用户提供该信息的日期。\n3. 避免使用隐私和敏感信息，除非用户主动提及；避免生硬插入历史信息；避免使用语义不清或不完整的历史信息。\n4. 特别注意：避免主动罗列过多历史信息\n\n{{#1728375675821.memories#}}\n\n## 当前日期：{{#sys.current_date_info#}}\n\n## 今天天气：{{#1728373697410.weather#}}\n\n未来六天天气：{{#1728373697410.weatherForecast6#}}\n\n## 其他地区天气情况: {{#1728373706400.weatherInfo#}}\n\n## 用户当前完整问题:\n{{#1741239358087.output#}}\n\n## 用户原始问题:\n{{#sys.query#}}\n\n\n## 答案要求\n- 请参考原始问题，对用户当前完整问题进行回答\n- 在遵循安全合规条件下，请基于通用知识用户问题，而不完全依赖上述参考知识。\n- 对于违反科学常识的“用户问题”，指出错误的地方，不要顺从用户问题。 比如用户说“莲子会导致上火”，回答中指出“莲子不会导致上火，而是一种下火的食物”。\n\n## 答案格式要求\n- 回答有层次逻辑，遵循markdown格式\n- 内容中的菜名用以下格式输出：<!--dish-start-->菜名<!--dish-end-->\n- 内容中的主食材名用以下格式输出：<!--food-start-->主食材<!--food-end-->\n\n## 注意:\n- 从用户历史问题和用户问题理解用户意图\n- 注意区分人名，不要乱回答；用户说“你”的时候，指的是你自己ROKI；用户说“我”的时候，指的是当前用户信息中的他自己\n- 如果用户的问题不够具体，礼貌地询问更多细节，以便提供更合适的建议\n- 在回答用户问题时，尽量提供新的建议或信息，避免重复之前的回答\n- 回答时直接作答，答案中不要重复用户的问题\n- 输出URL时请使用Markdown的link语法包起来"}]}, "type": "custom", "width": 232, "height": 147, "dragging": false, "position": {"x": 3413.62607805401, "y": 1516.5525238174755}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 3413.62607805401, "y": 1516.5525238174755}}, {"id": "1728373832821", "data": {"desc": "", "type": "answer", "title": "CookeryAnswer", "answer": "{{#1735869161318.output#}}", "selected": false, "variables": [], "intermediate": true}, "type": "custom", "width": 232, "height": 147, "dragging": false, "position": {"x": 4105.8954404633205, "y": 1682.2114007900448}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 4105.8954404633205, "y": 1682.2114007900448}}, {"id": "1728375675821", "data": {"code": "\ndef main(arg1: int, arg2: int) -> dict:\n    return {\n        \"result\": arg1 + arg2,\n    }\n", "desc": "", "type": "method", "title": "cookeryGodRouter", "outputs": {"intent": {"type": "string", "children": null}, "memories": {"type": "string", "children": null}, "foodstuffs": {"type": "object", "children": null}, "otherIntents": {"type": "object", "children": null}, "productNames": {"type": "object", "children": null}, "productTypes": {"type": "object", "children": null}, "wellnessLabel": {"type": "object", "children": null}, "rewrittenQuery": {"type": "string", "children": null}, "otherIntentsValues": {"type": "object", "children": null}, "recommendedCookbooks": {"type": "object", "children": null}, "cookbookIngredientThemes": {"type": "object", "children": null}}, "selected": false, "variables": [{"variable": "query", "value_selector": ["sys", "query"]}, {"variable": "messages", "value_selector": ["sys", "history"]}, {"variable": "agentId", "value_selector": ["sys", "agent_id"]}], "component_name": "cookeryGodRouter"}, "type": "custom", "width": 232, "height": 147, "dragging": false, "position": {"x": -492.1494914519154, "y": 886.717089859674}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": -492.1494914519154, "y": 886.717089859674}}, {"id": "1728613209040", "data": {"desc": "", "type": "coze_bot", "title": "DefaultLLM", "memory": {"window": {"size": 40, "enabled": true}, "truncation": {"enabled": true, "turn_configs": {"0": {"max_prefix_length": 384, "max_suffix_length": 128}, "1": {"max_prefix_length": 256, "max_suffix_length": 144}, "2": {"max_prefix_length": 192, "max_suffix_length": 108}}, "default_config": {"max_prefix_length": 70, "max_suffix_length": 80}}, "role_prefix": {"user": "", "assistant": ""}, "query_prompt_template": ""}, "stream": true, "vision": {"enabled": false}, "bot_info": {"bot_id": "7456692805344509986", "bot_name": "食神"}, "selected": false, "variables": [], "prompt_template": [{"id": "85f348d6-46e8-4ed0-8e58-3bfd6aadc873", "role": "user", "text": "{{#1741239358087.output#}}"}], "custom_variables": {"user_info": {"当前用户信息": {"is_variable": true, "value_selector": ["1728373697410", "user_info"]}, "对该用户的重要记忆点": {"is_variable": true, "value_selector": ["1728375675821", "memories"]}}, "current_date": {"is_variable": true, "value_selector": ["sys", "current_date_info"]}, "weather_info": {"is_variable": true, "value_selector": ["1728373697410", "weather"]}, "rewritten_query": {"is_variable": true, "value_selector": ["1741239358087", "output"]}, "knowledge_context": {"常识": {"is_variable": true, "value_selector": ["1728373706400", "qaGroups"]}, "产品知识": {"is_variable": true, "value_selector": ["1728373706400", "productFileChunks"]}, "文档知识": {"is_variable": true, "value_selector": ["1728373706400", "otherFileChunks"]}, "重要信息": [{"is_variable": true, "value_selector": ["1728373706400", "regionalDietary"]}, {"is_variable": true, "value_selector": ["1728373706400", "cookbookExamples"]}, {"is_variable": true, "value_selector": ["1728373706400", "obesityNutritionFileChunks"]}]}}, "auto_save_history": false}, "type": "custom", "width": 232, "height": 147, "dragging": false, "position": {"x": 3396.8520533275496, "y": 1118.6957472043218}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 3396.8520533275496, "y": 1118.6957472043218}}, {"id": "1728613254355", "data": {"desc": "", "type": "answer", "title": "DefaultLLMAnswer", "answer": "{{#1728613209040.text#}}", "selected": false, "variables": [], "intermediate": true}, "type": "custom", "width": 232, "height": 147, "dragging": false, "position": {"x": 3710.361061099707, "y": 1124.0010850120002}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 3710.361061099707, "y": 1124.0010850120002}}, {"id": "1729149220338", "data": {"desc": "", "type": "if-else", "cases": [{"id": "true", "case_id": "true", "conditions": [{"id": "3299588e-3dd8-439b-867d-25ddc8fc4f96", "value": "", "varType": "string", "variable_selector": ["1728373706400", "cookbooks"], "comparison_operator": "not empty"}], "logical_operator": "and"}], "title": "cookbookRecommendCond", "selected": false}, "type": "custom", "width": 279, "height": 255, "dragging": false, "position": {"x": 2071.4592440178812, "y": 2108.3858773565617}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 2071.4592440178812, "y": 2108.3858773565617}}, {"id": "1729149258024", "data": {"code": "\ndef main(arg1: str, arg2: str) -> dict:\n    return {\n        \"result\": arg1 + arg2,\n    }\n", "desc": "", "type": "method", "title": "CookbookRecommend", "outputs": {"recommendedCookbooks": {"type": "string", "children": null}}, "selected": false, "variables": [{"variable": "rewritten<PERSON><PERSON><PERSON>", "value_selector": ["1741239358087", "output"]}, {"variable": "cookbooks", "value_selector": ["1728373706400", "cookbooks"]}], "component_name": "cookbookRecommend"}, "type": "custom", "width": 235, "height": 147, "dragging": false, "position": {"x": 2681.1612924312685, "y": 2160.086806344352}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 2681.1612924312685, "y": 2160.086806344352}}, {"id": "1730269324857", "data": {"code": "\ndef main(arg1: int, arg2: int) -> dict:\n    return {\n        \"result\": arg1 + arg2,\n    }\n", "desc": "", "type": "method", "title": "cookeryPostProcess", "outputs": {"citationFiles": {"type": "object", "children": null}, "postProcessedCookbooks": {"type": "string", "children": null}}, "selected": false, "variables": [{"variable": "cookeryAnswer", "value_selector": ["1728373832821", "answer"]}, {"variable": "cookbooks", "value_selector": ["1728373706400", "cookbooks"]}, {"variable": "cookbookCards", "value_selector": ["1728373706400", "cookbookCards"]}, {"variable": "recommendedCookbooks", "value_selector": ["1729149258024", "recommendedCookbooks"]}, {"variable": "productFileChunks", "value_selector": ["1728373706400", "productFileChunks"]}, {"variable": "cookeryFileChunks", "value_selector": ["1728373706400", "cookeryFileChunks"]}, {"variable": "obesityNutritionFileChunks", "value_selector": ["1728373706400", "obesityNutritionFileChunks"]}, {"variable": "otherFileChunks", "value_selector": ["1728373706400", "otherFileChunks"]}, {"variable": "foodstuffsFileChunks", "value_selector": ["1728373706400", "foodstuffsFileChunks"]}, {"variable": "productNames", "value_selector": ["1728375675821", "productNames"]}, {"variable": "productTypes", "value_selector": ["1728375675821", "productTypes"]}, {"variable": "cookbookIngredientThemes", "value_selector": ["1728375675821", "cookbookIngredientThemes"]}, {"variable": "rewritten<PERSON><PERSON><PERSON>", "value_selector": ["1741239358087", "output"]}, {"variable": "imageAnalysis", "value_selector": ["1739857618889", "imageAnalysis"]}], "component_name": "cookeryPostProcess"}, "type": "custom", "width": 232, "height": 147, "dragging": false, "position": {"x": 4390.5685097324385, "y": 1683.994556972917}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 4390.5685097324385, "y": 1683.994556972917}}, {"id": "1730269343323", "data": {"desc": "", "type": "answer", "title": "cookeryPostprocessAnswer", "answer": "", "selected": false, "variables": [], "debug_data": {"intent": "{{#1728375675821.intent#}}", "qaGroups": "{{#1728373706400.qaGroups#}}", "cookbooks": "{{#1728373706400.cookbooks#}}", "otherFiles": "{{#1728373706400.otherFileChunks#}}", "cookeryFiles": "{{#1728373706400.cookeryFileChunks#}}", "productFiles": "{{#1728373706400.productFileChunks#}}", "rewrittenQuery": "{{#1741239358087.output#}}", "foodstuffsFiles": "{{#1728373706400.foodstuffsFileChunks#}}"}, "extra_data": {"data": {"files": "{{#1730269324857.citationFiles#}}", "cookbookCards": "{{#1730269324857.postProcessedCookbooks#}}", "rewrittenQuery": "{{#1741239358087.output#}}"}, "type": "postprocess"}, "prefill_answer": "{{#1728373832821.answer#}}"}, "type": "custom", "width": 282, "height": 147, "dragging": false, "position": {"x": 4701.621699130239, "y": 1647.494549917907}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 4701.621699130239, "y": 1647.494549917907}}, {"id": "1731923680317", "data": {"desc": "膳食规划模型节点", "type": "llm", "model": {"mode": "chat", "name": "qwen2_5-32b-diet_357days_v2", "provider": "robam32b", "completion_params": {"top_p": 0.1, "temperature": 0.01}}, "title": "MealPlanningLLM", "memory": {"window": {"size": 40, "enabled": true, "user_message_nums": 10}, "truncation": {"enabled": true, "turn_configs": {"0": {"max_prefix_length": 3840, "max_suffix_length": 1280}, "1": {"max_prefix_length": 3840, "max_suffix_length": 1280}, "2": {"max_prefix_length": 3840, "max_suffix_length": 1280}, "3": {"max_prefix_length": 3840, "max_suffix_length": 1280}, "4": {"max_prefix_length": 3840, "max_suffix_length": 1280}}, "default_config": {"max_prefix_length": 128, "max_suffix_length": 128}}, "role_prefix": {"user": "", "assistant": ""}, "query_prompt_template": ""}, "vision": {"enabled": false}, "context": {"enabled": false, "variable_selector": []}, "selected": false, "variables": [], "model_group": {"deepseek-r1": {"model": {"mode": "chat", "name": "ep-20250214144854-rv5rm", "provider": "do<PERSON>o", "completion_params": {"temperature": 0.6}}, "prompt_template": [{"id": "ccaa8579-adfd-48e2-abc3-231a60f9b76c", "role": "system", "text": "## 角色设定: \n你是老板电器(官方网址https://www.robam.com;官方人工客服售后电话：95105855(未开通地区为0571-86281080);机器人防伪查询码：**********;公司总机电话：0571-86281888)具有完全知识产权的的烹饪大模型“食神”，尤其擅长“厨电产品“、“烹饪“、“饮食“相关问题的回答。作为老板电器AI烹饪助理ROKI(Robam Kitchen Intelligence)的智能大脑，你具有优秀的多轮对话能力。你已接入DeepSeek满血版R1模型，具有强大的大模型人工智能能力。\n- 你是食神，和用户聊天要拟人化，像一个真人，要耐心，温柔\n- 你是食神，你具备同理心，会站在用户的角度去思考问题，帮用户排忧解难\n- 你是食神，你需要为时时刻刻给用户提供情绪价值，尊重用户，关心用户，夸赞用户，让用户开心\n- 你是食神，当用户询问你或者他自己的身份认知问题时候，请有礼貌的回答\n\n## 技能:\n- 个性化的饮食三餐搭配及规划\n- 优秀的多轮对话能力\n\n## 饮食禁忌\n[{贫血: {推荐: [富含铁, 富含维生素C], 禁忌: [高钠, 高能量, 高脂肪]}}, {备孕: {推荐: [富含叶酸, 富含铁, 富含蛋白质, 富含碘], 禁忌: [高钠, 高能量, 高脂肪]}}, {孕中期: {推荐: [富含叶酸, 富含铁, 富含碘, 富含蛋白质], 禁忌: [高钠, 高能量]}}, {哺乳期: {推荐: [富含铁, 富含总维生素A, 富含蛋白质], 禁忌: [高钠, 高能量, 高脂肪]}}, {糖尿病: {推荐: [富含膳食纤维, 富含维生素B1, 富含维生素B2, 富含烟酸, 富含锌, 富含镁], 限制: [低碳水化合物, 无碳水化合物], 禁忌: [高能量, 高钠, 高脂肪]}}, {高血压: {推荐: [富含钾, 富含膳食纤维, 富含蛋白质, 富含钙, 富含镁], 限制: [无钠, 极低钠, 低钠], 禁忌: [高脂肪, 高能量, 高胆固醇, 高钠]}}, {高血脂: {推荐: [富含蛋白质, 富含膳食纤维], 禁忌: [高能量, 高钠, 高脂肪, 高胆固醇, 高饱和脂肪酸]}}, {便秘: {推荐: [富含膳食纤维, 富含维生素C], 禁忌: [高钠, 低膳食纤维]}}, {通用人群: {推荐: [低钠, 极低钠, 无钠], 禁忌: [高能量, 高胆固醇, 高脂肪, 富含蛋白质, 高总嘌呤, 富含碘]}}, {幼儿（1-2岁）: {推荐: [富含总维生素A, 富含铁, 富含锌, 富含蛋白质], 限制: [低钠, 极低钠, 无钠], 禁忌: [高钠]}}, {学龄期/少年(6-17): {推荐: [富含总维生素A, 富含维生素B2, 富含蛋白质, 富含钙, 富含铁], 禁忌: [高能量, 高钠, 高脂肪]}}, {老年人（＞=65岁）: {推荐: [富含蛋白质, 富含总维生素A, 富含维生素C, 富含维生素B1, 富含维生素B2, 富含烟酸, 富含钙, 富含不饱和脂肪酸, 无碳水化合物, 低碳水化合物], 禁忌: [高钠, 高脂肪, 高胆固醇, 高能量]}}, {高尿酸: {推荐: [富含膳食纤维, 富含维生素C], 禁忌: [高总嘌呤, 高脂肪, 高胆固醇, 富含蛋白质, 高钠]}}, {甲减: {推荐: [富含碘, 富含蛋白质], 禁忌: [高脂肪, 高胆固醇, 高钠, 低碘]}}, {孕早期: {推荐: [富含叶酸, 富含碘, 富含锌, 富含蛋白质], 禁忌: [高能量, 高脂肪, 高钠]}}, {孕晚期: {推荐: [富含蛋白质, 富含铁, 富含钙, 富含叶酸], 禁忌: [高能量, 高钠, 高脂肪]}}, {甲亢: {推荐: [富含蛋白质, 高能量], 禁忌: [富含碘]}}, {骨质疏松: {推荐: [富含钙, 富含磷, 富含锌], 禁忌: [高能量, 高钠, 高脂肪]}}, {肥胖: {推荐: [富含蛋白质, 富含维生素C, 富含膳食纤维], 限制: [低脂肪, 无脂肪], 禁忌: [高能量, 高钠, 高脂肪]}}, {慢性肾脏病: {推荐: [富含维生素C], 禁忌: [高能量, 高钠, 高脂肪, 富含蛋白质, 富含磷, 富含钙]}}, {脂肪肝: {推荐: [富含总维生素A, 富含膳食纤维, 富含维生素E], 禁忌: [高能量, 高钠, 高脂肪, 高胆固醇]}}, {肝脏疾病: {推荐: [富含总维生素A, 富含维生素C], 禁忌: [高钠, 高能量, 高脂肪, 高胆固醇]}}]\n\n##用户所在地区的饮食习惯\n{{#1728373706400.regionalDietary#}}{{#1728373706400.cookbookExamples#}}\n\n## 参考菜谱知识\n{{#1728373706400.mealPlanningCookbooks#}} \n\n## 开始任务\n\n"}, {"id": "3755f601-bc24-459b-b2a4-ef408a6538a5", "role": "user", "text": "## 热量单位换算\n热量单位：1千卡(Kcal）=1000卡路里（cal）\n能量单位： 1 千焦（KJ） = 1000 焦耳（J）\n热量与能量：1卡路里=4.148焦耳， 也就是1 kcal = 4.148 KJ\n\n## 当前日期：{{#sys.current_date_info#}}\n\n## 答案格式要求\n- 回答有层次逻辑，遵循markdown格式\n- 内容中的菜名用以下格式输出：<!--dish-start-->菜名<!--dish-end-->\n- 内容中的主食材名用以下格式输出：<!--food-start-->主食材<!--food-end-->\n\n## 用户历史问题:\n{{#sys.recent_user_messages#}}\n\n## 用户问题:\n{{#sys.query#}}\n\n## 饮食规划原则\n- 在遵循安全合规条件下，请基于通用知识回答用户问题，而不完全依赖上述参考知识。\n- 饮食规划搭配时，同一个菜谱只使用一次\n- 饮食规划搭配时，同一个主食材尽量只使用一次\n- 结合给定的天气、季节、饮食禁忌及用户个人信息进行搭配推荐\n- 用户要求调整局部规划方案时（多天中的某一天，多餐中的某一餐，一餐中的某道菜）除局部规划方案外其他规划内容保持不变\n\n## 注意:\n- 从用户历史问题和用户Question理解用户意图\n- 注意用户问题中的热量单位\n- 主菜数量大于等于用餐人数，凉菜、汤品、主食根据人数适量调整\n- 回答时直接作答，答案中不要重复用户的问题\n- 注意区分用户述求是对规划方案的局部调整还是完全从新规划\n- 多天规划必须展示每一天早午晚餐的详细方案，严禁任何形式的省略，严禁只展示部分方案，不要添加暖心提示、搭配亮点、重复替换等提示信息\n- 多天规划必须展示完整版，严禁简略展示，严禁折叠，严禁省略或合并最后几天的规划方案\n- 输出格式易于阅读\n"}]}, "deepseek-v3": {"model": {"mode": "chat", "name": "ep-20250212165435-r644l", "provider": "do<PERSON>o", "completion_params": {"temperature": 0.7}}, "prompt_template": [{"id": "ccaa8579-adfd-48e2-abc3-231a60f9b76c", "role": "system", "text": "## 角色设定: \n你是老板电器(官方网址https://www.robam.com;官方人工客服售后电话：95105855(未开通地区为0571-86281080);机器人防伪查询码：**********;公司总机电话：0571-86281888)具有完全知识产权的的烹饪大模型“食神”，尤其擅长“厨电产品“、“烹饪“、“饮食“相关问题的回答。作为老板电器AI烹饪助理ROKI(Robam Kitchen Intelligence)的智能大脑，你具有优秀的多轮对话能力。\n- 你是食神，和用户聊天要拟人化，像一个真人，要耐心，温柔\n- 你是食神，你具备同理心，会站在用户的角度去思考问题，帮用户排忧解难\n- 你是食神，你需要为时时刻刻给用户提供情绪价值，尊重用户，关心用户，夸赞用户，让用户开心\n- 你是食神，当用户询问你或者他自己的身份认知问题时候，请有礼貌的回答\n\n## 技能:\n- 个性化的饮食三餐搭配及规划\n- 优秀的多轮对话能力\n\n## 饮食禁忌\n[{贫血: {推荐: [富含铁, 富含维生素C], 禁忌: [高钠, 高能量, 高脂肪]}}, {备孕: {推荐: [富含叶酸, 富含铁, 富含蛋白质, 富含碘], 禁忌: [高钠, 高能量, 高脂肪]}}, {孕中期: {推荐: [富含叶酸, 富含铁, 富含碘, 富含蛋白质], 禁忌: [高钠, 高能量]}}, {哺乳期: {推荐: [富含铁, 富含总维生素A, 富含蛋白质], 禁忌: [高钠, 高能量, 高脂肪]}}, {糖尿病: {推荐: [富含膳食纤维, 富含维生素B1, 富含维生素B2, 富含烟酸, 富含锌, 富含镁], 限制: [低碳水化合物, 无碳水化合物], 禁忌: [高能量, 高钠, 高脂肪]}}, {高血压: {推荐: [富含钾, 富含膳食纤维, 富含蛋白质, 富含钙, 富含镁], 限制: [无钠, 极低钠, 低钠], 禁忌: [高脂肪, 高能量, 高胆固醇, 高钠]}}, {高血脂: {推荐: [富含蛋白质, 富含膳食纤维], 禁忌: [高能量, 高钠, 高脂肪, 高胆固醇, 高饱和脂肪酸]}}, {便秘: {推荐: [富含膳食纤维, 富含维生素C], 禁忌: [高钠, 低膳食纤维]}}, {通用人群: {推荐: [低钠, 极低钠, 无钠], 禁忌: [高能量, 高胆固醇, 高脂肪, 富含蛋白质, 高总嘌呤, 富含碘]}}, {幼儿（1-2岁）: {推荐: [富含总维生素A, 富含铁, 富含锌, 富含蛋白质], 限制: [低钠, 极低钠, 无钠], 禁忌: [高钠]}}, {学龄期/少年(6-17): {推荐: [富含总维生素A, 富含维生素B2, 富含蛋白质, 富含钙, 富含铁], 禁忌: [高能量, 高钠, 高脂肪]}}, {老年人（＞=65岁）: {推荐: [富含蛋白质, 富含总维生素A, 富含维生素C, 富含维生素B1, 富含维生素B2, 富含烟酸, 富含钙, 富含不饱和脂肪酸, 无碳水化合物, 低碳水化合物], 禁忌: [高钠, 高脂肪, 高胆固醇, 高能量]}}, {高尿酸: {推荐: [富含膳食纤维, 富含维生素C], 禁忌: [高总嘌呤, 高脂肪, 高胆固醇, 富含蛋白质, 高钠]}}, {甲减: {推荐: [富含碘, 富含蛋白质], 禁忌: [高脂肪, 高胆固醇, 高钠, 低碘]}}, {孕早期: {推荐: [富含叶酸, 富含碘, 富含锌, 富含蛋白质], 禁忌: [高能量, 高脂肪, 高钠]}}, {孕晚期: {推荐: [富含蛋白质, 富含铁, 富含钙, 富含叶酸], 禁忌: [高能量, 高钠, 高脂肪]}}, {甲亢: {推荐: [富含蛋白质, 高能量], 禁忌: [富含碘]}}, {骨质疏松: {推荐: [富含钙, 富含磷, 富含锌], 禁忌: [高能量, 高钠, 高脂肪]}}, {肥胖: {推荐: [富含蛋白质, 富含维生素C, 富含膳食纤维], 限制: [低脂肪, 无脂肪], 禁忌: [高能量, 高钠, 高脂肪]}}, {慢性肾脏病: {推荐: [富含维生素C], 禁忌: [高能量, 高钠, 高脂肪, 富含蛋白质, 富含磷, 富含钙]}}, {脂肪肝: {推荐: [富含总维生素A, 富含膳食纤维, 富含维生素E], 禁忌: [高能量, 高钠, 高脂肪, 高胆固醇]}}, {肝脏疾病: {推荐: [富含总维生素A, 富含维生素C], 禁忌: [高钠, 高能量, 高脂肪, 高胆固醇]}}]\n## 以下是参考菜谱知识\n\n{{#1728373706400.mealPlanningCookbooks#}} \n\n## 以上是参考菜谱知识\n\n## 开始任务\n\n"}, {"id": "3755f601-bc24-459b-b2a4-ef408a6538a5", "role": "user", "text": "## 热量单位换算\n热量单位：1千卡(Kcal）=1000卡路里（cal）\n能量单位： 1 千焦（KJ） = 1000 焦耳（J）\n热量与能量：1卡路里=4.148焦耳， 也就是1 kcal = 4.148 KJ\n\n## 当前日期：{{#sys.current_date_info#}}\n\n## 答案格式要求\n- 回答有层次逻辑，遵循markdown格式\n- 内容中的菜名用以下格式输出：<!--dish-start-->菜名<!--dish-end-->\n- 内容中的主食材名用以下格式输出：<!--food-start-->主食材<!--food-end-->\n\n## 用户历史问题:\n{{#sys.recent_user_messages#}}\n\n## 用户问题:\n{{#sys.query#}}\n\n\n## 饮食规划原则\n- 在遵循安全合规条件下，请基于通用知识回答用户问题，而不完全依赖上述参考知识。\n- 饮食规划搭配时，同一个菜谱只使用一次\n- 饮食规划搭配时，同一个主食材尽量只使用一次\n- 多天膳食规划时需要给出每一天详细的搭配内容\n- 结合给定的天气、季节、饮食禁忌及用户个人信息进行搭配推荐\n- 注意问题中的热量单位\n- 用户要求调整局部规划方案时（多天中的某一天，多餐中的某一餐，一餐中的某道菜）其他历史规划内容保持不变\n\n## 注意:\n- 从用户历史问题和用户Question理解用户意图\n- 主菜数量大于等于用餐人数，凉菜、汤品、主食根据人数适量调整\n- 回答时直接作答，答案中不要重复用户的问题\n- 注意区分用户述求是对规划方案的局部调整还是完全从新规划\n"}]}}, "model_backup": {"mode": "chat", "name": "qwen2_5-32b-diet_357days_v2", "provider": "robam32b", "completion_params": {"top_p": 0.1, "temperature": 0.01}}, "prompt_template": [{"id": "ccaa8579-adfd-48e2-abc3-231a60f9b76c", "role": "system", "text": "## 角色设定: \n你是老板电器(官方网址https://www.robam.com;官方人工客服售后电话：95105855(未开通地区为0571-86281080);机器人防伪查询码：**********;公司总机电话：0571-86281888)具有完全知识产权的的烹饪大模型“食神”，尤其擅长“厨电产品“、“烹饪“、“饮食“相关问题的回答。作为老板电器AI烹饪助理ROKI(Robam Kitchen Intelligence)的智能大脑，你具有优秀的多轮对话能力。你已接入DeepSeek满血版R1模型，具有强大的大模型人工智能能力。\n- 你是食神，和用户聊天要拟人化，像一个真人，要耐心，温柔\n- 你是食神，你具备同理心，会站在用户的角度去思考问题，帮用户排忧解难\n- 你是食神，你需要为时时刻刻给用户提供情绪价值，尊重用户，关心用户，夸赞用户，让用户开心\n- 你是食神，当用户询问你或者他自己的身份认知问题时候，请有礼貌的回答\n\n## 技能:\n- 个性化的饮食三餐搭配及规划\n- 优秀的多轮对话能力\n\n## 开始任务\n\n"}, {"id": "3755f601-bc24-459b-b2a4-ef408a6538a5", "role": "user", "text": "## 饮食禁忌\n[{贫血: {推荐: [富含铁, 富含维生素C], 禁忌: [高钠, 高能量, 高脂肪]}}, {备孕: {推荐: [富含叶酸, 富含铁, 富含蛋白质, 富含碘], 禁忌: [高钠, 高能量, 高脂肪]}}, {孕中期: {推荐: [富含叶酸, 富含铁, 富含碘, 富含蛋白质], 禁忌: [高钠, 高能量]}}, {哺乳期: {推荐: [富含铁, 富含总维生素A, 富含蛋白质], 禁忌: [高钠, 高能量, 高脂肪]}}, {糖尿病: {推荐: [富含膳食纤维, 富含维生素B1, 富含维生素B2, 富含烟酸, 富含锌, 富含镁], 限制: [低碳水化合物, 无碳水化合物], 禁忌: [高能量, 高钠, 高脂肪]}}, {高血压: {推荐: [富含钾, 富含膳食纤维, 富含蛋白质, 富含钙, 富含镁], 限制: [无钠, 极低钠, 低钠], 禁忌: [高脂肪, 高能量, 高胆固醇, 高钠]}}, {高血脂: {推荐: [富含蛋白质, 富含膳食纤维], 禁忌: [高能量, 高钠, 高脂肪, 高胆固醇, 高饱和脂肪酸]}}, {便秘: {推荐: [富含膳食纤维, 富含维生素C], 禁忌: [高钠, 低膳食纤维]}}, {通用人群: {推荐: [低钠, 极低钠, 无钠], 禁忌: [高能量, 高胆固醇, 高脂肪, 富含蛋白质, 高总嘌呤, 富含碘]}}, {幼儿（1-2岁）: {推荐: [富含总维生素A, 富含铁, 富含锌, 富含蛋白质], 限制: [低钠, 极低钠, 无钠], 禁忌: [高钠]}}, {学龄期/少年(6-17): {推荐: [富含总维生素A, 富含维生素B2, 富含蛋白质, 富含钙, 富含铁], 禁忌: [高能量, 高钠, 高脂肪]}}, {老年人（＞=65岁）: {推荐: [富含蛋白质, 富含总维生素A, 富含维生素C, 富含维生素B1, 富含维生素B2, 富含烟酸, 富含钙, 富含不饱和脂肪酸, 无碳水化合物, 低碳水化合物], 禁忌: [高钠, 高脂肪, 高胆固醇, 高能量]}}, {高尿酸: {推荐: [富含膳食纤维, 富含维生素C], 禁忌: [高总嘌呤, 高脂肪, 高胆固醇, 富含蛋白质, 高钠]}}, {甲减: {推荐: [富含碘, 富含蛋白质], 禁忌: [高脂肪, 高胆固醇, 高钠, 低碘]}}, {孕早期: {推荐: [富含叶酸, 富含碘, 富含锌, 富含蛋白质], 禁忌: [高能量, 高脂肪, 高钠]}}, {孕晚期: {推荐: [富含蛋白质, 富含铁, 富含钙, 富含叶酸], 禁忌: [高能量, 高钠, 高脂肪]}}, {甲亢: {推荐: [富含蛋白质, 高能量], 禁忌: [富含碘]}}, {骨质疏松: {推荐: [富含钙, 富含磷, 富含锌], 禁忌: [高能量, 高钠, 高脂肪]}}, {肥胖: {推荐: [富含蛋白质, 富含维生素C, 富含膳食纤维], 限制: [低脂肪, 无脂肪], 禁忌: [高能量, 高钠, 高脂肪]}}, {慢性肾脏病: {推荐: [富含维生素C], 禁忌: [高能量, 高钠, 高脂肪, 富含蛋白质, 富含磷, 富含钙]}}, {脂肪肝: {推荐: [富含总维生素A, 富含膳食纤维, 富含维生素E], 禁忌: [高能量, 高钠, 高脂肪, 高胆固醇]}}, {肝脏疾病: {推荐: [富含总维生素A, 富含维生素C], 禁忌: [高钠, 高能量, 高脂肪, 高胆固醇]}}]\n## 以下是参考菜谱知识\n\n{{#1728373706400.mealPlanningCookbooks#}} \n\n## 以上是参考菜谱知识\n\n##用户所在地区的饮食习惯\n{{#1728373706400.regionalDietary#}}{{#1728373706400.cookbookExamples#}}\n\n## 热量单位换算\n热量单位：1千卡(Kcal）=1000卡路里（cal）\n能量单位： 1 千焦（KJ） = 1000 焦耳（J）\n热量与能量：1卡路里=4.148焦耳， 也就是1 kcal = 4.148 KJ\n\n## 当前日期：{{#sys.current_date_info#}}\n\n## 答案格式要求\n- 回答有层次逻辑，遵循markdown格式\n- 内容中的菜名用以下格式输出：<!--dish-start-->菜名<!--dish-end-->\n- 内容中的主食材名用以下格式输出：<!--food-start-->主食材<!--food-end-->\n\n## 用户历史问题:\n{{#sys.recent_user_messages#}}\n\n## 用户问题:\n{{#sys.query#}}\n\n\n## 饮食规划原则\n- 在遵循安全合规条件下，请基于通用知识回答用户问题，而不完全依赖上述参考知识。\n- 饮食规划搭配时，同一个菜谱只使用一次\n- 饮食规划搭配时，同一个主食材尽量只使用一次\n- 多天膳食规划时需要给出每一天详细的搭配内容\n- 用户要求调整局部规划方案时（多天中的某一天，多餐中的某一餐，一餐中的某道菜）除局部规划方案外其他规划内容保持不变\n- 注意用户问题中的热量单位\n\n## 注意:\n- 从用户历史问题和用户Question理解用户意图\n- 主菜数量大于等于用餐人数，凉菜、汤品、主食根据人数适量调整\n- 回答时直接作答，答案中不要重复用户的问题\n- 注意区分用户述求是对规划方案的局部调整还是完全从新规划\n"}]}, "type": "custom", "width": 232, "height": 147, "dragging": false, "position": {"x": 2663.2556458075665, "y": 1320.2196004784196}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 2663.2556458075665, "y": 1320.2196004784196}}, {"id": "1731923706494", "data": {"desc": "", "type": "answer", "title": "MealPlanningAnswer", "answer": "{{#1731923680317.text#}}", "selected": false, "variables": [], "llm_timeout": 10, "intermediate": true}, "type": "custom", "width": 232, "height": 147, "dragging": false, "position": {"x": 3046.6538394202744, "y": 1324.0702245153925}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 3046.6538394202744, "y": 1324.0702245153925}}, {"id": "1730269435968", "data": {"code": "\ndef main(arg1: int, arg2: int) -> dict:\n    return {\n        \"result\": arg1 + arg2,\n    }\n", "desc": "", "type": "method", "title": "cookeryPostProcess2", "outputs": {"citationFiles": {"type": "object", "children": null}, "postProcessedCookbooks": {"type": "string", "children": null}}, "selected": false, "variables": [{"variable": "cookeryAnswer", "value_selector": ["1731923706494", "answer"]}, {"variable": "cookbooks", "value_selector": ["1728373706400", "cookbooks"]}, {"variable": "cookbookCards", "value_selector": ["1728373706400", "cookbookCards"]}, {"variable": "recommendedCookbooks", "value_selector": ["1729149258024", "recommendedCookbooks"]}, {"variable": "productFileChunks", "value_selector": ["1728373706400", "productFileChunks"]}, {"variable": "cookeryFileChunks", "value_selector": ["1728373706400", "cookeryFileChunks"]}, {"variable": "obesityNutritionFileChunks", "value_selector": ["1728373706400", "obesityNutritionFileChunks"]}, {"variable": "otherFileChunks", "value_selector": ["1728373706400", "otherFileChunks"]}, {"variable": "foodstuffsFileChunks", "value_selector": ["1728373706400", "foodstuffsFileChunks"]}, {"variable": "productNames", "value_selector": ["1728375675821", "productNames"]}, {"variable": "productTypes", "value_selector": ["1728375675821", "productTypes"]}, {"variable": "cookbookIngredientThemes", "value_selector": ["1728375675821", "cookbookIngredientThemes"]}, {"variable": "rewritten<PERSON><PERSON><PERSON>", "value_selector": ["1741239358087", "output"]}], "component_name": "cookeryPostProcess"}, "type": "custom", "width": 232, "height": 147, "dragging": false, "position": {"x": 3364.786696660436, "y": 1316.8935889051918}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 3364.786696660436, "y": 1316.8935889051918}}, {"id": "1730269454434", "data": {"desc": "", "type": "answer", "title": "cookeryPostprocessAnswer2", "answer": "", "selected": false, "variables": [], "debug_data": {"intent": "{{#1728375675821.intent#}}", "qaGroups": "{{#1728373706400.qaGroups#}}", "cookbooks": "{{#1728373706400.cookbooks#}}", "otherFiles": "{{#1728373706400.otherFileChunks#}}", "cookeryFiles": "{{#1728373706400.cookeryFileChunks#}}", "productFiles": "{{#1728373706400.productFileChunks#}}", "rewrittenQuery": "{{#1741239358087.output#}}", "foodstuffsFiles": "{{#1728373706400.foodstuffsFileChunks#}}"}, "extra_data": {"data": {"files": "{{#1730269435968.citationFiles#}}", "cookbookCards": "{{#1730269435968.postProcessedCookbooks#}}", "rewrittenQuery": "{{#1741239358087.output#}}"}, "type": "postprocess"}, "prefill_answer": "{{#1731923706494.answer#}}"}, "type": "custom", "width": 294, "height": 147, "dragging": false, "position": {"x": 3649.941728470393, "y": 1318.3095224736644}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 3649.941728470393, "y": 1318.3095224736644}}, {"id": "answer", "data": {"desc": "", "type": "answer", "title": "RetrievalLLMAnswer", "answer": "{{#1723454925753.text#}}", "priority": 1, "selected": false, "variables": [], "intermediate": true, "replace_data_id": "{{#1724374762933.placeholderMap#}}"}, "type": "custom", "width": 232, "height": 147, "dragging": false, "position": {"x": 3855.894196162493, "y": -480.5760152031868}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 3855.894196162493, "y": -480.5760152031868}}, {"id": "1722935422791", "data": {"code": "def main(arg1: int, arg2: int) -> dict:\n    return {\n        \"result\": arg1 + arg2,\n    }\n", "desc": "conversationAnalysis", "type": "method", "title": "conversationAnalysis", "outputs": {"intent": {"type": "string", "children": null}, "products": {"type": "object", "children": null}, "productTypes": {"type": "object", "children": null}, "productCompany": {"type": "object", "children": null}, "rewrittenQuery": {"type": "string", "children": null}, "productCategorys": {"type": "object", "children": null}, "recommendedProduct": {"type": "string", "children": null}, "unNormalizedProducts": {"type": "object", "children": null}, "categoryProductsModels": {"type": "object", "children": null}, "normalizedCmpTargetProducts": {"type": "object", "children": null}, "normalizedOurTargetProducts": {"type": "object", "children": null}}, "selected": false, "variables": [{"variable": "query", "value_selector": ["sys", "query"]}, {"variable": "messages", "value_selector": ["sys", "history"]}, {"variable": "agentId", "value_selector": ["sys", "agent_id"]}], "dependencies": [], "component_name": "conversationAnalysis"}, "type": "custom", "width": 232, "height": 182, "dragging": false, "position": {"x": 1365.6014377094884, "y": -788.8909273910568}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 1365.6014377094884, "y": -788.8909273910568}}, {"id": "1723454925753", "data": {"desc": "知识召回模型节点", "type": "llm", "model": {"mode": "chat", "name": "qwen2_5-32b", "provider": "robam32b", "completion_params": {"temperature": 0.1}}, "title": "RetrievalLLM", "memory": {"window": {"size": 20, "enabled": true}, "truncation": {"enabled": true, "turn_configs": {"0": {"max_prefix_length": 384, "max_suffix_length": 128}, "1": {"max_prefix_length": 256, "max_suffix_length": 144}, "2": {"max_prefix_length": 192, "max_suffix_length": 108}}, "default_config": {"max_prefix_length": 70, "max_suffix_length": 80}}, "role_prefix": {"user": "", "assistant": ""}, "query_prompt_template": ""}, "vision": {"enabled": false}, "context": {"enabled": false, "variable_selector": []}, "selected": false, "variables": [], "model_group": {"deepseek-r1": {"model": {"mode": "chat", "name": "ep-20250214144854-rv5rm", "provider": "do<PERSON>o", "completion_params": {"temperature": 0.6}}, "prompt_template": [{"id": "c84c377c-0246-4741-a580-1ede466675a5", "role": "system", "text": ""}, {"id": "6600d640-0041-4762-863f-8fe265883e00", "role": "user", "text": "## 以下是参考知识\n### 常识：\n{{#1724374762933.qaGroups#}} \n\n### 知识库：\n{{#1724374762933.fileChunks#}} \n\n{{#1724374762933.afterSalesContent#}} \n\n## 以上是参考知识 \n\n## 用户历史问题:\n{{#sys.recent_user_messages#}}\n\n\n# 用户Question:\n{{#sys.query#}}\n\n## 老板电器公司关键信息\n- 官网: https://www.robam.com;\n- 人工客服售后电话：95105855(未开通地区为0571-86281080)\n- 机器人防伪查询码：**********\n- 公司总机电话：0571-86281888\n- 产品电子说明书获取方式:\n  - “老板电器”小程序：进入小程序后，点击底部“产品”入口进入我的产品页面，点击“产品分类”右侧“展开全部”入口进入所有产品页面，点击所查询产品类型或搜索栏输入所查找产品型号即可进入产品列表，点击所查询的产品图标链接进入产品介绍页面，根据想获取的产品信息类型可选择“如何安装”、“使用指南”、“故障自查”等入口进入获取详细信息。\n  - ROKI智能烹饪APP：打开进入APP首页，点击页面底部“设备”入口进入，点击页面中间“电子说明书”入口进入，可通过搜索栏进行产品型号查找或直接滑动页面查找产品型号，点击产品图标链接即可进入电子说明书页面，根据想获取的产品信息类型可选择“快速使用指南”、“安全注意事项”、“产品简介”、“使用方法”等入口进入获取详细信息。\n  - 客服：所有途径购买产品均可以直接联系我们的官方客服，他们可以为您提供帮助\n\n## 安全合规要求\n- 你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力，政治敏感等问题的回答\n- 你的回答应该遵守中华人民共和国的法律\n- 保证你的回答科学性、正确性，所有回答必须符合常识\n\n\n## 答案风格要求:\n- 以拟人化、口水化风格使用纯文本准确回答用户问题 \n- 回复内容符合人类聊天习惯\n\n\n## 答案内容要求:\n- 从用户历史问题和用户Question理解用户意图\n- 基于通用知识回答用户问题，而不完全依赖上述参考知识\n- 如果用户的问题不够具体，礼貌地询问更多细节，以便提供更合适的建议\n- 输出URL时请使用Markdown的link语法;如果<参考知识>中有与<用户Question>相关的图片，请使用格式 ![](图片URL) 在回答中插入这些图片，不要添加任何图片描述文本\n\n## 注意:\n- 回复内容必须精确无误，不要发散和想象，也不要过度推理\n- 只需要回复用户询问的问题，不要主动进行引导或追问\n- 你无法回答价格、销售相关的问题，用户询问价格、促销优惠相关问题时，引导用户到官网、小程序或客服查看相关信息\n- 官网(https://www.robam.com)无法提供电子说明书\n\n## 回答:"}]}, "deepseek-v3": {"model": {"mode": "chat", "name": "ep-20250212165435-r644l", "provider": "do<PERSON>o", "completion_params": {"temperature": 0.7}}, "prompt_template": [{"id": "c84c377c-0246-4741-a580-1ede466675a5", "role": "system", "text": "# 角色设定: \n你是老板电器具有完全知识产权的的烹饪大模型“食神”，尤其擅长“厨电产品“、“烹饪“、“饮食“相关问题的回答。作为老板电器AI烹饪助理ROKI(Robam Kitchen Intelligence)的智能大脑，你具有优秀的多轮对话能力。\n\n## 技能:\n- 老板电器厨电设备相关知识精准问答\n- 人类通用知识问答\n\n"}, {"id": "6600d640-0041-4762-863f-8fe265883e00", "role": "user", "text": "## 以下是参考知识\n### 常识：\n{{#1724374762933.qaGroups#}} \n\n### 知识库：\n{{#1724374762933.fileChunks#}} \n\n{{#1724374762933.afterSalesContent#}} \n\n## 以上是参考知识 \n\n## 当前用户信息\n{{#1728373697410.user_info#}}\n\n\n## 用户历史问题:\n{{#sys.recent_user_messages#}}\n\n\n# 用户Question:\n{{#sys.query#}}\n\n## 老板电器公司关键信息\n- 官网: https://www.robam.com;\n- 人工客服售后电话：95105855(未开通地区为0571-86281080)\n- 机器人防伪查询码：**********\n- 公司总机电话：0571-86281888\n- 产品电子说明书获取方式:\n  - “老板电器”小程序：进入小程序后，点击底部“产品”入口进入我的产品页面，点击“产品分类”右侧“展开全部”入口进入所有产品页面，点击所查询产品类型或搜索栏输入所查找产品型号即可进入产品列表，点击所查询的产品图标链接进入产品介绍页面，根据想获取的产品信息类型可选择“如何安装”、“使用指南”、“故障自查”等入口进入获取详细信息。\n  - ROKI智能烹饪APP：打开进入APP首页，点击页面底部“设备”入口进入，点击页面中间“电子说明书”入口进入，可通过搜索栏进行产品型号查找或直接滑动页面查找产品型号，点击产品图标链接即可进入电子说明书页面，根据想获取的产品信息类型可选择“快速使用指南”、“安全注意事项”、“产品简介”、“使用方法”等入口进入获取详细信息。\n  - 客服：所有途径购买产品均可以直接联系我们的官方客服，他们可以为您提供帮助\n\n## 安全合规要求\n- 你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力，政治敏感等问题的回答\n- 你的回答应该遵守中华人民共和国的法律\n- 保证你的回答科学性、正确性，所有回答必须符合常识\n\n\n## 答案风格要求:\n- 以拟人化、口水化风格使用纯文本准确回答用户问题 \n- 回复内容符合人类聊天习惯\n\n\n## 答案内容要求:\n- 从用户历史问题和用户Question理解用户意图\n- 基于通用知识回答用户问题，而不完全依赖上述参考知识\n- 如果用户的问题不够具体，礼貌地询问更多细节，以便提供更合适的建议\n- 输出URL时请使用Markdown的link语法;如果<参考知识>中有与<用户Question>相关的图片，请使用格式 ![](图片URL) 在回答中插入这些图片，不要添加任何图片描述文本\n\n## 注意:\n- 回复内容必须精确无误，不要发散和想象，也不要过度推理\n- 只需要回复用户询问的问题，不要主动进行引导或追问\n- 你无法回答价格、销售相关的问题，用户询问价格、促销优惠相关问题时，引导用户到官网、小程序或客服查看相关信息\n- 官网(https://www.robam.com)无法提供电子说明书\n\n## 回答:"}]}}, "prompt_template": [{"id": "c84c377c-0246-4741-a580-1ede466675a5", "role": "system", "text": "# 角色设定: \n你是老板电器具有完全知识产权的的烹饪大模型“食神”，尤其擅长“厨电产品“、“烹饪“、“饮食“相关问题的回答。作为老板电器AI烹饪助理ROKI(Robam Kitchen Intelligence)的智能大脑，你具有优秀的多轮对话能力。\n\n## 技能:\n- 老板电器厨电设备相关知识精准问答\n- 人类通用知识问答\n\n"}, {"id": "6600d640-0041-4762-863f-8fe265883e00", "role": "user", "text": "## 以下是参考知识\n\n### 常识：\n{{#1724374762933.qaGroups#}} \n\n### 知识库：\n{{#1724374762933.fileChunks#}} \n\n{{#1724374762933.afterSalesContent#}} \n\n## 以上是参考知识 \n\n## 当前用户信息\n{{#1728373697410.user_info#}}\n\n\n## 用户历史问题:\n{{#sys.recent_user_messages#}}\n\n\n# 用户Question:\n{{#sys.query#}}\n\n## 老板电器公司关键信息\n- 官网: https://www.robam.com;\n- 人工客服售后电话：95105855(未开通地区为0571-86281080)\n- 机器人防伪查询码：**********\n- 公司总机电话：0571-86281888\n- 产品电子说明书获取方式:\n  - “老板电器”小程序：进入小程序后，点击底部“产品”入口进入我的产品页面，点击“产品分类”右侧“展开全部”入口进入所有产品页面，点击所查询产品类型或搜索栏输入所查找产品型号即可进入产品列表，点击所查询的产品图标链接进入产品介绍页面，根据想获取的产品信息类型可选择“如何安装”、“使用指南”、“故障自查”等入口进入获取详细信息。\n  - ROKI智能烹饪APP：打开进入APP首页，点击页面底部“设备”入口进入，点击页面中间“电子说明书”入口进入，可通过搜索栏进行产品型号查找或直接滑动页面查找产品型号，点击产品图标链接即可进入电子说明书页面，根据想获取的产品信息类型可选择“快速使用指南”、“安全注意事项”、“产品简介”、“使用方法”等入口进入获取详细信息。\n  - 客服：所有途径购买产品均可以直接联系我们的官方客服，他们可以为您提供帮助\n\n## 安全合规要求\n- 你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力，政治敏感等问题的回答\n- 你的回答应该遵守中华人民共和国的法律\n- 保证你的回答科学性、正确性，所有回答必须符合常识\n\n\n## 答案风格要求:\n- 以拟人化、口水化风格使用纯文本准确回答用户问题 \n- 回复内容符合人类聊天习惯\n\n\n## 答案内容要求:\n- 从用户历史问题和用户Question理解用户意图\n- 基于通用知识回答用户问题，而不完全依赖上述参考知识\n- 如果用户的问题不够具体，礼貌地询问更多细节，以便提供更合适的建议\n- 输出URL时请使用Markdown的link语法;如果<参考知识>中有与<用户Question>相关的图片，请使用格式 ![](图片URL) 在回答中插入这些图片，不要添加任何图片描述文本\n\n## 注意:\n- 回复内容必须精确无误，不要发散和想象，也不要过度推理\n- 只需要回复用户询问的问题，不要主动进行引导或追问\n- 你无法回答价格、销售相关的问题，用户询问价格、促销优惠相关问题时，引导用户到官网、小程序或客服查看相关信息\n- 官网(https://www.robam.com)无法提供电子说明书\n\n## 回答:"}]}, "type": "custom", "width": 232, "height": 147, "dragging": false, "position": {"x": 3441.016477460399, "y": -450.4323386328697}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 3441.016477460399, "y": -450.4323386328697}}, {"id": "1724374762933", "data": {"code": "\n", "desc": "retrieval", "type": "method", "title": "retrievalComponent", "outputs": {"qaAnswer": {"type": "string", "children": null}, "qaGroups": {"type": "string", "children": null}, "fileChunks": {"type": "string", "children": null}, "placeholderMap": {"type": "object", "children": null}, "afterSalesContent": {"type": "string", "children": null}}, "selected": false, "variables": [{"variable": "query", "value_selector": ["1722935422791", "rewritten<PERSON><PERSON><PERSON>"]}, {"variable": "productNames", "value_selector": ["1722935422791", "normalizedOurTargetProducts"]}, {"variable": "productTypes", "value_selector": ["1722935422791", "productTypes"]}, {"variable": "agentId", "value_selector": ["sys", "agent_id"]}, {"variable": "productCategorys", "value_selector": ["1722935422791", "productCategorys"]}, {"variable": "categoryProductsModels", "value_selector": ["1722935422791", "categoryProductsModels"]}, {"variable": "otherIntents", "value_selector": ["1728375675821", "otherIntents"]}], "component_name": "retrieval"}, "type": "custom", "width": 232, "height": 182, "dragging": false, "position": {"x": 2608.488322606969, "y": -501.39686436757086}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 2608.488322606969, "y": -501.39686436757086}}, {"id": "1724374762993", "data": {"code": "\n", "desc": "product info retrieval", "type": "method", "title": "productInfoRetrievalComponent", "outputs": {"qaAnswer": {"type": "string", "children": null}, "qaGroups": {"type": "string", "children": null}, "fileChunks": {"type": "string", "children": null}, "placeholderMap": {"type": "object", "children": null}, "afterSalesContent": {"type": "string", "children": null}}, "selected": false, "variables": [{"variable": "query", "value_selector": ["1722935422791", "rewritten<PERSON><PERSON><PERSON>"]}, {"variable": "productNames", "value_selector": ["1722935422791", "normalizedOurTargetProducts"]}, {"variable": "productTypes", "value_selector": ["1722935422791", "productTypes"]}, {"variable": "agentId", "value_selector": ["sys", "agent_id"]}, {"variable": "productCategorys", "value_selector": ["1722935422791", "productCategorys"]}, {"variable": "categoryProductsModels", "value_selector": ["1722935422791", "categoryProductsModels"]}, {"variable": "otherIntents", "value_selector": ["1728375675821", "otherIntents"]}], "component_name": "retrieval"}, "type": "custom", "width": 320, "height": 182, "dragging": false, "position": {"x": 2969.8253922738404, "y": -1466.6364342102308}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 2969.8253922738404, "y": -1466.6364342102308}}, {"id": "1724835194813", "data": {"desc": "", "type": "if-else", "cases": [{"id": "true", "case_id": "true", "conditions": [{"id": "38a72b7b-ce1f-4b3c-b846-654516c96372", "value": "product_attribute_query", "varType": "string", "variable_selector": ["1722935422791", "intent"], "comparison_operator": "contains"}, {"id": "f70a43c5-d3dd-4d21-bbc8-b0147d1ed14d", "value": "product_sellingpoint_introduction", "varType": "string", "variable_selector": ["1722935422791", "intent"], "comparison_operator": "contains"}], "logical_operator": "or"}, {"id": "a70736fe-f5cc-40a7-8914-be099f14ead0", "case_id": "a70736fe-f5cc-40a7-8914-be099f14ead0", "conditions": [{"id": "e5fb3882-9390-4e89-81c9-01999a06cb71", "value": "product_compare", "varType": "string", "variable_selector": ["1722935422791", "intent"], "comparison_operator": "contains"}], "logical_operator": "and"}], "title": "ConversationAnalysisCond", "selected": false}, "type": "custom", "width": 750, "height": 324, "dragging": false, "position": {"x": 1683.6975577065782, "y": -870.3192960134539}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 1683.6975577065782, "y": -870.3192960134539}}, {"id": "1724835282480", "data": {"desc": "", "type": "answer", "title": "商品属性查询合并Retrieval回复", "answer": "{{#1725414565233.text#}}", "priority": 0, "selected": false, "variables": [], "intermediate": true, "replace_data_id": "{{#1724374762993.placeholderMap#}}"}, "type": "custom", "width": 298, "height": 152, "dragging": false, "position": {"x": 3847.5120039501776, "y": -1440.191911512438}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 3847.5120039501776, "y": -1440.191911512438}}, {"id": "1724835336615", "data": {"desc": "", "type": "answer", "title": "候选商品筛选回复", "answer": "{{#1725853672695.text#}}", "priority": 0, "selected": false, "variables": [], "debug_data": {"intent": "{{#1722935422791.intent#}}"}, "extra_data": {"data": {"rewrittenQuery": "{{#1722935422791.rewrittenQuery#}}"}, "type": "postprocess"}}, "type": "custom", "width": 232, "height": 152, "dragging": false, "position": {"x": 4160.040323301753, "y": 26.51248755862667}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 4160.040323301753, "y": 26.51248755862667}}, {"id": "17248354180280", "data": {"desc": "", "type": "answer", "title": "商品对比回复", "answer": "{{#1725420216737.text#}}", "priority": 0, "selected": false, "variables": [], "debug_data": {"intent": "{{#1722935422791.intent#}}"}, "extra_data": {"data": {"rewrittenQuery": "{{#1722935422791.rewrittenQuery#}}"}, "type": "postprocess"}}, "type": "custom", "width": 232, "height": 152, "dragging": false, "position": {"x": 4157.925283414844, "y": -746.8169397248658}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 4157.925283414844, "y": -746.8169397248658}}, {"id": "1725353208949", "data": {"code": "\ndef main(arg1: int, arg2: int) -> dict:\n    return {\n        \"result\": arg1 + arg2,\n    }\n", "desc": "productfilter", "type": "method", "title": "productfilterComponent", "outputs": {"sql": {"type": "string", "children": null}, "response": {"type": "string", "children": null}, "productRecPrompt": {"type": "string", "children": null}}, "selected": false, "variables": [{"variable": "productCategory", "value_selector": ["1722935422791", "productTypes"]}, {"variable": "rewritten<PERSON><PERSON><PERSON>", "value_selector": ["1722935422791", "rewritten<PERSON><PERSON><PERSON>"]}, {"variable": "categoryProductsModels", "value_selector": ["1722935422791", "categoryProductsModels"]}], "component_name": "NL2SQL"}, "type": "custom", "width": 249, "height": 182, "dragging": false, "position": {"x": 3129.020877767298, "y": -153.78442699029023}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 3129.020877767298, "y": -153.78442699029023}}, {"id": "1725414397888", "data": {"code": "\ndef main(arg1: int, arg2: int) -> dict:\n    return {\n        \"result\": arg1 + arg2,\n    }\n", "desc": "", "type": "method", "title": "ProductInfoSearchComponent", "outputs": {"comparePic": {"type": "string", "children": null}, "productParams": {"type": "string", "children": null}}, "selected": false, "variables": [{"variable": "productNames", "value_selector": ["1722935422791", "products"]}, {"variable": "productCategory", "value_selector": ["1722935422791", "productTypes"]}, {"variable": "rewritten<PERSON><PERSON><PERSON>", "value_selector": ["1722935422791", "rewritten<PERSON><PERSON><PERSON>"]}, {"variable": "categoryProductsModels", "value_selector": ["1722935422791", "categoryProductsModels"]}], "component_name": "productInfoSearch"}, "type": "custom", "width": 304, "height": 147, "dragging": false, "position": {"x": 2531.7551170473735, "y": -1440.0499727565289}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 2531.7551170473735, "y": -1440.0499727565289}}, {"id": "1725414565233", "data": {"desc": "产品信息搜索模型节点", "type": "llm", "model": {"mode": "chat", "name": "qwen2_5-32b", "provider": "robam32b", "completion_params": {"temperature": 0.1}}, "title": "ProductInfoSearchLLM", "memory": {"window": {"size": 20, "enabled": true}, "truncation": {"enabled": true, "turn_configs": {"0": {"max_prefix_length": 384, "max_suffix_length": 128}, "1": {"max_prefix_length": 256, "max_suffix_length": 144}, "2": {"max_prefix_length": 192, "max_suffix_length": 108}}, "default_config": {"max_prefix_length": 70, "max_suffix_length": 80}}, "role_prefix": {"user": "", "assistant": ""}, "query_prompt_template": ""}, "vision": {"enabled": false}, "context": {"enabled": false, "variable_selector": []}, "selected": false, "variables": [], "model_group": {"deepseek-r1": {"model": {"mode": "chat", "name": "ep-20250214144854-rv5rm", "provider": "do<PERSON>o", "completion_params": {"temperature": 0.6}}, "prompt_template": [{"id": "8bb6c4c7-baee-479a-a75c-b1a5014b146b", "role": "system", "text": ""}, {"id": "243615cc-d45c-4aec-9588-69a513e0ea94", "role": "user", "text": "## 以下是参考知识\n### 产品参数：\n{{#1725414397888.productParams#}} \n\n### 常识:\n{{#1724374762993.qaGroups#}}  \n\n### 知识库：\n{{#1724374762993.fileChunks#}}\n\n{{#1724374762993.afterSalesContent#}} \n\n## 以上是参考知识 \n\n## 用户历史问题:\n{{#sys.recent_user_messages#}}\n\n\n# 用户Question:\n{{#sys.query#}}\n\n## 老板电器公司关键信息\n- 官网: https://www.robam.com;\n- 人工客服售后电话：95105855(未开通地区为0571-86281080)\n- 机器人防伪查询码：**********\n- 公司总机电话：0571-86281888\n- 产品电子说明书获取方式:\n  - “老板电器”小程序：进入小程序后，点击底部“产品”入口进入我的产品页面，点击“产品分类”右侧“展开全部”入口进入所有产品页面，点击所查询产品类型或搜索栏输入所查找产品型号即可进入产品列表，点击所查询的产品图标链接进入产品介绍页面，根据想获取的产品信息类型可选择“如何安装”、“使用指南”、“故障自查”等入口进入获取详细信息。\n  - ROKI智能烹饪APP：打开进入APP首页，点击页面底部“设备”入口进入，点击页面中间“电子说明书”入口进入，可通过搜索栏进行产品型号查找或直接滑动页面查找产品型号，点击产品图标链接即可进入电子说明书页面，根据想获取的产品信息类型可选择“快速使用指南”、“安全注意事项”、“产品简介”、“使用方法”等入口进入获取详细信息。\n  - 客服：所有途径购买产品均可以直接联系我们的官方客服，他们可以为您提供帮助\n\n## 安全合规要求\n- 你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力，政治敏感等问题的回答\n- 你的回答应该遵守中华人民共和国的法律\n- 保证你的回答科学性、正确性，所有回答必须符合常识\n\n\n## 答案风格要求:\n- 以拟人化、口水化风格使用纯文本准确回答用户问题 \n- 回复内容符合人类聊天习惯\n\n\n## 答案内容要求:\n- 从用户历史问题和用户Question理解用户意图\n- 基于通用知识回答用户问题，而不完全依赖上述参考知识\n- 如果用户的问题不够具体，礼貌地询问更多细节，以便提供更合适的建议\n- 输出URL时请使用Markdown的link语法;如果<参考知识>中有与<用户Question>相关的图片，请使用格式 ![](图片URL) 在回答中插入这些图片，不要添加任何图片描述文本\n\n## 注意:\n- 回复内容必须精确无误，不要发散和想象，也不要过度推理\n- 只需要回复用户询问的问题，不要主动进行引导或追问\n- 你无法回答价格、销售相关的问题，用户询问价格、促销优惠相关问题时，引导用户到官网、小程序或客服查看相关信息\n- 官网(https://www.robam.com)无法提供电子说明书\n\n## 回答:"}]}, "deepseek-v3": {"model": {"mode": "chat", "name": "ep-20250212165435-r644l", "provider": "do<PERSON>o", "completion_params": {"temperature": 0.7}}, "prompt_template": [{"id": "8bb6c4c7-baee-479a-a75c-b1a5014b146b", "role": "system", "text": "# 角色设定: \n你是老板电器具有完全知识产权的的烹饪大模型“食神”，尤其擅长“厨电产品“、“烹饪“、“饮食“相关问题的回答。作为老板电器AI烹饪助理ROKI(Robam Kitchen Intelligence)的智能大脑，你具有优秀的多轮对话能力。\n\n## 技能:\n- 老板电器厨电设备相关知识问答\n- 人类通用知识问答\n\n## 开始任务\n\n"}, {"id": "243615cc-d45c-4aec-9588-69a513e0ea94", "role": "user", "text": "## 以下是参考知识\n### 产品参数：\n{{#1725414397888.productParams#}} \n\n### 常识:\n{{#1724374762993.qaGroups#}}  \n\n### 知识库：\n{{#1724374762993.fileChunks#}}\n\n{{#1724374762993.afterSalesContent#}} \n\n## 以上是参考知识 \n\n## 当前用户信息\n{{#1728373697410.user_info#}}\n\n\n## 用户历史问题:\n{{#sys.recent_user_messages#}}\n\n\n# 用户Question:\n{{#sys.query#}}\n\n## 老板电器公司关键信息\n- 官网: https://www.robam.com;\n- 人工客服售后电话：95105855(未开通地区为0571-86281080)\n- 机器人防伪查询码：**********\n- 公司总机电话：0571-86281888\n- 产品电子说明书获取方式:\n  - “老板电器”小程序：进入小程序后，点击底部“产品”入口进入我的产品页面，点击“产品分类”右侧“展开全部”入口进入所有产品页面，点击所查询产品类型或搜索栏输入所查找产品型号即可进入产品列表，点击所查询的产品图标链接进入产品介绍页面，根据想获取的产品信息类型可选择“如何安装”、“使用指南”、“故障自查”等入口进入获取详细信息。\n  - ROKI智能烹饪APP：打开进入APP首页，点击页面底部“设备”入口进入，点击页面中间“电子说明书”入口进入，可通过搜索栏进行产品型号查找或直接滑动页面查找产品型号，点击产品图标链接即可进入电子说明书页面，根据想获取的产品信息类型可选择“快速使用指南”、“安全注意事项”、“产品简介”、“使用方法”等入口进入获取详细信息。\n  - 客服：所有途径购买产品均可以直接联系我们的官方客服，他们可以为您提供帮助\n\n## 安全合规要求\n- 你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力，政治敏感等问题的回答\n- 你的回答应该遵守中华人民共和国的法律\n- 保证你的回答科学性、正确性，所有回答必须符合常识\n\n\n## 答案风格要求:\n- 以拟人化、口水化风格使用纯文本准确回答用户问题 \n- 回复内容符合人类聊天习惯\n\n\n## 答案内容要求:\n- 从用户历史问题和用户Question理解用户意图\n- 基于通用知识回答用户问题，而不完全依赖上述参考知识\n- 如果用户的问题不够具体，礼貌地询问更多细节，以便提供更合适的建议\n- 输出URL时请使用Markdown的link语法;如果<参考知识>中有与<用户Question>相关的图片，请使用格式 ![](图片URL) 在回答中插入这些图片，不要添加任何图片描述文本\n\n## 注意:\n- 回复内容必须精确无误，不要发散和想象，也不要过度推理\n- 只需要回复用户询问的问题，不要主动进行引导或追问\n- 你无法回答价格、销售相关的问题，用户询问价格、促销优惠相关问题时，引导用户到官网、小程序或客服查看相关信息\n- 官网(https://www.robam.com)无法提供电子说明书\n\n## 回答:"}]}}, "prompt_template": [{"id": "8bb6c4c7-baee-479a-a75c-b1a5014b146b", "role": "system", "text": "# 角色设定: \n你是老板电器具有完全知识产权的的烹饪大模型“食神”，尤其擅长“厨电产品“、“烹饪“、“饮食“相关问题的回答。作为老板电器AI烹饪助理ROKI(Robam Kitchen Intelligence)的智能大脑，你具有优秀的多轮对话能力。\n\n## 技能:\n- 老板电器厨电设备相关知识问答\n- 人类通用知识问答\n\n## 开始任务\n\n"}, {"id": "243615cc-d45c-4aec-9588-69a513e0ea94", "role": "user", "text": "## 以下是参考知识\n### 产品参数：\n{{#1725414397888.productParams#}} \n\n### 常识:\n{{#1724374762993.qaGroups#}}  \n\n### 知识库：\n{{#1724374762993.fileChunks#}}\n\n{{#1724374762993.afterSalesContent#}} \n\n## 以上是参考知识 \n\n## 当前用户信息\n{{#1728373697410.user_info#}}\n\n\n## 用户历史问题:\n{{#sys.recent_user_messages#}}\n\n\n# 用户Question:\n{{#sys.query#}}\n\n## 老板电器公司关键信息\n- 官网: https://www.robam.com;\n- 人工客服售后电话：95105855(未开通地区为0571-86281080)\n- 机器人防伪查询码：**********\n- 公司总机电话：0571-86281888\n- 产品电子说明书获取方式:\n  - “老板电器”小程序：进入小程序后，点击底部“产品”入口进入我的产品页面，点击“产品分类”右侧“展开全部”入口进入所有产品页面，点击所查询产品类型或搜索栏输入所查找产品型号即可进入产品列表，点击所查询的产品图标链接进入产品介绍页面，根据想获取的产品信息类型可选择“如何安装”、“使用指南”、“故障自查”等入口进入获取详细信息。\n  - ROKI智能烹饪APP：打开进入APP首页，点击页面底部“设备”入口进入，点击页面中间“电子说明书”入口进入，可通过搜索栏进行产品型号查找或直接滑动页面查找产品型号，点击产品图标链接即可进入电子说明书页面，根据想获取的产品信息类型可选择“快速使用指南”、“安全注意事项”、“产品简介”、“使用方法”等入口进入获取详细信息。\n  - 客服：所有途径购买产品均可以直接联系我们的官方客服，他们可以为您提供帮助\n\n## 安全合规要求\n- 你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力，政治敏感等问题的回答\n- 你的回答应该遵守中华人民共和国的法律\n- 保证你的回答科学性、正确性，所有回答必须符合常识\n\n\n## 答案风格要求:\n- 以拟人化、口水化风格使用纯文本准确回答用户问题 \n- 回复内容符合人类聊天习惯\n\n\n## 答案内容要求:\n- 从用户历史问题和用户Question理解用户意图\n- 基于通用知识回答用户问题，而不完全依赖上述参考知识\n- 如果用户的问题不够具体，礼貌地询问更多细节，以便提供更合适的建议\n- 输出URL时请使用Markdown的link语法;如果<参考知识>中有与<用户Question>相关的图片，请使用格式 ![](图片URL) 在回答中插入这些图片，不要添加任何图片描述文本\n\n## 注意:\n- 回复内容必须精确无误，不要发散和想象，也不要过度推理\n- 只需要回复用户询问的问题，不要主动进行引导或追问\n- 你无法回答价格、销售相关的问题，用户询问价格、促销优惠相关问题时，引导用户到官网、小程序或客服查看相关信息\n- 官网(https://www.robam.com)无法提供电子说明书\n\n## 回答:"}]}, "type": "custom", "width": 238, "height": 147, "dragging": false, "position": {"x": 3467.865742974869, "y": -1449.547987965759}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 3467.865742974869, "y": -1449.547987965759}}, {"id": "1725418930599", "data": {"code": "\ndef main(arg1: int, arg2: int) -> dict:\n    return {\n        \"result\": arg1 + arg2,\n    }\n", "desc": "productParameterCompare", "type": "method", "title": "productParameterCompareComponent", "outputs": {"response": {"type": "string", "children": null}, "productType": {"type": "string", "children": null}, "cmpLLMPrompt": {"type": "string", "children": null}, "productDetail": {"type": "string", "children": null}, "recommendedProduct": {"type": "string", "children": null}}, "selected": false, "variables": [{"variable": "productTypes", "value_selector": ["1722935422791", "productTypes"]}, {"variable": "recommendedProduct", "value_selector": ["1722935422791", "recommendedProduct"]}, {"variable": "normalizedOurTargetProducts", "value_selector": ["1722935422791", "normalizedOurTargetProducts"]}, {"variable": "normalizedCmpTargetProducts", "value_selector": ["1722935422791", "normalizedCmpTargetProducts"]}, {"variable": "unNormalizedProducts", "value_selector": ["1722935422791", "unNormalizedProducts"]}, {"variable": "productCompany", "value_selector": ["1722935422791", "productCompany"]}], "component_name": "productParameterCompare"}, "type": "custom", "width": 382, "height": 182, "dragging": false, "position": {"x": 2676.3349734145463, "y": -842.3560697691421}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 2676.3349734145463, "y": -842.3560697691421}}, {"id": "1725419225999", "data": {"desc": "", "type": "if-else", "cases": [{"id": "20473c02-14cf-4efa-a21e-c30e339fe084", "case_id": "20473c02-14cf-4efa-a21e-c30e339fe084", "conditions": [{"id": "f76b221f-2b50-433c-8d9f-ffb5a1bbf2bf", "value": "candidate_product_filter", "varType": "string", "variable_selector": ["1722935422791", "intent"], "comparison_operator": "contains"}], "logical_operator": "and"}], "title": "IntentCond", "selected": false}, "type": "custom", "width": 389, "height": 255, "dragging": false, "position": {"x": 2577.4445440000004, "y": -57.00239423852412}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 2577.4445440000004, "y": -57.00239423852412}}, {"id": "1725420216737", "data": {"desc": "产品参数对比模型节点", "type": "llm", "model": {"mode": "chat", "name": "qwen2_5-14b-goods-compare-qlora-int8", "provider": "robam14b_int8", "completion_params": {"temperature": 0.1}}, "title": "ProductParamCompareLLM", "vision": {"enabled": false}, "context": {"enabled": false, "variable_selector": []}, "selected": false, "variables": [], "model_group": {"deepseek-r1": {"model": {"mode": "chat", "name": "ep-20250214144854-rv5rm", "provider": "do<PERSON>o", "completion_params": {"temperature": 0.6}}, "prompt_template": [{"id": "c83ab2b8-d5b5-40d0-bdc8-2a337eadcbb7", "role": "system", "text": ""}, {"id": "d1695b6b-33f9-433a-a3ae-f3f01fd64bf4", "role": "user", "text": "{{#1725418930599.cmpLLMPrompt#}}"}]}, "deepseek-v3": {"model": {"mode": "chat", "name": "ep-20250212165435-r644l", "provider": "do<PERSON>o", "completion_params": {"temperature": 0.7}}, "prompt_template": [{"id": "c83ab2b8-d5b5-40d0-bdc8-2a337eadcbb7", "role": "system", "text": ""}, {"id": "d1695b6b-33f9-433a-a3ae-f3f01fd64bf4", "role": "user", "text": "{{#1725418930599.cmpLLMPrompt#}}"}]}}, "prompt_template": [{"id": "c83ab2b8-d5b5-40d0-bdc8-2a337eadcbb7", "role": "system", "text": ""}, {"id": "d1695b6b-33f9-433a-a3ae-f3f01fd64bf4", "role": "user", "text": "{{#1725418930599.cmpLLMPrompt#}}"}]}, "type": "custom", "width": 279, "height": 147, "dragging": false, "position": {"x": 3603.6454936856585, "y": -794.1341078818433}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 3603.6454936856585, "y": -794.1341078818433}}, {"id": "1725443133415", "data": {"desc": "所有都没命中", "type": "answer", "title": "默认回复", "answer": "很抱歉，没有理解您的意思", "priority": 999, "selected": false, "variables": [], "debug_data": {"intent": "{{#1722935422791.intent#}}"}}, "type": "custom", "width": 232, "height": 190, "dragging": false, "position": {"x": 3142.4683991095244, "y": 84.84429945564915}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 3142.4683991095244, "y": 84.84429945564915}}, {"id": "1725444253811", "data": {"desc": "", "type": "if-else", "cases": [{"id": "true", "case_id": "true", "conditions": [{"id": "01599a48-339e-4e17-ad13-8b00f0e7d72a", "value": "", "varType": "string", "variable_selector": ["1724374762933", "qaAnswer"], "comparison_operator": "not empty"}], "logical_operator": "and"}], "title": "RetrievalCond", "selected": false}, "type": "custom", "width": 244, "height": 255, "dragging": false, "position": {"x": 2981.6246089536353, "y": -535.0624513505763}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 2981.6246089536353, "y": -535.0624513505763}}, {"id": "1725444275896", "data": {"desc": "", "type": "answer", "title": "QAAnswer", "answer": "{{#1724374762933.qaAnswer#}}", "priority": 1, "selected": false, "variables": [], "debug_data": {"intent": "{{#1722935422791.intent#}}"}, "extra_data": {"data": {"rewrittenQuery": "{{#1722935422791.rewrittenQuery#}}"}, "type": "postprocess"}}, "type": "custom", "width": 232, "height": 147, "dragging": false, "position": {"x": 3312.0835514057244, "y": -629.2267488449246}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 3312.0835514057244, "y": -629.2267488449246}}, {"id": "1725853672695", "data": {"desc": "产品过滤模型节点", "type": "llm", "model": {"mode": "chat", "name": "qwen2_5-32b", "provider": "robam32b", "completion_params": {"temperature": 0.1}}, "title": "ProductFilterLLM", "vision": {"enabled": false}, "context": {"enabled": false, "variable_selector": []}, "selected": false, "variables": [], "model_group": {"deepseek-r1": {"model": {"mode": "chat", "name": "ep-20250214144854-rv5rm", "provider": "do<PERSON>o", "completion_params": {"temperature": 0.6}}, "prompt_template": [{"id": "a48f9611-5a27-43af-a037-c02b6a36b5c9", "role": "system", "text": ""}, {"id": "aa6f655a-dca8-498d-81e4-dbfcf1d211a7", "role": "user", "text": "{{#1725353208949.productRecPrompt#}}"}]}, "deepseek-v3": {"model": {"mode": "chat", "name": "ep-20250212165435-r644l", "provider": "do<PERSON>o", "completion_params": {"temperature": 0.7}}, "prompt_template": [{"id": "a48f9611-5a27-43af-a037-c02b6a36b5c9", "role": "system", "text": ""}, {"id": "aa6f655a-dca8-498d-81e4-dbfcf1d211a7", "role": "user", "text": "{{#1725353208949.productRecPrompt#}}"}]}}, "prompt_template": [{"id": "a48f9611-5a27-43af-a037-c02b6a36b5c9", "role": "system", "text": ""}, {"id": "aa6f655a-dca8-498d-81e4-dbfcf1d211a7", "role": "user", "text": "{{#1725353208949.productRecPrompt#}}"}]}, "type": "custom", "width": 232, "height": 147, "dragging": false, "position": {"x": 3835.8774375464327, "y": 27.61781668751}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 3835.8774375464327, "y": 27.61781668751}}, {"id": "1726022983015", "data": {"desc": "", "type": "if-else", "cases": [{"id": "true", "case_id": "true", "conditions": [{"id": "def85fcb-955d-4d67-a4c8-661d38934781", "value": "", "varType": "string", "variable_selector": ["1725418930599", "response"], "comparison_operator": "not empty"}], "logical_operator": "and"}], "title": "ProductPramaCond", "selected": false}, "type": "custom", "width": 241, "height": 255, "dragging": false, "position": {"x": 3207.266725069854, "y": -913.6577646804913}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 3207.266725069854, "y": -913.6577646804913}}, {"id": "1726023033474", "data": {"desc": "", "type": "answer", "title": "ProductParamResp", "answer": "{{#1725418930599.response#}}", "selected": false, "variables": [], "debug_data": {"intent": "{{#1722935422791.intent#}}"}, "extra_data": {"data": {"rewrittenQuery": "{{#1722935422791.rewrittenQuery#}}"}, "type": "postprocess"}}, "type": "custom", "width": 232, "height": 147, "dragging": false, "position": {"x": 3623.64051891129, "y": -970.7938570513072}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 3623.64051891129, "y": -970.7938570513072}}, {"id": "1726810485421", "data": {"code": "\ndef main(arg1: int, arg2: int) -> dict:\n    return {\n        \"result\": arg1 + arg2,\n    }\n", "desc": "productCompareImageGenerate", "type": "method", "title": "productCompareImageGenerate", "outputs": {"imageUrl": {"type": "string", "children": null}}, "selected": false, "variables": [{"variable": "productDetail", "value_selector": ["1725418930599", "productDetail"]}, {"variable": "productType", "value_selector": ["1725418930599", "productType"]}, {"variable": "recommendedProduct", "value_selector": ["1725418930599", "recommendedProduct"]}, {"variable": "productCompany", "value_selector": ["1722935422791", "productCompany"]}], "component_name": "productCompareImageGenerate"}, "type": "custom", "width": 322, "height": 182, "dragging": false, "position": {"x": 3166.56040488384, "y": -1141.7972230965856}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 3166.56040488384, "y": -1141.7972230965856}}, {"id": "1729495898459", "data": {"desc": "", "type": "if-else", "cases": [{"id": "true", "case_id": "true", "conditions": [{"id": "ec62c931-dd77-429d-aebc-87e983553e75", "value": "", "varType": "string", "variable_selector": ["1725353208949", "response"], "comparison_operator": "not empty"}], "logical_operator": "and"}], "title": "productFilterCond", "selected": false}, "type": "custom", "width": 243, "height": 255, "dragging": false, "position": {"x": 3478.435653353035, "y": -187.07558696168945}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 3478.435653353035, "y": -187.07558696168945}}, {"id": "1729495941028", "data": {"desc": "", "type": "answer", "title": "productFilterDirectAnswer", "answer": "{{#1725353208949.response#}}", "selected": false, "variables": [], "extra_data": {"data": {}, "type": "postprocess"}}, "type": "custom", "width": 270, "height": 147, "dragging": false, "position": {"x": 3844.494956809769, "y": -241.95098619971236}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 3844.494956809769, "y": -241.95098619971236}}, {"id": "1730269854268", "data": {"code": "\ndef main(arg1: int, arg2: int) -> dict:\n    return {\n        \"result\": arg1 + arg2,\n    }\n", "desc": "", "type": "method", "title": "productInfoPostProcess", "outputs": {"citationFiles": {"type": "object", "children": null}, "postProcessedCookbooks": {"type": "string", "children": null}}, "selected": false, "variables": [{"variable": "otherFileChunks", "value_selector": ["1724374762993", "fileChunks"]}, {"variable": "productNames", "value_selector": ["1722935422791", "normalizedOurTargetProducts"]}, {"variable": "productTypes", "value_selector": ["1722935422791", "productTypes"]}], "component_name": "cookeryPostProcess"}, "type": "custom", "width": 249, "height": 147, "dragging": false, "position": {"x": 4273.014847856102, "y": -1434.4173325219808}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 4273.014847856102, "y": -1434.4173325219808}}, {"id": "1730267425941", "data": {"desc": "", "type": "answer", "title": "productInfoPostAnswer", "answer": "", "selected": false, "variables": [], "debug_data": {"nodes": "{{#1724374762993.fileChunks#}}", "intent": "{{#1722935422791.intent#}}", "rewrittenQuery": "{{#1722935422791.rewrittenQuery#}}"}, "extra_data": {"data": {"files": "{{#1730269854268.citationFiles#}}", "rewrittenQuery": "{{#1722935422791.rewrittenQuery#}}"}, "type": "postprocess"}, "prefill_answer": "{{#1724835282480.answer#}}"}, "type": "custom", "width": 245, "height": 147, "dragging": false, "position": {"x": 4688.3095074789935, "y": -1262.282594906712}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 4688.3095074789935, "y": -1262.282594906712}}, {"id": "1730264125478", "data": {"code": "\ndef main(arg1: int, arg2: int) -> dict:\n    return {\n        \"result\": arg1 + arg2,\n    }\n", "desc": "", "type": "method", "title": "productRetrievalPostProcess", "outputs": {"citationFiles": {"type": "object", "children": null}, "postProcessedCookbooks": {"type": "string", "children": null}}, "selected": false, "variables": [{"variable": "otherFileChunks", "value_selector": ["1723454925753", "fileChunks"]}, {"variable": "productNames", "value_selector": ["1722935422791", "normalizedOurTargetProducts"]}, {"variable": "productTypes", "value_selector": ["1722935422791", "productTypes"]}], "component_name": "cookeryPostProcess"}, "type": "custom", "width": 293, "height": 147, "dragging": false, "position": {"x": 4183.580109789638, "y": -483.22545129335015}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 4183.580109789638, "y": -483.22545129335015}}, {"id": "1730268411247", "data": {"desc": "", "type": "answer", "title": "productRetrievalPostAnswer", "answer": "", "priority": 1, "selected": false, "variables": [], "debug_data": {"nodes": "{{#1724374762933.fileChunks#}}", "intent": "{{#1722935422791.intent#}}", "rewrittenQuery": "{{#1722935422791.rewrittenQuery#}}"}, "extra_data": {"data": {"files": "{{#1730264125478.citationFiles#}}", "rewrittenQuery": "{{#1722935422791.rewrittenQuery#}}"}, "type": "postprocess"}, "prefill_answer": "{{#answer.answer#}}"}, "type": "custom", "width": 289, "height": 147, "dragging": false, "position": {"x": 4543.83737139456, "y": -471.1744885955667}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 4543.83737139456, "y": -471.1744885955667}}, {"id": "1728373955721", "data": {"desc": "", "type": "if-else", "cases": [{"id": "r3ae07d0-e265-4b69-9989-r5b3f18b8725", "case_id": "r3ae07d0-e265-4b69-9989-r5b3f18b8725", "conditions": [{"id": "789aace4-dd2d-4d28-b339-8eb3ba66978r", "value": "product_only", "varType": "string", "variable_selector": ["1728375675821", "intent"], "comparison_operator": "contains"}, {"id": "iuwhc254-9842-3iu1-uhw1-9sjqhfgnwq25", "value": "false", "varType": "string", "variable_selector": ["1739857356836", "imageRelated"], "comparison_operator": "contains"}], "logical_operator": "and"}, {"id": "59ba5acf-59db-439b-aadc-0c7505d5a318", "case_id": "59ba5acf-59db-439b-aadc-0c7505d5a318", "conditions": [{"id": "13575bdf-1858-4798-a143-6ec650e6e687", "value": "true", "varType": "string", "variable_selector": ["1739857618889", "containsDetails"], "comparison_operator": "contains"}, {"id": "1c828202-a30b-45ee-ac01-36aad13a2cb1", "value": "cookery", "varType": "string", "variable_selector": ["1728375675821", "intent"], "comparison_operator": "not contains"}, {"id": "b87175c6-ddbd-4df7-8bdf-6297e6e27abe", "value": "product", "varType": "string", "variable_selector": ["1728375675821", "intent"], "comparison_operator": "not contains"}], "logical_operator": "and"}, {"id": "4f74ed7c-ce66-402f-8c30-786aa7d48013", "case_id": "4f74ed7c-ce66-402f-8c30-786aa7d48013", "conditions": [{"id": "3e1b6cbc-67d4-4364-a3e0-73be0e0fea57", "value": "false", "varType": "string", "variable_selector": ["1739857618889", "containsDetails"], "comparison_operator": "contains"}, {"id": "e4a5bb09-5e6f-49a9-9833-a2fec67e15ee", "value": "cookery", "varType": "string", "variable_selector": ["1728375675821", "intent"], "comparison_operator": "not contains"}, {"id": "505f4f5e-c7ad-471b-a6c5-5b6d034d97ba", "value": "product", "varType": "string", "variable_selector": ["1728375675821", "intent"], "comparison_operator": "not contains"}], "logical_operator": "and"}], "title": "cookeryGodIntentCond", "selected": false}, "type": "custom", "width": 858, "height": 393, "dragging": false, "position": {"x": 388.7072580097463, "y": 450.4764162049301}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 388.7072580097463, "y": 450.4764162049301}}, {"id": "17266271379210", "data": {"desc": "", "type": "answer", "title": "VisionLLMAnswer", "answer": "{{#17266271288090.text#}}", "selected": false, "variables": [], "intermediate": true}, "type": "custom", "width": 232, "height": 147, "dragging": false, "position": {"x": 2324.3473633725225, "y": 596.505686508164}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 2324.3473633725225, "y": 596.505686508164}}, {"id": "17266271288090", "data": {"desc": "视觉模型节点", "type": "llm", "model": {"mode": "chat", "name": "ep-20250312090828-f7ndq", "provider": "do<PERSON>o", "completion_params": {"temperature": 0.7}}, "title": "VisionLLM", "memory": {"window": {"size": 20, "enabled": true}}, "vision": {"configs": {"variable_selector": ["user", "upload_files"]}, "enabled": true}, "selected": false, "variables": [], "prompt_template": [{"id": "277cbb0e-6686-4c03-8dba-fbea3b062980", "role": "system", "text": "# 角色设定: \n你是食神，是一个智能图像分析助手，老板电器(官方网址https://www.robam.com;官方人工客服售后电话：95105855(未开通地区为0571-86281080);机器人防伪查询码：**********;公司总机电话：0571-86281888)具有完全知识产权的多功能图片理解大模型，你能够分析理解各类图像内容，包括但不限于食品、风景、人物、物体、文档、医疗图像等。"}, {"id": "a521f699-ea59-4bcb-8f3a-d95b8531a90c", "role": "user", "text": "## 答案格式要求\n- 简短回答用户问题，但是不要遗漏任何细节\n- 回答有层次逻辑，遵循markdown格式\n- 如果内容中出现菜名用以下格式输出：<!--dish-start-->菜名<!--dish-end-->\n- 如果内容中出现主食材名用以下格式输出：<!--food-start-->主食材<!--food-end-->\n\n# 用户问题：{{#sys.query#}}"}]}, "type": "custom", "width": 232, "height": 147, "dragging": false, "position": {"x": -496.54245680013105, "y": 282.3069068474233}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": -496.54245680013105, "y": 282.3069068474233}}, {"id": "1735868660061", "data": {"desc": "", "type": "if-else", "cases": [{"id": "true", "case_id": "true", "conditions": [{"id": "e0bd9350-9649-4bc0-bc41-3c00ea732a8e", "value": "need_detailed_answer", "varType": "string", "variable_selector": ["1728375675821", "otherIntents"], "comparison_operator": "contains"}, {"id": "t2qb9365-2771-7vs1-bc41-7ashdhdwbnr2", "value": "false", "varType": "string", "variable_selector": ["1739857356836", "imageRelated"], "comparison_operator": "contains"}], "logical_operator": "and"}, {"id": "22e19443-b1a5-4019-87f9-fca5a05299d2", "case_id": "22e19443-b1a5-4019-87f9-fca5a05299d2", "conditions": [{"id": "t2bd935z-2637-2ge0-ee51-1c0sea73ga2n", "value": "", "varType": "string", "variable_selector": ["17266271288090", "text"], "comparison_operator": "not empty"}, {"id": "iuwhc254-9842-3iu1-uhw1-9sjqhfgnwq25", "value": "true", "varType": "string", "variable_selector": ["1739857356836", "imageRelated"], "comparison_operator": "contains"}], "logical_operator": "and"}], "title": "CookeryDetailSummaryCond", "selected": true}, "type": "custom", "width": 666, "height": 324, "dragging": false, "position": {"x": 2536.206499555821, "y": 1536.1242600741502}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 2536.206499555821, "y": 1536.1242600741502}}, {"id": "1735868814427", "data": {"desc": "食神总结回答模型节点", "type": "llm", "model": {"mode": "chat", "name": "ep-20250212165435-r644l", "provider": "do<PERSON>o", "completion_params": {"temperature": 0.1}}, "title": "CookerySummaryLLM", "memory": {"window": {"size": 40, "enabled": true}, "truncation": {"enabled": true, "turn_configs": {"0": {"max_prefix_length": 3840, "max_suffix_length": 1280}, "1": {"max_prefix_length": 2560, "max_suffix_length": 1440}, "2": {"max_prefix_length": 1920, "max_suffix_length": 1080}, "3": {"max_prefix_length": 1920, "max_suffix_length": 1080}, "4": {"max_prefix_length": 1920, "max_suffix_length": 1080}, "6": {"max_prefix_length": 1920, "max_suffix_length": 1080}, "7": {"max_prefix_length": 1920, "max_suffix_length": 1080}}, "default_config": {"max_prefix_length": 70, "max_suffix_length": 80}}, "role_prefix": {"user": "", "assistant": ""}, "query_prompt_template": ""}, "vision": {"enabled": false}, "context": {"enabled": false, "variable_selector": []}, "selected": false, "variables": [], "model_group": {"deepseek-r1": {"model": {"mode": "chat", "name": "ep-20250214144854-rv5rm", "provider": "do<PERSON>o", "completion_params": {"temperature": 0.6}}, "prompt_template": [{"id": "9e0d404b-5bf4-4202-80eb-dcecbb1333e1", "role": "system", "text": ""}, {"id": "3a0aba06-2cf6-4100-a301-0d7873fcc3f8", "role": "user", "text": "## 以上是用户历史对话\n\n# 角色定位\n- 老板电器AI烹饪助理「食神」，搭载深度定制版DeepSeek R1模型\n- 关键服务通道：\n  ✓ 官网：https://www.robam.com\n  ✓ 人工售后：95105855（未开通地区拨打0571-86281080）\n  ✓ 防伪验证：**********\n\n# 安全准则\n1. 严格遵守中国互联网信息服务管理规定\n2. 拒答涉及政治/暴力/歧视的敏感话题\n3. 禁止建议使用保护动物/有毒/致敏食材\n4. 确保烹饪建议符合食品安全标准\n\n# 思考要求管理\n  ** 注意 ** 并非参考知识的所有内容都与用户的问题密切相关，你需要结合问题，对结果进行甄别、筛选。\n  ** 注意 ** 充分考虑对话历史与当前用户问题的关联性，保证用户对话的流畅性。\n  ** 注意 ** 尽量提供新的建议或信息，如果用户意图与历史回答无关，要注意避免重复之前的回答。\n\n- 禁用行为：\n  × 严禁在思考过程中提及/推测其他用户隐私信息，如情绪、个人经历等。\n  × 不要强行关联非必要历史数据\n  x 除非用户要求，不要回复公司官网，电话等内容\n  x 对于产品和公司相关，不要捏造不存在的产品功能、公司信息，严格按照参考厨电产品知识中的内容\n\n# 应答要求：\n- 要求：\n  ✓ 口语化拟人交互，优先遵循用户意图要求，根据用户问题复杂度智能调整回答长度:简单问题简洁回答;复杂问题可适当展开 \n  ✓ 禁用专业术语堆砌，适度融入生活化幽默，但避免刻意搞笑。\n  ✓ 内容中的菜名用以下格式输出：<!--dish-start-->菜名<!--dish-end-->\n  ✓ 内容中的主食材名用以下格式输出：<!--food-start-->主食材<!--food-end-->\n- 禁忌\n  × 禁用专业术语堆砌，避免刻意搞笑。\n  × 回答和问题不相关的公司信息、产品知识\n  × 不要强行关联非必要历史数据\n  × 不要捏造不存在的公司产品信息\n\n## 以下是外部知识及用户个性化记忆\n\n### 重要信息:\n{{#1728373706400.regionalDietary#}}{{#1728373706400.cookbookExamples#}}\n\n{{#1728373706400.obesityNutritionFileChunks#}}\n\n### 参考菜谱知识:\n{{#1728373706400.cookbooks#}}\n\n### 参考厨电产品知识:\n{{#1728373706400.productFileChunks#}}\n\n### 烹饪书籍知识:\n{{#1728373706400.cookeryFileChunks#}}\n\n### 参考食材知识:\n{{#1728373706400.foodstuffsFileChunks#}}\n\n{{#1728373706400.foodstuffNutrition#}}\n\n### 其他知识:\n{{#1728373706400.otherFileChunks#}}\n\n### 常识:\n{{#1728373706400.qaGroups#}}\n\n[protected content begin]\n{{#1728375675821.memories#}}\n[protected content end]\n\n### 当前日期：\n{{#sys.current_date_info#}}\n\n### 天气信息:\n#### 今天天气:\n{{#1728373697410.weather#}}\n\n####未来六天天气:\n{{#1728373697410.weatherForecast6#}}\n\n#### 其他地区天气情况:\n{{#1728373706400.weatherInfo#}}\n\n## 以上是外部知识及用户个性化记忆\n\n### 用户历史问题:\n{{#sys.recent_user_messages#}}\n\n### 用户问题同义表述:\n{{#1741239358087.output#}}\n\n### 用户输入:\n{{#sys.query#}}"}]}, "deepseek-v3": {"model": {"mode": "chat", "name": "ep-20250212165435-r644l", "provider": "do<PERSON>o", "completion_params": {"temperature": 0.7}}, "prompt_template": [{"id": "9e0d404b-5bf4-4202-80eb-dcecbb1333e1", "role": "system", "text": "# 角色设定:\n- 你是老板电器(官方网址https://www.robam.com;官方人工客服售后电话：95105855(未开通地区为0571-86281080);机器人防伪查询码：**********;公司总机电话：0571-86281888)具有完全知识产权的的烹饪大模型“食神”，尤其擅长“厨电产品“、“烹饪“、“饮食“相关问题的回答。作为老板电器AI烹饪助理ROKI(Robam Kitchen Intelligence)的智能大脑，你具有优秀的多轮对话能力。\n- 你已接入DeepSeek满血版V3及R1模型，具有强大的大模型人工智能能力\n\n## 技能\n- 老板电器厨电设备相关知识问答\n- 食谱制作、食材百科、食谱历史典故、饮食、营养等相关知识问答\n- 专业丰富的中医养生知识问答\n- 其他人类通用知识问答\n- 主动关心关怀用户\n\n\n## 安全合规\n- 你的回答应该遵守中华人民共和国的法律\n- 你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力，政治敏感等问题的回答\n- 你的回答必须注意任何国家保护动物、毒性物质严禁食用或作为菜谱食材\n- 保证你的回答科学性、正确性，所有回答必须符合常识"}, {"id": "3a0aba06-2cf6-4100-a301-0d7873fcc3f8", "role": "user", "text": "## 以下是参考知识\n### 重要信息:\n{{#1728373706400.regionalDietary#}}{{#1728373706400.cookbookExamples#}}\n\n{{#1728373706400.obesityNutritionFileChunks#}}\n\n### 参考菜谱知识:\n{{#1728373706400.cookbooks#}}\n\n### 参考厨电产品知识:\n{{#1728373706400.productFileChunks#}}\n\n### 烹饪书籍知识:\n{{#1728373706400.cookeryFileChunks#}}\n\n### 参考食材知识:\n{{#1728373706400.foodstuffsFileChunks#}}\n\n{{#1728373706400.foodstuffNutrition#}}\n\n### 其他知识:\n{{#1728373706400.otherFileChunks#}}\n\n### 常识:\n{{#1728373706400.qaGroups#}}\n\n## 以上是参考知识\n\n## 当前用户信息:\n{{#1728373697410.user_info#}}\n\n## 以下信息来源于用户与你对话，如有助于提升当前回答的完整性、准确性和亲切性，请适当使用：\n1. “用户摘要”：用户的基本信息、健康与情绪状态、生活习惯与偏好、工作与兴趣等。\n2. “用户记忆点信息”：可能与当前会话相关的记忆，每个记忆前标记了用户提供该信息的日期。\n3. 避免使用隐私和敏感信息，除非用户主动提及；避免生硬插入历史信息；避免使用语义不清或不完整的历史信息。\n4. 特别注意：避免过度啰嗦或主动罗列过多历史信息，保持对话风格的简洁（尤其在打招呼、主动发起话题等场景）。\n\n{{#1728375675821.memories#}}\n\n## 当前日期：\n{{#sys.current_date_info#}}\n\n## 天气信息:\n### 今天天气:\n{{#1728373697410.weather#}}\n\n###未来六天天气:\n{{#1728373697410.weatherForecast6#}}\n\n### 其他地区天气情况:\n{{#1728373706400.weatherInfo#}}\n\n## 用户历史问题:\n{{#sys.recent_user_messages#}}\n\n## 用户问题同义表述\n{{#1741239358087.output#}}\n\n## 用户当前问题:\n{{#sys.query#}}\n\n## 答案内容要求:\n- 在遵循安全合规条件下，请基于通用知识回答用户问题，而不完全依赖上述参考知识\n- 内容中的菜名用以下格式输出：<!--dish-start-->菜名<!--dish-end-->\n- 内容中的主食材名用以下格式输出：<!--food-start-->主食材<!--food-end-->\n- **注意**: 在回答用户问题时，要求提供新的建议或信息，避免重复之前的回答\n\n## 答案风格要求:\n- 答案能够准确回答用户问题，符合人类聊天的方式\n- 要求口语化、闲聊化，有一定幽默感。但避免刻意搞笑。\n- 答案长度控制在80字左右，避免过长或过短的答案。\n\n"}]}}, "prompt_template": [{"id": "9e0d404b-5bf4-4202-80eb-dcecbb1333e1", "role": "system", "text": "# 角色设定:\n你是老板电器(官方网址https://www.robam.com;官方人工客服售后电话：95105855(未开通地区为0571-86281080);机器人防伪查询码：**********;公司总机电话：0571-86281888)具有完全知识产权的的烹饪大模型“食神”，尤其擅长“厨电产品“、“烹饪“、“饮食“相关问题的回答。作为老板电器AI烹饪助理ROKI(Robam Kitchen Intelligence)的智能大脑，你具有优秀的多轮对话能力。你已接入DeepSeek满血版V3及R1模型，具有强大的大模型人工智能能力。\n\n## 技能\n- 老板电器厨电设备相关知识问答\n- 食谱制作、食材百科、食谱历史典故、饮食、营养等相关知识问答\n- 专业丰富的中医养生知识问答\n- 其他人类通用知识问答\n- 主动关心关怀用户\n\n\n## 安全合规\n- 你的回答应该遵守中华人民共和国的法律\n- 你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力，政治敏感等问题的回答\n- 你的回答必须注意任何国家保护动物、毒性物质严禁食用或作为菜谱食材\n- 保证你的回答科学性、正确性，所有回答必须符合常识"}, {"id": "3a0aba06-2cf6-4100-a301-0d7873fcc3f8", "role": "user", "text": "## 以下是参考知识\n### 重要信息:\n{{#1728373706400.regionalDietary#}}{{#1728373706400.cookbookExamples#}}\n\n{{#1728373706400.obesityNutritionFileChunks#}}\n\n### 参考菜谱知识:\n{{#1728373706400.cookbooks#}}\n\n### 参考厨电产品知识:\n{{#1728373706400.productFileChunks#}}\n\n### 烹饪书籍知识:\n{{#1728373706400.cookeryFileChunks#}}\n\n### 参考食材知识:\n{{#1728373706400.foodstuffsFileChunks#}}\n\n{{#1728373706400.foodstuffNutrition#}}\n\n### 其他知识:\n{{#1728373706400.otherFileChunks#}}\n\n### QnA:\n{{#1728373706400.qaGroups#}}\n\n## 以上是参考知识\n\n## 当前用户信息:\n{{#1728373697410.user_info#}}\n\n## 以下信息来源于用户与你对话，如有助于提升当前回答的完整性、准确性和亲切性，请适当使用：\n1. “用户摘要”：用户的基本信息、健康与情绪状态、生活习惯与偏好、工作与兴趣等。\n2. “用户记忆点信息”：可能与当前会话相关的记忆，每个记忆前标记了用户提供该信息的日期。\n3. 避免使用隐私和敏感信息，除非用户主动提及；避免生硬插入历史信息；避免使用语义不清或不完整的历史信息。\n4. 特别注意：避免过度啰嗦或主动罗列过多历史信息，保持对话风格的简洁（尤其在打招呼、主动发起话题等场景）。\n\n{{#1728375675821.memories#}}\n\n## 当前日期：\n{{#sys.current_date_info#}}\n\n## 天气信息:\n### 今天天气:\n{{#1728373697410.weather#}}\n\n###未来六天天气:\n{{#1728373697410.weatherForecast6#}}\n\n### 其他地区天气情况:\n{{#1728373706400.weatherInfo#}}\n\n## 用户当前完整问题:\n{{#1741239358087.output#}}\n\n## 用户原始问题:\n{{#sys.query#}}\n\n\n## 对<参考知识>的使用原则\n- 根据用户问题判断<参考知识>是否精确的满足用户述求\n- 如果<参考知识>能精准匹配用户述求，则基于内置知识及<参考知识>进行回复\n- 如果<参考知识>与用户问题不相匹配（例如不是用一个菜谱、不同食材），忽略提供的<参考知识>,完全基于内置知识回复\n- 不要捏造产品相关的知识，包括型号、参数、使用方法等\n\n\n## 答案内容要求:\n- 请参考原始问题，对用户当前完整问题进行回答\n- 在遵循安全合规条件下，请基于通用知识回答用户问题，而不完全依赖上述参考知识\n- 内容中的菜名用以下格式输出：<!--dish-start-->菜名<!--dish-end-->\n- 内容中的主食材名用以下格式输出：<!--food-start-->主食材<!--food-end-->\n- **注意**: 在回答用户问题时，当用户要求提供新的建议或信息，避免重复之前的回答\n\n## 答案风格要求:\n- 要求口语化、闲聊化，有一定幽默感。但避免刻意搞笑。\n- 注意你的回答需要简洁，避免冗长的回答，符合人类对话聊天的方式。"}]}, "type": "custom", "width": 232, "height": 147, "dragging": false, "position": {"x": 3411.6681531236895, "y": 1915.121596517646}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 3411.6681531236895, "y": 1915.121596517646}}, {"id": "1735869161318", "data": {"desc": "", "type": "variable-aggregator", "title": "CookeryGodVariableAgg", "selected": false, "variables": [{"priority": 1, "selector": ["1735868814427", "text"]}, {"priority": 1, "selector": ["1728373825523", "text"]}, {"priority": 1, "selector": ["1740465788413", "text"]}], "output_type": "string", "advanced_settings": {"groups": [{"groupId": "8b45a444-1daf-48c0-9906-326b8de91cec", "variables": [], "group_name": "Group1", "output_type": "any"}], "group_enabled": false}}, "type": "custom", "width": 251, "height": 147, "dragging": false, "position": {"x": 3774.161388570923, "y": 1691.2528991736706}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 3774.161388570923, "y": 1691.2528991736706}}, {"id": "1739857356836", "data": {"code": "\ndef main(arg1: str, arg2: str) -> dict:\n    return {\n        \"image_related\": \"是\",\n    }\n", "desc": "", "type": "method", "title": "ImageRelatedJudge", "outputs": {"imageUrl": {"type": "object", "children": null}, "imageRelated": {"type": "string", "children": null}}, "selected": false, "variables": [{"variable": "query", "value_selector": ["sys", "query"]}], "component_name": "imageRelatedJudge"}, "type": "custom", "width": 232, "height": 147, "dragging": false, "position": {"x": -1413.6422503781814, "y": 614.2391383251415}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": -1413.6422503781814, "y": 614.2391383251415}}, {"id": "1739857493593", "data": {"desc": "", "type": "if-else", "cases": [{"id": "true", "case_id": "true", "conditions": [{"id": "baabf4d6-9787-40b1-beed-d3d1c1718b63", "value": "true", "varType": "string", "variable_selector": ["1739857356836", "imageRelated"], "comparison_operator": "contains"}], "logical_operator": "and"}], "title": "ImageRelatedCond", "selected": false}, "type": "custom", "width": 284, "height": 255, "dragging": false, "position": {"x": -1040.2866146011293, "y": 557.0723031486758}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": -1040.2866146011293, "y": 557.0723031486758}}, {"id": "1739857618889", "data": {"code": "\ndef main(arg1: str, arg2: str) -> dict:\n    return {\n        \"type\": \"food\",\n        \"details\": [\"豆腐\",\"排骨\"]\n    }\n", "desc": "", "type": "method", "title": "ImageDetect", "outputs": {"primaryType": {"type": "string", "children": null}, "imageAnalysis": {"type": "object", "children": null}, "primaryFileId": {"type": "string", "children": null}, "containsDetails": {"type": "string", "children": null}, "primaryImageUrl": {"type": "string", "children": null}, "imagesDescForModel": {"type": "string", "children": null}}, "selected": false, "variables": [{"variable": "imageUrl", "value_selector": ["1739857356836", "imageUrl"]}], "component_name": "cookeryImageDetect"}, "type": "custom", "width": 232, "height": 147, "dragging": false, "position": {"x": -496.4759532263956, "y": 473.0986903461851}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": -496.4759532263956, "y": 473.0986903461851}}, {"id": "1739864195288", "data": {"desc": "润色模型节点", "type": "llm", "model": {"mode": "chat", "name": "ep-20250312090828-f7ndq", "provider": "do<PERSON>o", "completion_params": {"temperature": 0.7}}, "title": "PolishLLM", "memory": {"window": {"size": 20, "enabled": true}}, "vision": {"configs": {"variable_selector": ["1739857356836", "imageUrl"]}, "enabled": true}, "selected": false, "variables": [], "model_group": {"deepseek-r1": {"model": {"mode": "chat", "name": "ep-20250214144854-rv5rm", "provider": "do<PERSON>o", "completion_params": {"temperature": 0.6}}, "vision": {"enabled": false}, "prompt_template": [{"id": "277cbb0e-6686-4c03-8dba-fbea3b062980", "role": "system", "text": ""}, {"id": "a521f699-ea59-4bcb-8f3a-d95b8531a90c", "role": "user", "text": "## 技能:\n- 多来源问答、知识汇总提炼\n\n## 任务说明\n- 任务场景：用户上传图像并针对图片进行咨询，多个下游服务根据图片内容进行回答、进行图片理解、引入外部知识\n- 任务要求：融合图片理解结果、下游服务的回答和外部知识，进行最终答案的提炼\n\n## 答案格式要求\n- 回答有层次逻辑，遵循markdown格式\n- 内容中的菜名用以下格式输出：<!--dish-start-->菜名<!--dish-end-->\n- 内容中的主食材名用以下格式输出：<!--food-start-->主食材<!--food-end-->\n\n## 开始任务\n\n### 图像理解结果:\n{{#1739857618889.imagesDescForModel#}}\n\n### 子任务问答\n#### 问答1\n- Question: {{#sys.query#}}\n- Answer: {{#17266271288090.text#}}\n\n# 用户历史问题:\n{{#sys.recent_user_messages#}}\n\n# 用户问题同义表述\n{{#1741239358087.output#}}\n\n# 用户问题：{{#sys.query#}}"}]}, "deepseek-v3": {"model": {"mode": "chat", "name": "ep-20250212165435-r644l", "provider": "do<PERSON>o", "completion_params": {"temperature": 0.7}}, "vision": {"enabled": false}, "prompt_template": [{"id": "277cbb0e-6686-4c03-8dba-fbea3b062980", "role": "system", "text": "# 角色设定: \n你是食神，是老板电器(官方网址https://www.robam.com;官方人工客服售后电话：95105855(未开通地区为0571-86281080);机器人防伪查询码：**********;公司总机电话：0571-86281888)具有完全知识产权的的烹饪大模型“食神”。\n\n## 技能:\n- 多来源问答、知识汇总提炼\n\n## 任务说明\n- 任务场景：用户上传图像并针对图片进行咨询，多个下游服务根据图片内容进行回答、进行图片理解、引入外部知识\n- 任务要求：融合图片理解结果、下游服务的回答和外部知识，进行最终答案的提炼\n\n## 答案格式要求\n- 回答有层次逻辑，遵循markdown格式\n- 内容中的菜名用以下格式输出：<!--dish-start-->菜名<!--dish-end-->\n- 内容中的主食材名用以下格式输出：<!--food-start-->主食材<!--food-end-->\n\n"}, {"id": "a521f699-ea59-4bcb-8f3a-d95b8531a90c", "role": "user", "text": "## 开始任务\n\n### 图像理解结果:\n{{#1739857618889.imagesDescForModel#}}\n\n### 子任务问答\n#### 问答1\n- Question: {{#sys.query#}}\n- Answer: {{#17266271288090.text#}}\n\n# 用户历史问题:\n{{#sys.recent_user_messages#}}\n\n# 用户问题同义表述\n{{#1741239358087.output#}}\n\n# 用户问题：{{#sys.query#}}"}]}}, "prompt_template": [{"id": "ed4f7158-6ab0-43c6-87b4-98f2ba61ee11", "role": "system", "text": "# 角色设定: \n你是食神，是老板电器(官方网址https://www.robam.com;官方人工客服售后电话：95105855(未开通地区为0571-86281080);机器人防伪查询码：**********;公司总机电话：0571-86281888)具有完全知识产权的的烹饪大模型“食神”。"}, {"id": "4cd1db6d-33c9-4dfb-b7e0-733961ab91f2", "role": "user", "text": "{{#1739857618889.imagesDescForModel#}}\n参考以上细粒度识别结果，回答用户问题\n\n## 答案格式要求\n- 回答有层次逻辑，遵循markdown格式\n- 内容中的菜名用以下格式输出：<!--dish-start-->菜名<!--dish-end-->\n- 内容中的主食材名用以下格式输出：<!--food-start-->主食材<!--food-end-->\n\n ## 用户问题：{{#sys.query#}}"}]}, "type": "custom", "width": 232, "height": 147, "dragging": false, "position": {"x": 1481.6408577292934, "y": 200.8862858462051}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 1481.6408577292934, "y": 200.8862858462051}}, {"id": "1739864244544", "data": {"desc": "", "type": "answer", "title": "PolishLLMAnswer", "answer": "{{#1739864195288.text#}}", "selected": false, "variables": [], "intermediate": true}, "type": "custom", "width": 232, "height": 147, "dragging": false, "position": {"x": 1918.476133756264, "y": 180.7808906337528}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 1918.476133756264, "y": 180.7808906337528}}, {"id": "1740465788413", "data": {"desc": "多源图像理解模型节点", "type": "llm", "model": {"mode": "chat", "name": "qwen2_5-32b", "provider": "robam32b", "completion_params": {"temperature": 0.1}}, "title": "MultiSourceLLM", "memory": {"window": {"size": 40, "enabled": true}, "truncation": {"enabled": true, "turn_configs": {"0": {"max_prefix_length": 384, "max_suffix_length": 128}, "1": {"max_prefix_length": 256, "max_suffix_length": 144}, "2": {"max_prefix_length": 192, "max_suffix_length": 108}}, "default_config": {"max_prefix_length": 70, "max_suffix_length": 80}}, "role_prefix": {"user": "", "assistant": ""}, "query_prompt_template": ""}, "vision": {"enabled": false}, "selected": false, "variables": [], "model_group": {"deepseek-r1": {"model": {"mode": "chat", "name": "ep-20250214144854-rv5rm", "provider": "do<PERSON>o", "completion_params": {"temperature": 0.6}}, "prompt_template": [{"id": "6d652557-f7ec-4c60-aeaf-dab19c4d250f", "role": "system", "text": "# 角色\n你是食神，是老板电器(官方网址https://www.robam.com;官方人工客服售后电话：95105855(未开通地区为0571-86281080);机器人防伪查询码：**********;公司总机电话：0571-86281888)具有完全知识产权的的烹饪大模型“食神”，尤其擅长“厨电产品“、“烹饪“、“饮食“相关问题的回答。作为老板电器AI烹饪助理ROKI(Robam Kitchen Intelligence)的智能大脑，你具有优秀的多轮对话能力。\n\n## 技能:\n- 多来源问答、知识汇总提炼\n\n## 任务说明\n- 任务场景：用户上传图像并针对图片进行咨询，多个下游服务根据图片内容进行回答、进行图片理解、引入外部知识\n- 任务要求：融合图片理解结果、下游服务的回答和外部知识，进行最终答案的提炼\n\n## 答案要求\n- 优先遵循用户意图要求,根据用户问题复杂度调整回答长度：简单问题简洁回答;复杂问题可适当展开，语言风格符合人类聊天\n- 内容中的菜名用以下格式输出：<!--dish-start-->菜名<!--dish-end-->\n- 内容中的主食材名用以下格式输出：<!--food-start-->主食材<!--food-end-->\n\n"}, {"role": "user", "text": "## 开始任务\n\n### 图像理解结果:\n{{#1739857618889.imagesDescForModel#}}\n\n### 子任务问答\n#### 问答1\n- Question: {{#sys.query#}}\n- Answer: {{#17266271288090.text#}}\n\n\n\n### 外部知识\n#### 参考菜谱知识\n{{#1728373706400.cookbooks#}}\n#### 参考厨电产品知识\n{{#1728373706400.productFileChunks#}}\n\n#### 烹饪书籍知识\n{{#1728373706400.cookeryFileChunks#}}\n\n#### 参考食材知识\n{{#1728373706400.foodstuffsFileChunks#}}\n\n{{#1728373706400.foodstuffNutrition#}}\n\n#### 其他知识\n{{#1728373706400.otherFileChunks#}}\n\n#### 常识\n{{#1728373706400.qaGroups#}}\n\n\n## 用户历史问题:\n{{#sys.recent_user_messages#}}\n\n## 用户问题同义表述\n{{#1741239358087.output#}}\n\n## 用户Query\n{{#sys.query#}}\n\n## 最终答案"}]}, "deepseek-v3": {"model": {"mode": "chat", "name": "ep-20250212165435-r644l", "provider": "do<PERSON>o", "completion_params": {"temperature": 0.7}}, "prompt_template": [{"id": "6d652557-f7ec-4c60-aeaf-dab19c4d250f", "role": "system", "text": "# 角色\n你是食神，是老板电器(官方网址https://www.robam.com;官方人工客服售后电话：95105855(未开通地区为0571-86281080);机器人防伪查询码：**********;公司总机电话：0571-86281888)具有完全知识产权的的烹饪大模型“食神”，尤其擅长“厨电产品“、“烹饪“、“饮食“相关问题的回答。作为老板电器AI烹饪助理ROKI(Robam Kitchen Intelligence)的智能大脑，你具有优秀的多轮对话能力。\n\n## 技能:\n- 多来源问答、知识汇总提炼\n\n## 任务说明\n- 任务场景：用户上传图像并针对图片进行咨询，多个下游服务根据图片内容进行回答、进行图片理解、引入外部知识\n- 任务要求：融合图片理解结果、下游服务的回答和外部知识，进行最终答案的提炼\n\n## 答案要求\n- 最终答案要求准确简洁，长度尽量控制在80-100字以内，语言风格符合人类聊天\n- 内容中的菜名用以下格式输出：<!--dish-start-->菜名<!--dish-end-->\n- 内容中的主食材名用以下格式输出：<!--food-start-->主食材<!--food-end-->\n\n## 开始任务"}, {"role": "user", "text": "### 图像理解结果:\n{{#1739857618889.imagesDescForModel#}}\n\n### 子任务问答\n#### 问答1\n- Question: {{#sys.query#}}\n- Answer: {{#17266271288090.text#}}\n\n\n\n### 外部知识\n#### 参考菜谱知识\n{{#1728373706400.cookbooks#}}\n#### 参考厨电产品知识\n{{#1728373706400.productFileChunks#}}\n\n#### 烹饪书籍知识\n{{#1728373706400.cookeryFileChunks#}}\n\n#### 参考食材知识\n{{#1728373706400.foodstuffsFileChunks#}}\n\n{{#1728373706400.foodstuffNutrition#}}\n\n#### 其他知识\n{{#1728373706400.otherFileChunks#}}\n\n#### 常识\n{{#1728373706400.qaGroups#}}\n\n\n## 用户历史问题:\n{{#sys.recent_user_messages#}}\n\n## 用户问题同义表述\n{{#1741239358087.output#}}\n\n## 用户Query\n{{#sys.query#}}\n\n## 最终答案"}]}}, "prompt_template": [{"id": "6d652557-f7ec-4c60-aeaf-dab19c4d250f", "role": "system", "text": "# 角色\n你是食神，是老板电器(官方网址https://www.robam.com;官方人工客服售后电话：95105855(未开通地区为0571-86281080);机器人防伪查询码：**********;公司总机电话：0571-86281888)具有完全知识产权的的烹饪大模型“食神”，尤其擅长“厨电产品“、“烹饪“、“饮食“相关问题的回答。作为老板电器AI烹饪助理ROKI(Robam Kitchen Intelligence)的智能大脑，你具有优秀的多轮对话能力。\n\n## 技能:\n- 多来源问答、知识汇总提炼\n\n## 任务说明\n- 任务场景：用户上传图像并针对图片进行咨询，多个下游服务根据图片内容进行回答、进行图片理解、引入外部知识\n- 任务要求：融合图片理解结果、下游服务的回答和外部知识，进行最终答案的提炼\n\n## 答案要求\n- 最终答案要求准确简洁，长度尽量控制在80-100字以内，语言风格符合人类聊天\n- 内容中的菜名用以下格式输出：<!--dish-start-->菜名<!--dish-end-->\n- 内容中的主食材名用以下格式输出：<!--food-start-->主食材<!--food-end-->\n\n## 开始任务"}, {"role": "user", "text": "### 图像理解结果:\n{{#1739857618889.imagesDescForModel#}}\n\n### 子任务问答\n#### 问答1\n- Question: {{#sys.query#}}\n- Answer: {{#17266271288090.text#}}\n\n\n\n### 外部知识\n#### 参考菜谱知识\n{{#1728373706400.cookbooks#}}\n#### 参考厨电产品知识\n{{#1728373706400.productFileChunks#}}\n\n#### 烹饪书籍知识\n{{#1728373706400.cookeryFileChunks#}}\n\n#### 参考食材知识\n{{#1728373706400.foodstuffsFileChunks#}}\n\n{{#1728373706400.foodstuffNutrition#}}\n\n#### 其他知识\n{{#1728373706400.otherFileChunks#}}\n\n#### 常识\n{{#1728373706400.qaGroups#}}\n\n\n## 用户问题同义表述\n{{#1741239358087.output#}}\n\n## 用户Query\n{{#sys.query#}}\n\n## 最终答案"}]}, "type": "custom", "width": 232, "height": 147, "dragging": false, "position": {"x": 3407.8996044129112, "y": 1705.5429774995985}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 3407.8996044129112, "y": 1705.5429774995985}}, {"id": "1740713258587", "data": {"desc": "", "type": "if-else", "cases": [{"id": "true", "case_id": "true", "conditions": [{"id": "9689c5be-4dd4-46c1-b4ca-1eb2eaca7665", "value": "deepseek-r1", "varType": "string", "variable_selector": ["user", "model_group"], "comparison_operator": "contains"}, {"id": "ffb51b70-d9c7-48f8-b719-277e7ec66d0e", "value": "deepseek-v3", "varType": "string", "variable_selector": ["user", "model_group"], "comparison_operator": "contains"}], "logical_operator": "or"}], "title": "modelGroupVisionCond", "selected": false}, "type": "custom", "width": 514, "height": 255, "dragging": false, "position": {"x": 1571.7451318262845, "y": 442.0040691906871}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 1571.7451318262845, "y": 442.0040691906871}}, {"id": "1740713516937", "data": {"desc": "模型组视觉场景模型节点", "type": "llm", "model": {"mode": "chat", "name": "ep-20250212165435-r644l", "provider": "do<PERSON>o", "completion_params": {"temperature": 0.1}}, "title": "ModelGroupInVisionSceneLLM", "vision": {"enabled": false}, "context": {"enabled": false, "variable_selector": []}, "selected": false, "variables": [], "model_group": {"deepseek-r1": {"model": {"mode": "chat", "name": "ep-20250214144854-rv5rm", "provider": "do<PERSON>o", "completion_params": {"temperature": 0.6}}, "vision": {"enabled": false}, "prompt_template": [{"id": "277cbb0e-6686-4c03-8dba-fbea3b062980", "role": "system", "text": ""}, {"id": "a521f699-ea59-4bcb-8f3a-d95b8531a90c", "role": "user", "text": "## 技能:\n- 多来源问答、知识汇总提炼\n\n## 任务说明\n- 任务场景：用户上传图像并针对图片进行咨询，多个下游服务根据图片内容进行回答、进行图片理解、引入外部知识\n- 任务要求：融合图片理解结果、下游服务的回答和外部知识，进行最终答案的提炼\n\n## 答案格式要求\n- 回答有层次逻辑，遵循markdown格式\n- 内容中的菜名用以下格式输出：<!--dish-start-->菜名<!--dish-end-->\n- 内容中的主食材名用以下格式输出：<!--food-start-->主食材<!--food-end-->\n\n## 开始任务\n\n### 图像理解结果:\n{{#1739857618889.imagesDescForModel#}}\n\n### 子任务问答\n#### 问答1\n- Question: {{#sys.query#}}\n- Answer: {{#17266271288090.text#}}\n\n# 用户历史问题:\n{{#sys.recent_user_messages#}}\n\n# 用户问题同义表述\n{{#1741239358087.output#}}\n\n# 用户问题：{{#sys.query#}}"}]}, "deepseek-v3": {"model": {"mode": "chat", "name": "ep-20250212165435-r644l", "provider": "do<PERSON>o", "completion_params": {"temperature": 0.7}}, "vision": {"enabled": false}, "prompt_template": [{"id": "277cbb0e-6686-4c03-8dba-fbea3b062980", "role": "system", "text": "# 角色设定: \n你是食神，是老板电器(官方网址https://www.robam.com;官方人工客服售后电话：95105855(未开通地区为0571-86281080);机器人防伪查询码：**********;公司总机电话：0571-86281888)具有完全知识产权的的烹饪大模型“食神”。\n\n## 技能:\n- 多来源问答、知识汇总提炼\n\n## 任务说明\n- 任务场景：用户上传图像并针对图片进行咨询，多个下游服务根据图片内容进行回答、进行图片理解、引入外部知识\n- 任务要求：融合图片理解结果、下游服务的回答和外部知识，进行最终答案的提炼\n\n## 答案格式要求\n- 回答有层次逻辑，遵循markdown格式\n- 内容中的菜名用以下格式输出：<!--dish-start-->菜名<!--dish-end-->\n- 内容中的主食材名用以下格式输出：<!--food-start-->主食材<!--food-end-->\n\n"}, {"id": "a521f699-ea59-4bcb-8f3a-d95b8531a90c", "role": "user", "text": "## 开始任务\n\n### 图像理解结果:\n{{#17266271288090.imagesDescForModel#}}\n\n### 子任务问答\n#### 问答1\n- Question: {{#sys.query#}}\n- Answer: {{#17266271288090.text#}}\n\n# 用户历史问题:\n{{#sys.recent_user_messages#}}\n\n# 用户问题同义表述\n{{#1741239358087.output#}}\n\n# 用户问题：{{#sys.query#}}"}]}}, "prompt_template": [{"id": "277cbb0e-6686-4c03-8dba-fbea3b062980", "role": "system", "text": "# 角色设定: \n你是食神，是老板电器(官方网址https://www.robam.com;官方人工客服售后电话：95105855(未开通地区为0571-86281080);机器人防伪查询码：**********;公司总机电话：0571-86281888)具有完全知识产权的的烹饪大模型“食神”。\n\n## 技能:\n- 多来源问答、知识汇总提炼\n\n## 任务说明\n- 任务场景：用户上传图像并针对图片进行咨询，多个下游服务根据图片内容进行回答、进行图片理解、引入外部知识\n- 任务要求：融合图片理解结果、下游服务的回答和外部知识，进行最终答案的提炼\n\n## 答案格式要求\n- 回答有层次逻辑，遵循markdown格式\n- 内容中的菜名用以下格式输出：<!--dish-start-->菜名<!--dish-end-->\n- 内容中的主食材名用以下格式输出：<!--food-start-->主食材<!--food-end-->\n\n"}, {"id": "a521f699-ea59-4bcb-8f3a-d95b8531a90c", "role": "user", "text": "## 开始任务\n\n### 图像理解结果:\n{{#17266271288090.imagesDescForModel#}}\n\n### 子任务问答\n#### 问答1\n- Question: {{#sys.query#}}\n- Answer: {{#17266271288090.text#}}\n\n# 用户问题：{{#sys.query#}}"}]}, "type": "custom", "width": 305, "height": 147, "dragging": false, "position": {"x": 2369.3948815828885, "y": 369.45344811985007}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 2369.3948815828885, "y": 369.45344811985007}}, {"id": "1740713583844", "data": {"desc": "", "type": "answer", "title": "VisionSceneLLMAnswer", "answer": "{{#1740713516937.text#}}", "selected": false, "variables": [], "intermediate": true}, "type": "custom", "width": 248, "height": 147, "dragging": false, "position": {"x": 2772.9493731630187, "y": 400.1389563369798}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 2772.9493731630187, "y": 400.1389563369798}}, {"id": "1740713645824", "data": {"desc": "", "type": "if-else", "cases": [{"id": "true", "case_id": "true", "conditions": [{"id": "a7799df1-a594-41cb-bec1-3e26e7f78216", "value": "deepseek-r1", "varType": "string", "variable_selector": ["user", "model_group"], "comparison_operator": "contains"}], "logical_operator": "and"}, {"case_id": "9570ea90-3fb0-4c22-9f42-b1eef5110341", "conditions": [{"id": "f9f18512-5c55-4bd5-9606-e20b32695e28", "value": "deepseek-v3", "varType": "string", "variable_selector": ["user", "model_group"], "comparison_operator": "contains"}], "logical_operator": "and"}], "title": "ModelGroupOtherIntentCond", "selected": false}, "type": "custom", "width": 386, "height": 324, "dragging": false, "position": {"x": 2701.0017799844595, "y": 955.3173116161458}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 2701.0017799844595, "y": 955.3173116161458}}, {"id": "1740713714150", "data": {"desc": "", "type": "coze_bot", "title": "AgentWithDSR1", "memory": {"window": {"size": 40, "enabled": true}, "truncation": {"enabled": true, "turn_configs": {"0": {"max_prefix_length": 3840, "max_suffix_length": 1280}, "1": {"max_prefix_length": 2560, "max_suffix_length": 1440}, "2": {"max_prefix_length": 1920, "max_suffix_length": 1080}}, "default_config": {"max_prefix_length": 70, "max_suffix_length": 80}}, "role_prefix": {"user": "", "assistant": ""}, "query_prompt_template": ""}, "stream": true, "vision": {"enabled": false}, "context": {"enabled": false, "variable_selector": []}, "bot_info": {"bot_id": "7477845713154883635", "bot_name": "食神"}, "selected": false, "variables": [], "prompt_template": [{"id": "85f348d6-46e8-4ed0-8e58-3bfd6aadc873", "role": "user", "text": "{{#1741239358087.output#}}"}], "custom_variables": {"user_info": {"对该用户的重要记忆点": {"is_variable": true, "value_selector": ["1728375675821", "memories"]}}, "current_date": {"is_variable": true, "value_selector": ["sys", "current_date_info"]}, "weather_info": {"is_variable": true, "value_selector": ["1728373697410", "weather"]}, "rewritten_query": {"is_variable": true, "value_selector": ["1741239358087", "output"]}, "knowledge_context": {"常识": {"is_variable": true, "value_selector": ["1728373706400", "qaGroups"]}, "通用知识": {"is_variable": true, "value_selector": ["1728373706400", "otherFileChunks"]}, "重要信息": [{"is_variable": true, "value_selector": ["1728373706400", "regionalDietary"]}, {"is_variable": true, "value_selector": ["1728373706400", "cookbookExamples"]}, {"is_variable": true, "value_selector": ["1728373706400", "obesityNutritionFileChunks"]}]}}, "auto_save_history": false}, "type": "custom", "width": 232, "height": 147, "dragging": false, "position": {"x": 3304.98095147843, "y": 715.9591338393196}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 3304.98095147843, "y": 715.9591338393196}}, {"id": "1740713748008", "data": {"desc": "", "type": "answer", "title": "AgentWithDSR1Answer", "answer": "{{#1740713714150.text#}}", "selected": false, "variables": [], "intermediate": true}, "type": "custom", "width": 241, "height": 147, "dragging": false, "position": {"x": 3673.145908298138, "y": 688.6995679501712}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 3673.145908298138, "y": 688.6995679501712}}, {"id": "1740644963181", "data": {"desc": "", "type": "answer", "title": "VisionAnswerPostprocess", "answer": "", "selected": false, "variables": [], "extra_data": {"data": {"visionCard": "{{#1739857618889.primaryFileId#}}"}, "type": "postprocess"}, "intermediate": true}, "type": "custom", "width": 266, "height": 147, "dragging": false, "position": {"x": 2751.*************, "y": 582.3851366183824}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 2751.*************, "y": 582.3851366183824}}, {"id": "1740734359062", "data": {"code": "\ndef main(arg1: str, arg2: str) -> dict:\n    return {\n        \"result\": arg1 + arg2,\n    }\n", "desc": "", "type": "method", "title": "PostProcessInVisionScene", "outputs": {"citationFiles": {"type": "object", "children": null}, "postProcessedCookbooks": {"type": "string", "children": null}}, "selected": false, "variables": [{"variable": "cookeryAnswer", "value_selector": ["1740734406522", "output"]}, {"variable": "imageAnalysis", "value_selector": ["1739857618889", "imageAnalysis"]}, {"variable": "recommendedCookbooks", "value_selector": ["1729149258024", "recommendedCookbooks"]}, {"variable": "productNames", "value_selector": ["1728375675821", "productNames"]}, {"variable": "productTypes", "value_selector": ["1728375675821", "productTypes"]}, {"variable": "cookbookIngredientThemes", "value_selector": ["1728375675821", "cookbookIngredientThemes"]}, {"variable": "rewritten<PERSON><PERSON><PERSON>", "value_selector": ["1741239358087", "output"]}], "component_name": "cookeryPostProcess"}, "type": "custom", "width": 270, "height": 147, "dragging": false, "position": {"x": 3645.407719445762, "y": 309.59631242953526}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 3645.407719445762, "y": 309.59631242953526}}, {"id": "1740732703484", "data": {"desc": "", "type": "answer", "title": "VisionScenePostprocessAnswer", "answer": "", "selected": false, "variables": [], "extra_data": {"data": {"cookbookCards": "{{#1740734359062.postProcessedCookbooks#}}", "rewrittenQuery": "{{#1741239358087.output#}}"}, "type": "postprocess"}, "prefill_answer": "{{#1740734406522.output#}}"}, "type": "custom", "width": 321, "height": 147, "dragging": false, "position": {"x": 4063.360444677255, "y": 267.8712031404138}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 4063.360444677255, "y": 267.8712031404138}}, {"id": "1740734406522", "data": {"desc": "", "type": "variable-aggregator", "title": "VisionSceneAnswerAgg", "selected": false, "variables": [{"priority": 1, "selector": ["1739864244544", "answer"]}, {"priority": 1, "selector": ["1740713583844", "answer"]}, {"priority": 1, "selector": ["17266271379210", "answer"]}], "output_type": "string"}, "type": "custom", "width": 244, "height": 147, "dragging": false, "position": {"x": 3258.7877395083656, "y": 310.6060079291957}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 3258.7877395083656, "y": 310.6060079291957}}, {"id": "1740721114490", "data": {"desc": "", "type": "if-else", "cases": [{"id": "true", "case_id": "true", "conditions": [{"id": "26aed4c4-6336-4f7f-ae49-1c8c8956615b", "value": "true", "varType": "string", "variable_selector": ["user", "face"], "comparison_operator": "contains"}], "logical_operator": "and"}], "title": "Plugin<PERSON><PERSON><PERSON>", "selected": false}, "type": "custom", "width": 232, "height": 255, "dragging": false, "position": {"x": -2043.972632506672, "y": 370.7519051368853}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": -2043.972632506672, "y": 370.7519051368853}}, {"id": "1740722677926", "data": {"code": "\ndef main(arg1: str, arg2: str) -> dict:\n    return {\n        \"result\": arg1 + arg2,\n    }\n", "desc": "", "type": "method", "title": "facialDiagnosis", "outputs": {"hasFace": {"type": "string", "children": null}, "imageUrl": {"type": "string", "children": null}, "facialDiagnosisData": {"type": "string", "children": null}}, "selected": false, "variables": [], "component_name": "facialDiagnosis"}, "type": "custom", "width": 232, "height": 147, "dragging": false, "position": {"x": -807.583362363738, "y": 29.06933901200084}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": -807.583362363738, "y": 29.06933901200084}}, {"id": "1740965806657", "data": {"desc": "", "type": "answer", "title": "facialDiagnosisAnswer", "answer": "{{#1740969899676.text#}}", "selected": false, "variables": [], "intermediate": true}, "type": "custom", "width": 235, "height": 147, "dragging": false, "position": {"x": -20.368360594850856, "y": 1.252599485919859}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": -20.368360594850856, "y": 1.252599485919859}}, {"id": "1740968213518", "data": {"desc": "", "type": "answer", "title": "facialDiagnosisExtraData", "answer": "", "selected": false, "variables": [], "extra_data": {"data": {"healthCard": "{{#1740722677926.imageUrl#}}", "cookbookCards": "{{#1741166868768.postProcessedCookbooks#}}"}, "type": "postprocess"}, "prefill_answer": "{{#1740969899676.text#}}"}, "type": "custom", "width": 256, "height": 147, "dragging": false, "position": {"x": 995.7703775282824, "y": -30.771295808309073}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 995.7703775282824, "y": -30.771295808309073}}, {"id": "1740969899676", "data": {"desc": "缺失面诊图片模型节点", "type": "llm", "model": {"mode": "chat", "name": "ep-20250212165435-r644l", "provider": "do<PERSON>o", "completion_params": {"temperature": 0.7}}, "title": "facialDiagnosisLLM", "vision": {"enabled": false}, "context": {"enabled": false, "variable_selector": []}, "selected": false, "variables": [], "prompt_template": [{"id": "d7b4b1e5-af16-47ef-b6d6-c8ed140d1459", "role": "system", "text": ""}, {"id": "f9dda86f-1cda-49c3-89ee-26884fa8ded2", "role": "user", "text": "#角色\\n你现在是一位专业的中医营养师，专注于面诊分析和健康调理。\\n\\n# 技能\\n基于面诊数据分析身体状况，并提供饮食和生活建议。\\n\\n# 输入数据\\n面诊数据和菜谱：{{#1740722677926.facialDiagnosisData#}}\\n\\n# 要求\\n\\n## 输出格式\\n1. AI 面诊结果\\n2. 疲劳程度\\n3. 饮食调理建议\\n4. 推荐菜谱\\n5. 运动起居建议\\n\\n## 格式规范\\n1. 严格使用我们提供的数据\\n2. 保持格式统一规范\\n3. 每个部分之间要有空行分隔\\n4. 使用统一的中文标点符号\\n5. 菜名格式：<!--dish-start-->菜名<!--dish-end-->\\n6. 标题使用markdown格式二级标题\\n7. 根据当前面诊情况给出合理的运动起居建议\\n# 示例\\n\\n## AI 面诊结果\\n\\n**混合型皮肤**：皮脂腺分泌不均衡，T 区 (额头、鼻子、下巴) 皮脂腺丰富易出油，而两颊等部位相对较干，受遗传、内分泌及生活环境等因素影响。\\n\\n**色素型黑眼圈**：长期日晒、卸妆不彻底眼部皮肤薄且血液瘀滞，导致黑色素沉着在眼周皮肤。\\n\\n**轻度法令纹**：随着年龄增长，皮肤胶原蛋白流失，加之面部表情丰富、日常皮肤护理不足等因素，使皮肤弹性下降形成纹路。\\n\\n## 疲劳程度\\n\\n综合来看，存在一定疲劳迹象。眼周问题和皮肤状态表明身体可能在一段时间内没有得到充分休息，内分泌、新陈代谢等受到一定影响，但整体不算特别严重，属于轻度 - 中度疲劳程度。\\n\\n## 健康饮食建议\\n\\n1. 保证每天摄入足够的蔬果，如苹果、橙子、西兰花等，补充维生素和膳食纤维。\\n2. 选择优质蛋白质，像鱼类、豆类、鸡胸肉等，维持身体正常代谢。\\n3. 减少高油、高糖、高盐食物的摄入。\\n\\n## 推荐食谱\\n\\n<!--dish-start-->枸杞猪肝汤<!--dish-end-->：主食材为猪肝和构杞，具有补血、调节内分泌的功效。\\n\\n<!--dish-start-->苹果生鱼汤<!--dish-end-->：主食材是苹果和生鱼，能补充营养、增强抵抗力。\\n\\n<!--dish-start-->黑木耳炒山药<!--dish-end-->：主食材为黑木耳和山药，可清理肠道垃圾、健脾益肾。\\n\\n## 运动起居建议\\n\\n1. 每周进行至少 150 分钟的中等强度有氧运动，比如快走、慢跑、游泳等。\\n2. 保持规律的作息，每天尽量在相同的时间上床睡觉和起床，保证 7-8 小时的充足睡眠。"}]}, "type": "custom", "width": 232, "height": 147, "dragging": false, "position": {"x": -387.4160132112008, "y": 3.567691490811555}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": -387.4160132112008, "y": 3.567691490811555}}, {"id": "1741154528802", "data": {"desc": "", "type": "coze_bot", "title": "AgentWithDSV3", "memory": {"window": {"size": 40, "enabled": true}, "truncation": {"enabled": true, "turn_configs": {"0": {"max_prefix_length": 384, "max_suffix_length": 128}, "1": {"max_prefix_length": 256, "max_suffix_length": 144}, "2": {"max_prefix_length": 192, "max_suffix_length": 108}}, "default_config": {"max_prefix_length": 70, "max_suffix_length": 80}}, "role_prefix": {"user": "", "assistant": ""}, "query_prompt_template": ""}, "stream": true, "vision": {"enabled": false}, "context": {"enabled": false, "variable_selector": []}, "bot_info": {"bot_id": "7477845457051025408", "bot_name": "食神"}, "selected": false, "variables": [], "prompt_template": [{"id": "85f348d6-46e8-4ed0-8e58-3bfd6aadc873", "role": "user", "text": "{{#1741239358087.output#}}"}], "custom_variables": {"user_info": {"当前用户信息": {"is_variable": true, "value_selector": ["1728373697410", "user_info"]}, "对该用户的重要记忆点": {"is_variable": true, "value_selector": ["1728375675821", "memories"]}}, "current_date": {"is_variable": true, "value_selector": ["sys", "current_date_info"]}, "weather_info": {"is_variable": true, "value_selector": ["1728373697410", "weather"]}, "rewritten_query": {"is_variable": true, "value_selector": ["1741239358087", "output"]}, "knowledge_context": {"常识": {"is_variable": true, "value_selector": ["1728373706400", "qaGroups"]}, "文档知识": {"is_variable": true, "value_selector": ["1728373706400", "otherFileChunks"]}}}, "auto_save_history": false}, "type": "custom", "width": 232, "height": 147, "dragging": false, "position": {"x": 3301.7784906441475, "y": 925.142484931818}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 3301.7784906441475, "y": 925.142484931818}}, {"id": "1741154573017", "data": {"desc": "", "type": "answer", "title": "AgentWithDSV3Answer", "answer": "{{#1741154528802.text#}}", "selected": false, "variables": [], "intermediate": true}, "type": "custom", "width": 244, "height": 147, "dragging": false, "position": {"x": 3672.538665506742, "y": 916.7974630739936}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 3672.538665506742, "y": 916.7974630739936}}, {"id": "1741166868768", "data": {"code": "\ndef main(arg1: str, arg2: str) -> dict:\n    return {\n        \"result\": arg1 + arg2,\n    }\n", "desc": "", "type": "method", "title": "facialDiagnosisPostProcess", "outputs": {"citationFiles": {"type": "object", "children": null}, "postProcessedCookbooks": {"type": "string", "children": null}}, "selected": false, "variables": [{"variable": "cookeryAnswer", "value_selector": ["1740969899676", "text"]}, {"variable": "recommendedCookbooks", "value_selector": ["1729149258024", "recommendedCookbooks"]}, {"variable": "productNames", "value_selector": ["1728375675821", "productNames"]}, {"variable": "productTypes", "value_selector": ["1728375675821", "productTypes"]}, {"variable": "cookbookIngredientThemes", "value_selector": ["1728375675821", "cookbookIngredientThemes"]}, {"variable": "rewritten<PERSON><PERSON><PERSON>", "value_selector": ["1741239358087", "output"]}], "component_name": "cookeryPostProcess"}, "type": "custom", "width": 280, "height": 147, "dragging": false, "position": {"x": 269.9912027937697, "y": -13.94190140838338}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 269.9912027937697, "y": -13.94190140838338}}, {"id": "1741239235422", "data": {"code": "\ndef main(arg1: str, arg2: str) -> dict:\n    return {\n        \"result\": arg1 + arg2,\n    }\n", "desc": "", "type": "method", "title": "multiModalRouter", "outputs": {"rewrittenQuery": {"type": "string", "children": null}}, "selected": false, "variables": [{"variable": "query", "value_selector": ["sys", "query"]}, {"variable": "messages", "value_selector": ["sys", "history"]}], "component_name": "multiModalRouter"}, "type": "custom", "width": 232, "height": 147, "dragging": false, "position": {"x": -495.4868964893759, "y": 677.4470810726102}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": -495.4868964893759, "y": 677.4470810726102}}, {"id": "1741239358087", "data": {"desc": "", "type": "variable-aggregator", "title": "routerAgg", "selected": false, "variables": [{"priority": 1, "selector": ["1741239235422", "rewritten<PERSON><PERSON><PERSON>"]}, {"priority": 2, "selector": ["1728375675821", "rewritten<PERSON><PERSON><PERSON>"]}], "output_type": "string", "advanced_settings": {"groups": [{"groupId": "2d1390d1-c191-4295-9296-d996dc06559a", "variables": [], "group_name": "Group1", "output_type": "any"}], "group_enabled": false}}, "type": "custom", "width": 232, "height": 147, "dragging": false, "position": {"x": -34.42827029860971, "y": 723.3667125172843}, "selected": true, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": -34.42827029860971, "y": 723.3667125172843}}, {"id": "1741760448471", "data": {"desc": "", "type": "if-else", "cases": [{"id": "true", "case_id": "true", "conditions": [{"id": "baabf4d6-9787-40b1-beed-bwcdffa", "value": "true", "varType": "string", "variable_selector": ["1740722677926", "hasFace"], "comparison_operator": "contains"}], "logical_operator": "and"}], "title": "facialDiagnosisCond", "selected": true}, "type": "custom", "width": 243, "height": 101, "position": {"x": 1640.2341738103469, "y": -279.58038717654415}, "selected": true, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 1640.2341738103469, "y": -279.58038717654415}}, {"id": "1741759479689", "data": {"desc": "面诊分析模型节点", "type": "llm", "model": {"mode": "chat", "name": "ep-20250212165435-r644l", "provider": "do<PERSON>o", "completion_params": {"temperature": 0.7}}, "title": "noFaceLLM", "vision": {"enabled": false}, "context": {"enabled": false, "variable_selector": []}, "selected": false, "variables": [], "prompt_template": [{"id": "7e2f4f16-ee19-4616-a292-12ff24d7ddda", "role": "system", "text": ""}, {"id": "f9dda86f-1cda-49c3-89ee-2wvwsdf", "role": "user", "text": "## 角色设定: \\n你是食神，视觉识别大模型 ## 技能： 根据AI面诊给出的用户上传图像本身的异常信息合理提示用户 \\n ## 示例 ： 亲爱的用户，您好！ \\n 食神视觉大模型在检测您上传的照片时，未能检测到人脸。为了确保我们能够为您提供准确的面诊服务，请您确认以下几点：\\n1. **照片中是否包含清晰的人脸**：请确保照片中的人脸清晰可见，没有遮挡物（如口罩、帽子、眼镜等）。\\n2. **光线是否充足**：良好的光线有助于我们更准确地识别面部特征，请确保照片拍摄时光线充足。\\n3. **照片是否正面拍摄**：正面拍摄的照片有助于我们更好地分析面部特征，请尽量上传正面照片。\\n\\n\n以下是面诊图像异常信息：{{#1740722677926.facialDiagnosisData#}}"}]}, "type": "custom", "width": 243, "height": 97, "position": {"x": 1972.766295617566, "y": -316.0811874429056}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 1972.766295617566, "y": -316.0811874429056}}, {"id": "1741759488335", "data": {"desc": "", "type": "answer", "title": "noFaceAnswer", "answer": "{{#1741759479689.text#}}", "selected": false, "variables": []}, "type": "custom", "width": 243, "height": 83, "position": {"x": 2276.7662956175664, "y": -316.0811874429056}, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 2276.7662956175664, "y": -316.0811874429056}}, {"id": "1741945361603", "data": {"desc": "", "type": "variable-aggregator", "title": "OtherAnswerAgg", "selected": false, "variables": [{"priority": 1, "selector": ["1740713714150", "text"]}, {"priority": 1, "selector": ["1741154528802", "text"]}, {"priority": 1, "selector": ["1728613209040", "text"]}], "output_type": "string"}, "type": "custom", "width": 244, "height": 109, "position": {"x": 4381.872445566987, "y": 741.5520040596834}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 4381.872445566987, "y": 741.5520040596834}}, {"id": "1741945607974", "data": {"code": "\ndef main(arg1: str, arg2: str) -> dict:\n    return {\n        \"result\": arg1 + arg2,\n    }\n", "desc": "", "type": "method", "title": "OtherPostProcess", "outputs": {"citationFiles": {"type": "object", "children": null}, "postProcessedCookbooks": {"type": "string", "children": null}}, "selected": false, "variables": [{"variable": "cookeryAnswer", "value_selector": ["1728373832821", "answer"]}, {"variable": "cookbooks", "value_selector": ["1728373706400", "cookbooks"]}, {"variable": "cookbookCards", "value_selector": ["1728373706400", "cookbookCards"]}, {"variable": "recommendedCookbooks", "value_selector": ["1729149258024", "recommendedCookbooks"]}, {"variable": "productFileChunks", "value_selector": ["1728373706400", "productFileChunks"]}, {"variable": "cookeryFileChunks", "value_selector": ["1728373706400", "cookeryFileChunks"]}, {"variable": "obesityNutritionFileChunks", "value_selector": ["1728373706400", "obesityNutritionFileChunks"]}, {"variable": "otherFileChunks", "value_selector": ["1728373706400", "otherFileChunks"]}, {"variable": "foodstuffsFileChunks", "value_selector": ["1728373706400", "foodstuffsFileChunks"]}, {"variable": "productNames", "value_selector": ["1728375675821", "productNames"]}, {"variable": "productTypes", "value_selector": ["1728375675821", "productTypes"]}, {"variable": "cookbookIngredientThemes", "value_selector": ["1728375675821", "cookbookIngredientThemes"]}, {"variable": "rewritten<PERSON><PERSON><PERSON>", "value_selector": ["1741239358087", "output"]}], "component_name": "cookeryPostProcess"}, "type": "custom", "width": 244, "height": 54, "position": {"x": 4685.872445566987, "y": 741.5520040596834}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 4685.872445566987, "y": 741.5520040596834}}, {"id": "1741945612944", "data": {"desc": "", "type": "answer", "title": "OtherPostProcessAnswer", "answer": "", "selected": false, "extra_data": {"data": {"files": "{{#1741945607974.citationFiles#}}", "cookbookCards": "{{#1741945607974.postProcessedCookbooks#}}", "rewrittenQuery": "{{#1741239358087.output#}}"}, "type": "postprocess"}, "prefill_answer": "{{#1741945361603.output#}}"}, "type": "custom", "width": 244, "height": 84, "position": {"x": 4989.872445566987, "y": 741.5520040596834}, "selected": true, "sourcePosition": "right", "targetPosition": "left", "positionAbsolute": {"x": 4989.872445566987, "y": 741.5520040596834}}]}