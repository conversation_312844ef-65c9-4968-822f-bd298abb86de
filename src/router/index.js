import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/Home.vue'
import Workflow from '../views/Workflow.vue'
import Details from '../views/Details.vue'
import Compare from '../views/Compare.vue'
import WorkflowHistory from '../views/WorkflowHistory.vue'
import LogTrace from '../views/LogTrace.vue'
import AgentConfig from '../views/AgentConfig.vue'
import JsonEditorNew from '../views/JsonEditorNew.vue'
import PromptDebug from '../views/PromptDebug.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/details/:clientKey',
    name: 'Details',
    component: Details
  },
  {
    path: '/workflow',
    name: 'Workflow',
    component: Workflow
  },
  {
    path: '/compare',
    name: 'Compare',
    component: Compare
  },
  {
    path: '/workflow-history/:clientKey',
    name: 'WorkflowHistory',
    component: WorkflowHistory
  },
  {
    path: '/new-workflow',
    name: 'NewWorkflow',
    component: Workflow
  },
  {
    path: '/log-trace',
    name: 'LogTrace',
    component: LogTrace
  },
  {
    path: '/agent-config/:clientKey',
    name: 'AgentConfig',
    component: AgentConfig
  },
  {
    path: '/json-editor-new',
    name: 'JsonEditorNew',
    component: JsonEditorNew
  },
  {
    path: '/prompt-debug',
    name: 'PromptDebug',
    component: PromptDebug
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router 