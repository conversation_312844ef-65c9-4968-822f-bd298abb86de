<template>
  <div class="log-trace-page">
    <div class="search-fab-outer-top">
      <transition name="fab-fade">
        <div v-if="showSearch" id="search-fab-box" class="search-fab-box-top">
          <a-input
            v-model="searchTraceId"
            placeholder="Trace ID 日志追踪"
            allow-clear
            @press-enter="handleSearch"
            class="trace-input-fab"
            ref="inputRef"
          >
            <template #prefix>
              <icon-search style="color: #c0c4cc; font-size: 18px;" />
            </template>
          </a-input>
          <a-button type="primary" @click="handleSearch" :disabled="!searchTraceId || loading" class="trace-btn-fab">
            <template v-if="loading"><span class="arco-icon-loading"></span> 查询中</template>
            <template v-else>查询</template>
          </a-button>
        </div>
      </transition>
      <transition name="fab-fade">
        <a-button v-if="!showSearch" shape="circle" size="large" class="search-fab-btn-top" @click="openSearch" type="primary">
          <icon-search />
        </a-button>
      </transition>
    </div>

    <!-- 查询进度展示 -->
    <div v-if="loading" class="search-progress">
      <div class="progress-container">
        <div class="progress-status">{{ progressStatus }}</div>
        <a-progress :percent="progressPercent" :animation="true" :show-text="false" />
        <div class="progress-detail">{{ progressDetail }}</div>
      </div>
    </div>

    <div v-if="foundEnv" class="log-env">已查到环境：<b>{{ foundEnv }}</b></div>
    <div v-if="errorMsg" class="log-error">{{ errorMsg }}</div>
    <div v-if="parsedWorkflowJson" class="log-result">
      <VueJsonPretty :data="parsedWorkflowJson" />
    </div>
    <WorkflowViewer
      v-if="workflowJsonForViewer"
      :initialData="workflowJsonForViewer"
    />
    <WorkflowViewer
      v-else
      :trace-id="traceId"
    />
  </div>
</template>

<script setup>
import { ref, watch, nextTick, onMounted, onBeforeUnmount, computed } from 'vue'
import WorkflowViewer from '../components/WorkflowViewer.vue'
import { IconSearch } from '@arco-design/web-vue/es/icon'
import { searchLogs, getAuthToken } from '../api/log/logs'
import VueJsonPretty from 'vue-json-pretty'
import 'vue-json-pretty/lib/styles.css'
import { getLogTraceEnv, saveLogTraceEnv , saveLogTraceId } from '../utils/storage'
import exampleJson from '../stores/example.json'
import { Message } from '@arco-design/web-vue'

const traceId = ref('')
const searchTraceId = ref('')
const showSearch = ref(false)
const inputRef = ref(null)
const logResult = ref(null)
const loading = ref(false)
const errorMsg = ref('')
const foundEnv = ref('')

// 新增：进度相关状态
const progressPercent = ref(0)
const progressStatus = ref('')
const progressDetail = ref('')

// 新增：用于画布渲染的 JSON
const workflowJsonForViewer = ref(null)

// 动态获取 token
const token = ref('')
const index = 'roki_ai_ckb_agent_manager'
const envList = ['dev', 'test','prod']

// 页面加载时获取 token
onMounted(async () => {
  try {
    progressStatus.value = '正在获取授权...'
    token.value = await getAuthToken()
   // console.log('授权 token 获取成功：'+ token.value)
  } catch (error) {
    console.error('获取 token 失败:', error)
    errorMsg.value = '获取授权失败，请刷新页面重试'
    Message.error({
      content: '获取授权失败，请刷新页面重试',
      duration: 5000,
      closable: true
    })
  } finally {
    document.addEventListener('mousedown', handleClickOutside)
    document.addEventListener('keydown', handleEsc)
  }
})
// 处理节点状态样式
const processNodeStyles = (workflowData) => {
  if (!workflowData?.nodes) return workflowData
  
  const processedData = JSON.parse(JSON.stringify(workflowData))
  processedData.nodes = processedData.nodes.map(node => {
    if (node.data.type === 'start') return node

    // 只处理失败节点的样式
    if (node.data.success === false) {
      // 失败节点样式
      node.data.style = {
        ...node.data.style,
        border: '2px solid #f53f3f',
        borderRadius: '4px',
        boxShadow: '0 0 8px rgba(245, 63, 63, 0.4)',
        background: '#fff2f2'
      }
      // 添加失败标识
      const title = node.data.title || node.data.type
      node.data.title = `❌ ${title}`
      node.data.label = node.data.title
      
      // 如果有错误信息，添加到 tooltip
      if (node.data.error) {
        node.data.tooltip = {
          content: `错误信息：${node.data.error}`,
          style: {
            background: '#f53f3f',
            color: '#fff',
            padding: '8px 12px',
            borderRadius: '4px',
            fontSize: '14px',
            maxWidth: '300px',
            wordBreak: 'break-all'
          }
        }
      }
    } 
    // 成功节点只添加图标，不改变样式
    else if (node.data.success === true) {
      // 添加成功标识（绿色对勾）
      const title = node.data.title || node.data.type
      node.data.title = `✅ ${title}`  // 使用绿色圆圈
      node.data.label = node.data.title
    }
    return node
  })
  
  return processedData
}

const handleSearch = async () => {
  if (!searchTraceId.value) return
  traceId.value = searchTraceId.value
  
  // 在查询开始时就保存 traceId
  saveLogTraceId(searchTraceId.value)
  
  showSearch.value = false
  loading.value = true
  errorMsg.value = ''
  logResult.value = null
  foundEnv.value = ''
  workflowJsonForViewer.value = null // 每次查询前清空
  
  // 重置进度
  progressPercent.value = 0
  progressStatus.value = '正在准备查询...'
  progressDetail.value = '初始化查询参数'

  // 开发环境下使用模拟数据
  if (import.meta.env.DEV && traceId.value === '1') {
    try {
      // 模拟 API 延迟和进度
      progressStatus.value = '正在查询开发环境...'
      for (let i = 0; i < 5; i++) {
        progressPercent.value = i * 20
        progressDetail.value = `模拟数据加载中 (${i+1}/5)`
        await new Promise(resolve => setTimeout(resolve, 500))
      }
      // 完成进度
      progressPercent.value = 100
      progressStatus.value = '查询完成'
      progressDetail.value = '正在处理数据'
      
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // 处理节点样式后再赋值
      workflowJsonForViewer.value = processNodeStyles(exampleJson)
      logResult.value = {
        hits: {
          hits: [{
            _source: {
              message: `Executed Workflow Graph JSON:${JSON.stringify(exampleJson)}`
            }
          }]
        }
      }
      foundEnv.value = 'dev'
      saveLogTraceEnv('dev')
      loading.value = false
      return
    } catch (e) {
      console.error('模拟数据加载失败:', e)
    }
  }

  try {
    const body = {
      size: 1000,
      sort: [
        { '@timestamp': { order: 'desc' } }
      ],
      query: {
        bool: {
          must: [
            { terms: { 'traceId.keyword': [traceId.value] } }
          ],
          should: [
            { match_phrase: { message: 'Executed Workflow Graph JSON' } }
          ],
          minimum_should_match: 1
        }
      }
    }

    // 初始进度
    progressPercent.value = 10
    progressStatus.value = '正在准备查询...'
          progressDetail.value = '构建查询参数'

      // 检查 token 是否已获取
      if (!token.value) {
        try {
          progressStatus.value = '正在获取授权...'
          progressDetail.value = '尝试重新获取授权'
          token.value = await getAuthToken()
          console.log('授权 token 获取成功')
        } catch (error) {
          errorMsg.value = '获取授权失败，请刷新页面重试'
          loading.value = false
          return
        }
      }

      // 优先查询上次成功的环境
      const lastEnv = getLogTraceEnv()
      if (lastEnv) {
        progressPercent.value = 30
        progressStatus.value = `正在查询 ${lastEnv} 环境...`
        progressDetail.value = '使用上次成功的环境'
        
        const res = await searchLogs({ index, body, token: token.value, env: lastEnv })
      const hits = res.data.data?.hits?.hits
      
      progressPercent.value = 50
      progressDetail.value = '解析查询结果'
      
      if (hits && hits.length) {
        logResult.value = res.data
        foundEnv.value = lastEnv
        const msg = hits[0]._source?.message
        console.log('message:', msg)
        
        progressPercent.value = 70
        progressStatus.value = '在上次环境中找到数据'
        progressDetail.value = '正在处理日志数据'
        
        if (handleWorkflowJson(msg)) {
          progressPercent.value = 100
          progressStatus.value = '查询完成'
          progressDetail.value = '数据处理成功'
          loading.value = false
          return
        }
      }
    }

    // 如果上次环境未查到，则遍历所有环境
    let envIndex = 0
    for (const env of envList) {
      if (env === lastEnv) {
        envIndex++
        continue // 跳过已查询过的环境
      }
      
      progressPercent.value = 60 + (envIndex * 10)
      progressStatus.value = `正在查询 ${env} 环境...`
      progressDetail.value = `尝试环境 ${envIndex+1}/${envList.length}`
      
      const res = await searchLogs({ index, body, token: token.value, env })
      const hits = res.data.data?.hits?.hits
      
      if (hits && hits.length) {
        logResult.value = res.data
        foundEnv.value = env
        saveLogTraceEnv(env)
        const msg = hits[0]._source?.message
        console.log('message:', msg)
        
        progressPercent.value = 90
        progressStatus.value = `在 ${env} 环境中找到数据`
        progressDetail.value = '正在处理日志数据'
        
        if (handleWorkflowJson(msg)) {
          progressPercent.value = 100
          progressStatus.value = '查询完成'
          progressDetail.value = '数据处理成功'
          break
        }
      }
      
      envIndex++
    }

    if (!logResult.value) {
      progressPercent.value = 100
      progressStatus.value = '查询完成'
      progressDetail.value = '未找到匹配数据'
      
      errorMsg.value = 'dev、test、prod 三个环境都未查到日志'
      console.warn(errorMsg.value)
      
      // 添加弹框提示
      Message.warning({
        content: `未查询到 Trace ID: ${traceId.value} 的相关日志数据，请检查 ID 是否正确`,
        duration: 5000,
        closable: true
      })
    }
  } catch (e) {
    progressPercent.value = 100
    progressStatus.value = '查询失败'
    progressDetail.value = e?.message || '未知错误'
    
    errorMsg.value = e?.response?.data?.message || e.message || '日志查询失败'
    logResult.value = null
    workflowJsonForViewer.value = null
    console.error('查询异常:', errorMsg.value)
    
    // 添加失败弹框提示
    Message.error({
      content: `查询失败: ${e?.message || '未知错误'}`,
      duration: 5000,
      closable: true
    })
  } finally {
    loading.value = false
  }
}

// 修改 handleWorkflowJson 函数
const handleWorkflowJson = (msg) => {
  const prefix = 'Executed Workflow Graph JSON:'
  if (msg && msg.startsWith(prefix)) {
    try {
      const jsonData = JSON.parse(msg.slice(prefix.length).trim())
      // 处理节点样式后再赋值
      workflowJsonForViewer.value = processNodeStyles(jsonData)
      console.log('解析成功，workflowJsonForViewer:', workflowJsonForViewer.value)
      return true
    } catch (e) {
      workflowJsonForViewer.value = null
      errorMsg.value = '日志 JSON 解析失败：' + e.message
      console.error(errorMsg.value)
    }
  } else {
    workflowJsonForViewer.value = null
    errorMsg.value = '日志内容不包含有效的 Workflow JSON'
    console.warn(errorMsg.value)
  }
  return false
}

const parsedWorkflowJson = computed(() => {
  if (!logResult.value) return null
  const hits = logResult.value?.hits?.hits
  if (!hits || !hits.length) return null
  const msg = hits[0]._source?.message
  const prefix = 'Executed Workflow Graph JSON:'
  if (!msg || !msg.startsWith(prefix)) return null
  try {
    return JSON.parse(msg.slice(prefix.length).trim())
  } catch {
    return null
  }
})

const openSearch = () => {
  showSearch.value = true
  nextTick(() => {
    inputRef.value && inputRef.value.focus && inputRef.value.focus()
  })
}

const closeSearch = () => {
  showSearch.value = false
}

const handleClickOutside = (e) => {
  if (!showSearch.value) return
  const searchBox = document.getElementById('search-fab-box')
  if (searchBox && !searchBox.contains(e.target)) {
    closeSearch()
  }
}

const handleEsc = (e) => {
  if (e.key === 'Escape') closeSearch()
}

// 原来的 onMounted 逻辑已移至上面的 token 获取部分
onBeforeUnmount(() => {
  document.removeEventListener('mousedown', handleClickOutside)
  document.removeEventListener('keydown', handleEsc)
})

// 打印 parsedWorkflowJson（只打印前 50 行）
watch(parsedWorkflowJson, (val) => {
  if (val) {
    const jsonStr = JSON.stringify(val, null, 2)
    const lines = jsonStr.split('\n').slice(0, 50).join('\n')
    console.log('【LogTrace 检索到的 JSON（前 50 行）】\n' + lines)
  }
})

// 用于调试传递给 WorkflowViewer 的数据
function logAndReturn(val) {
  console.log('【LogTrace.vue 传递给 WorkflowViewer 的 initialData】', val)
  return val
}
</script>

<style scoped>
.log-trace-page {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #f8fafc 0%, #f2f6fc 100%);
}
.search-fab-outer-top {
  position: fixed;
  top: 32px;
  left: 0;
  right: 0;
  z-index: 300;
  display: flex;
  flex-direction: column;
  align-items: center;
  pointer-events: none;
}
.search-fab-btn-top {
  pointer-events: auto;
  box-shadow: 0 4px 16px 0 rgba(31,56,88,0.12);
  background: var(--color-primary-6);
  color: #fff;
  border: none;
  transition: box-shadow 0.2s;
}
.search-fab-btn-top:hover {
  box-shadow: 0 8px 24px 0 rgba(var(--primary-6), 0.18);
}
.search-fab-box-top {
  pointer-events: auto;
  background: #fff;
  box-shadow: 0 6px 32px 0 rgba(31,56,88,0.13);
  border-radius: 16px;
  padding: 18px 18px 14px 18px;
  display: flex;
  gap: 12px;
  align-items: center;
  min-width: 260px;
  max-width: 340px;
  margin: 0 auto;
  transition: box-shadow 0.2s;
}
.trace-input-fab {
  flex: 1;
  font-size: 16px;
  height: 38px;
  color: var(--color-text-1);
}
.trace-btn-fab {
  height: 38px;
  font-size: 16px;
  padding: 0 18px;
  border-radius: 8px;
}
.fab-fade-enter-active, .fab-fade-leave-active {
  transition: all 0.22s cubic-bezier(.4,0,.2,1);
}
.fab-fade-enter-from, .fab-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}
@media (max-width: 600px) {
  .search-fab-outer-top {
    top: 10px;
  }
  .search-fab-box-top {
    min-width: 120px;
    max-width: 98vw;
    padding: 10px 6px 8px 6px;
  }
  .trace-input-fab, .trace-btn-fab {
    font-size: 13px;
    height: 32px;
  }
}
.log-error {
  color: rgb(var(--red-6));
  background: rgb(var(--red-1));
  border: 1px solid rgb(var(--red-6));
  border-radius: 6px;
  padding: 8px 16px;
  margin: 16px auto;
  max-width: 600px;
  text-align: center;
}
.log-result {
  background: var(--color-fill-2);
  border-radius: 8px;
  margin: 16px auto;
  max-width: 900px;
  padding: 16px;
  font-size: 14px;
  color: rgb(var(--green-6));
  overflow-x: auto;
}
.log-env {
  color: rgb(var(--green-6));
  background: rgb(var(--green-1));
  border-radius: 6px;
  padding: 6px 16px;
  margin: 12px auto 0 auto;
  max-width: 400px;
  text-align: center;
  font-size: 15px;
}

/* 查询进度样式 */
.search-progress {
  position: fixed;
  top: 80px;
  left: 0;
  right: 0;
  z-index: 250;
  display: flex;
  justify-content: center;
  padding: 0 20px;
}

.progress-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 16px 20px;
  box-shadow: 0 4px 20px rgba(31, 56, 88, 0.15);
  width: 100%;
  max-width: 500px;
  animation: slideDown 0.3s ease;
}

.progress-status {
  font-size: 16px;
  font-weight: 500;
  color: rgb(var(--green-6));
  margin-bottom: 12px;
}

.progress-detail {
  font-size: 13px;
  color: rgb(var(--gray-7));
  margin-top: 8px;
  text-align: right;
}

@keyframes slideDown {
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}
</style> 