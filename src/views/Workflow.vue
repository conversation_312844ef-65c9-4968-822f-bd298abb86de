<template>
  <div class="workflow-page">
    <div v-if="loading" class="loading-overlay">
      <a-spin size="large">
        <template #icon><icon-loading /></template>
        <template #tip><span class="loading-text">加载工作流数据中...</span></template>
      </a-spin>
    </div>
    <div v-else-if="error" class="error-overlay">
      <a-result status="error" :subtitle="error">
        <template #extra>
          <a-button type="primary" status="danger" @click="fetchWorkflowData">
            重试
          </a-button>
        </template>
      </a-result>
    </div>
    <WorkflowViewer v-else :key="workflowDataKey" :initialData="workflowData" />
  </div>
</template>

<script setup>
import { onMounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import WorkflowViewer from '../components/WorkflowViewer.vue'
import { IconLoading } from '@arco-design/web-vue/es/icon'
import {getWorkflowGraph} from "../api/workflow/index.js";
import { getWorkflowData, saveWorkflowData } from '../utils/storage'
import {Message} from "@arco-design/web-vue";

const route = useRoute()
const clientKey = ref(route.query.clientKey)
const workflowData = ref(null)
const workflowDataKey = ref(0) // 添加一个 key 用于强制组件重新渲染
const loading = ref(true)
const error = ref(null)

// 默认空工作流数据
const defaultWorkflowData = {
  nodes: [],
  edges: []
}

// 获取工作流数据
const fetchWorkflowData = async () => {
  // 如果是新建工作流页面，直接使用默认空值
  if (route.path === '/new-workflow') {
    workflowData.value = defaultWorkflowData
    workflowDataKey.value++ // 更新 key 强制重新渲染
    loading.value = false
    return
  }

  // 如果有 refresh 参数，强制从服务器获取新数据
  if (route.query.refresh === 'true') {
    console.log('检测到 refresh 参数，强制从服务器获取新数据')
    loading.value = true
    error.value = null
    
    try {
      const response = await getWorkflowGraph(clientKey.value)
      if (response && response.data) {
        workflowData.value = response.data
        workflowDataKey.value++ // 更新 key 强制重新渲染
        // 更新 localStorage 中的数据
        saveWorkflowData(workflowData.value, clientKey.value)
        loading.value = false
        return
      }
    } catch (err) {
      console.error('强制刷新数据失败:', err)
    }
  }

  // 优先从 localStorage 获取数据
  const storedData = getWorkflowData(clientKey.value)
  if (storedData) {
    console.log('从 localStorage 获取到工作流数据')
    workflowData.value = storedData
    workflowDataKey.value++ // 更新 key 强制重新渲染
    loading.value = false
    return
  }

  // 如果没有本地数据且有 clientKey，则从服务器获取
  if (clientKey.value) {
    loading.value = true
    error.value = null
    
    try {
      console.log('开始从服务器获取工作流数据, clientKey:', clientKey.value)
      const response = await getWorkflowGraph(clientKey.value)
      console.log('工作流数据响应:', response)
      
      if (response && response.data) {
        workflowData.value = response.data
        workflowDataKey.value++ // 更新 key 强制重新渲染
        console.log('设置工作流数据:', workflowData.value)
        
        // 将工作流数据存储到 localStorage
        saveWorkflowData(workflowData.value, clientKey.value)
        console.log('工作流数据已存储到 localStorage')
      } else {
        console.error('工作流数据响应格式不符合预期:', response)
        error.value = '获取工作流数据失败'
      }
    } catch (err) {
      console.error('获取工作流数据出错:', err)
      error.value = `获取工作流数据时发生错误: ${err.message}`
    } finally {
      loading.value = false
    }
  } else {
    console.warn('未提供 clientKey，无法获取工作流数据')
    loading.value = false
  }
}

// 监听 clientKey 变化，重新获取数据
watch(clientKey, (newValue) => {
  if (newValue) {
    fetchWorkflowData()
  }
})

// 监听路由参数变化
watch(() => route.query, (newQuery) => {
  if (newQuery.refresh === 'true') {
    fetchWorkflowData()
  }
}, { immediate: true })

onMounted(() => {
  fetchWorkflowData()
})
</script>

<style scoped>
.workflow-page {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.loading-overlay, .error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: var(--color-bg-1);
  z-index: 1000;
}

.loading-text {
  margin-top: 16px;
  color: var(--color-text-2);
}
</style> 