<template>
  <div class="compare-container">
    <div class="compare-header">
      <button class="back-button" @click="goBack">
        <span class="back-icon">←</span> 返回
      </button>
      <h1 class="page-title">
        {{ route.query.source === 'editor' 
          ? '当前编辑与原始版本对比' 
          : `工作流版本对比 (${route.query.versionInfo || '历史版本'} vs 当前版本)` }}
      </h1>
      <div class="header-actions">
        <a-button 
          v-if="route.query.source === 'editor'"
          class="action-btn" 
          type="secondary" 
          @click="handleUpdate"
          :disabled="diffOldString === diffNewString"
          :title="diffOldString === diffNewString ? '当前没有修改内容' : '可以更新'"
        >
          <template #icon><icon-sync /></template>
          更新
        </a-button>
        <a-button 
          v-else
          class="action-btn" 
          type="secondary" 
          @click="handleRollback"
          :disabled="diffOldString === diffNewString"
          :title="diffOldString === diffNewString ? '当前没有修改内容' : '可以回滚'"
        >
          <template #icon><icon-sync /></template>
          回滚到此版本
        </a-button>
        <a-button class="history-btn" type="secondary" @click="viewHistory">
          <template #icon><icon-history /></template>
          查看更新记录
        </a-button>
      </div>
    </div>
    
    <div class="compare-content">
      <div v-if="loading" class="loading-indicator">
        <div class="spinner"></div>
        <span>加载对比数据中...</span>
      </div>
      
      <div v-else-if="error" class="error-message">
        <p>{{ error }}</p>
        <button @click="fetchData" class="retry-btn">重试</button>
      </div>
      
      <div v-else class="compare-outer-wrapper">
        <!-- 标题行 -->
        <div class="compare-header-row">
          <div class="version-header">
            {{ route.query.source === 'editor' ? '原始版本' : '历史版本' }}
          </div>
          <div class="version-header">
            {{ route.query.source === 'editor' ? '当前编辑版本' : '当前版本' }}
          </div>
        </div>
        
        <!-- 内容区域 -->
        <div class="compare-scroll-wrapper">
          <div class="compare-wrapper">
            <!-- 使用 CodeDiff 进行对比 -->
            <CodeDiff
              language="json"
              :old-string="diffOldString"
              :new-string="diffNewString"
              output-format="side-by-side"
              :force-inline-comparison="true"
              @diff="handleDiff"
            />
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 添加更新确认对话框 -->
  <a-modal
    v-model:visible="updateModalVisible"
    title="确认更新"
    @ok="confirmUpdate"
    @cancel="cancelUpdate"
    :ok-button-props="{ disabled: !updateForm.updateBy || !updateForm.description }"
  >
    <a-form :model="updateForm" layout="vertical">
      <a-form-item
        field="updateBy"
        label="修改人"
        :rules="[{ required: true, message: '请输入修改人姓名' }]"
        :validate-trigger="['change', 'blur']"
      >
        <a-input
          v-model="updateForm.updateBy"
          placeholder="请输入修改人姓名"
          allow-clear
        />
      </a-form-item>
      <a-form-item
        field="description"
        label="修改内容"
        :rules="[{ required: true, message: '请输入修改内容' }]"
        :validate-trigger="['change', 'blur']"
      >
        <a-textarea
          v-model="updateForm.description"
          placeholder="请输入修改内容描述"
          allow-clear
          :auto-size="{ minRows: 3, maxRows: 5 }"
        />
      </a-form-item>
    </a-form>
  </a-modal>

  <!-- 回滚确认对话框 -->
  <a-modal
    v-model:visible="rollbackModalVisible"
    title="确认回滚"
    @ok="confirmRollback"
    @cancel="cancelRollback"
  >
    <p>确定要回滚到该版本吗？此操作不可逆。</p>
  </a-modal>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { IconHistory, IconSync } from '@arco-design/web-vue/es/icon'
import { Message } from '@arco-design/web-vue'
import {getWorkflowCacheDetail, getWorkflowGraph, updateWorkflow, cacheUpdateWorkflow, rollbackWorkflow} from '../api/workflow'
import { CodeDiff } from 'v-code-diff'
import { clearWorkflowData, saveWorkflowData, saveCurrentWorkflowJson } from '../utils/storage'

const route = useRoute()
const router = useRouter()
const clientKey = route.query.clientKey
const cacheId = route.query.cacheId
const versionInfo = route.query.versionInfo

const loading = ref(true)
const error = ref(null)
const currentData = ref(null)
const cacheData = ref(null)
const diffResult = ref({})

// 添加更新表单相关的状态
const updateModalVisible = ref(false)
const updateForm = ref({
  updateBy: '',
  description: ''
})

const rollbackModalVisible = ref(false)
const rollbackLoading = ref(false)

// 对工作流数据进行排序并过滤不必要的字段
const sortWorkflowData = (data) => {
  if (!data) return {}
  
  try {
    const result = {}
    const orderedKeys = ['edges','nodes']
    
    for (const key of orderedKeys) {
      if (data[key]) {
        if (Array.isArray(data[key])) {
          result[key] = [...data[key]].map(item => {
            // 对于 nodes，保留 id、data 和 position 字段，并对 data 进行排序
            if (key === 'nodes') {
              return {
                id: item.id,
                position: {
                  x: Number(item.position.x.toFixed(2)),
                  y: Number(item.position.y.toFixed(2))
                },
                data: item.data
              }
            }
            // 对于 edges，确保字段顺序一致
            if (key === 'edges') {
              return {
                id: item.id,
                source: item.source,
                target: item.target,
                sourceHandle: item.sourceHandle,
                targetHandle: item.targetHandle,
                type: item.type
              }
            }
            return { ...item }
          }).sort((a, b) => a.id.localeCompare(b.id))
        } else {
          result[key] = data[key]
        }
      }
    }
    
    for (const key in data) {
      if (!orderedKeys.includes(key)) {
        result[key] = data[key]
      }
    }
    
    return result
  } catch (error) {
    console.error('数据处理错误:', error)
    return {}
  }
}

// 对对象属性进行排序
const sortObject = (obj) => {
  if (!obj || typeof obj !== 'object') return obj
  
  if (Array.isArray(obj)) {
    return obj.map(item => sortObject(item))
  }
  
  const sorted = {}
  Object.keys(obj)
    .sort()
    .forEach(key => {
      if (typeof obj[key] === 'object' && obj[key] !== null) {
        sorted[key] = sortObject(obj[key])
      } else {
        sorted[key] = obj[key]
      }
    })
  
  return sorted
}

// 计算排序后的数据
const sortedCurrentData = computed(() => sortWorkflowData(currentData.value))
const sortedCacheData = computed(() => sortWorkflowData(cacheData.value))

// 处理差异比较结果
const handleDiff = (diff) => {
  console.log('差异比较结果:', diff)
  // 保存差异结果
  diffResult.value = diff
}

// 检查是否有差异
const hasChanges = computed(() => {
  // 直接比较新旧字符串是否相同
  return diffOldString.value !== diffNewString.value
})

// 修改计算属性，使用排序后的数据
const diffOldString = computed(() => {
  try {
    return JSON.stringify(sortedCacheData.value, null, 2) || '{}'
  } catch (error) {
    return '{}'
  }
})

const diffNewString = computed(() => {
  try {
    return JSON.stringify(sortedCurrentData.value, null, 2) || '{}'
  } catch (error) {
    return '{}'
  }
})

// 修改获取数据的逻辑
const fetchData = async () => {
  loading.value = true
  error.value = null
  
  try {
    const source = route.query.source
    
    if (source === 'editor') {
      // 从 localStorage 获取当前工作流数据
      const currentWorkflowJson = localStorage.getItem(`current_workflow_json_${clientKey}`)
      
      if (!currentWorkflowJson) {
        throw new Error('未找到当前工作流数据')
      }
      
      // 获取服务器上的原始数据
      const originalResponse = await getWorkflowGraph(clientKey)
      
      if (originalResponse?.code === 200) {
        // 交换数据顺序：原始数据放在左边(cacheData)，当前编辑数据放在右边(currentData)
        cacheData.value = originalResponse.data
        currentData.value = JSON.parse(currentWorkflowJson)
      } else {
        throw new Error('获取原始数据失败: ' + (originalResponse?.message || '未知错误'))
      }
    } else {
      // 历史版本对比的逻辑
      const [currentResponse, cacheResponse] = await Promise.all([
        getWorkflowGraph(clientKey),
        getWorkflowCacheDetail(clientKey, cacheId)
      ])
      
      if (currentResponse?.code === 200 && cacheResponse?.code === 200) {
        // 交换数据顺序：历史版本放在左边(cacheData)，当前版本放在右边(currentData)
        cacheData.value = cacheResponse.data
        currentData.value = currentResponse.data
      } else {
        error.value = '获取数据失败: ' + 
          (currentResponse?.message || cacheResponse?.message || '未知错误')
      }
    }
  } catch (err) {
    console.error('获取对比数据失败:', err)
    error.value = err.message || '获取数据失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 返回上一页
const goBack = () => {
  const cacheId = route.query.cacheId
  if (cacheId) {
    // 如果有cacheId，说明是从历史页面对比进入的，返回到历史页面
    router.push({
      path: `/workflow-history/${clientKey}`,
      query: {
        source: 'compare',
        from: 'history'
      }
    })
  } else {
    // 其他情况（包括从workflow进入和从历史页面查看历史进入）返回到workflow页面
    router.push({
      path: '/workflow',
      query: { clientKey }
    })
  }
}

// 添加查看历史记录方法
const viewHistory = () => {
  const clientKey = route.query.clientKey
  if (!clientKey) {
    console.error('缺少 clientKey 参数')
    return
  }
  router.push({
    path: `/workflow-history/${clientKey}`,
    query: {
      source: 'compare',
      from: 'editor'
    }
  })
}

// 修改更新方法
const handleUpdate = async () => {
  // 如果新旧内容完全相同，显示提示并返回
  if (diffOldString.value === diffNewString.value) {
    Message.warning('当前没有任何修改，无需更新')
    return
  }

  // 显示更新确认对话框
  updateModalVisible.value = true
}

// 确认更新
const confirmUpdate = async () => {
  if (!updateForm.value.updateBy || !updateForm.value.description) {
    Message.error('请填写完整的修改信息')
    return
  }

  try {
    // 使用原始数据而不是排序后的数据进行更新
    const updateData = {
      clientKey,
      description: updateForm.value.description,
      updateBy: updateForm.value.updateBy,
      graphString: JSON.stringify(currentData.value, null, 2)
    }

    // 调用更新接口
    const response = await updateWorkflow(updateData)

    if (response?.code === 200) {
      Message.success('更新成功')
      updateModalVisible.value = false
      // 清空表单
      updateForm.value = {
        updateBy: '',
        description: ''
      }
      
      // 跳转到工作流页面
      router.push({
        path: '/workflow',
        query: { 
          clientKey,
          refresh: 'true',
          fitView: 'true'
        }
      })
    } else {
      throw new Error(response?.message || '更新失败')
    }
  } catch (err) {
    Message.error('更新失败：' + (err.message || '未知错误'))
  }
}

// 取消更新
const cancelUpdate = () => {
  updateModalVisible.value = false
  // 清空表单
  updateForm.value = {
    updateBy: '',
    description: ''
  }
}

// 处理回滚
const handleRollback = () => {
  rollbackModalVisible.value = true
}

// 确认回滚
const confirmRollback = async () => {
  if (!clientKey || !cacheId) return
  
  rollbackLoading.value = true
  try {
    const response = await rollbackWorkflow({
      clientKey,
      id: cacheId
    })

    if (response?.code === 200) {
      Message.success('回滚成功')
      
      // 更新 current_workflow_json
      if (diffNewString.value) {
        try {
          const workflowData = JSON.parse(diffNewString.value)
          saveCurrentWorkflowJson(workflowData, clientKey)
          saveWorkflowData(workflowData, clientKey)
        } catch (err) {
          console.error('更新工作流数据失败:', err)
        }
      }
      
      // 跳转到工作流页面
      router.push({
        path: '/workflow',
        query: { 
          clientKey,
          refresh: 'true',
          fitView: 'true'
        }
      })
    } else {
      throw new Error(response?.message || '回滚失败')
    }
  } catch (err) {
    Message.error('回滚失败：' + (err.message || '未知错误'))
  } finally {
    rollbackLoading.value = false
    rollbackModalVisible.value = false
  }
}

// 取消回滚
const cancelRollback = () => {
  rollbackModalVisible.value = false
}

onMounted(() => {
  // 修改验证逻辑
  if (!clientKey) {
    error.value = '缺少客户端标识'
    loading.value = false
    return
  }
  
  // 如果不是来自编辑器，则需要验证 cacheId
  if (route.query.source !== 'editor' && !cacheId) {
    error.value = '缺少缓存标识'
    loading.value = false
    return
  }
  
  fetchData()
})
</script>

<style scoped>
.compare-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  overflow: hidden;
}

.compare-header {
  display: flex;
  align-items: center;
  padding: 1rem 2rem;
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.back-button {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: #2196f3;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s;
}

.back-button:hover {
  color: #0d8aee;
  transform: translateX(-3px);
}

.back-icon {
  font-size: 1.2rem;
  margin-right: 0.5rem;
}

.page-title {
  flex: 1;
  font-size: 1.8rem;
  color: #333;
  margin: 0 0 0 1rem;
}

.compare-content {
  flex: 1;
  padding: 2rem;
  overflow: hidden;
  height: calc(100vh - 80px); /* 减去头部高度 */
}

.compare-outer-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.compare-header-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  padding: 0 0 0.5rem 0;
}

.version-header {
  font-size: 1.2rem;
  font-weight: bold;
  color: #333;
  padding: 0 1.5rem;
}

.compare-scroll-wrapper {
  flex: 1;
  overflow-y: auto;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.compare-wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  overflow: hidden;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(33, 150, 243, 0.3);
  border-radius: 50%;
  border-top-color: #2196f3;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error-message {
  text-align: center;
  color: #f44336;
  padding: 2rem;
  background-color: white;
  border-radius: 8px;
  margin: 2rem auto;
  max-width: 600px;
}

.retry-btn {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 0.7rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 1rem;
  transition: background-color 0.3s;
}

.retry-btn:hover {
  background-color: #d32f2f;
}

/* v-code-diff 样式调整 */
:deep(.v-code-diff) {
  border-radius: 8px;
  overflow: hidden;
  font-family: 'Monaco', 'Menlo', 'Consolas', 'Courier New', monospace;
  min-height: 200px;
  height: 100%;
  background-color: #f8f9fa;
}

:deep(.v-code-diff pre) {
  margin: 0;
  padding: 1rem;
}

:deep(.v-code-diff .line-number) {
  color: #6c757d;
  padding: 0 0.5rem;
}

:deep(.v-code-diff .addition) {
  background-color: #e6ffed;
}

:deep(.v-code-diff .deletion) {
  background-color: #ffeef0;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .compare-header-row,
  .compare-wrapper {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .compare-header {
    padding: 1rem;
  }
  
  .page-title {
    font-size: 1.5rem;
  }
  
  .compare-content {
    padding: 1rem;
  }
}

.error-boundary {
  padding: 1rem;
  color: #721c24;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  margin: 1rem 0;
}

/* 移除不需要的样式 */
.json-details,
.version-container,
.diff-analysis,
:deep(.json-viewer) {
  display: none;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-left: auto;
  margin-right: 20px;
}

.header-actions :deep(.arco-btn) {
  display: flex;
  align-items: center;
  gap: 4px;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  transition: all 0.3s;
}

.header-actions :deep(.arco-btn.action-btn) {
  color: #fff;
  border-color: #165dff;
  background-color: #165dff;
}

.header-actions :deep(.arco-btn.action-btn:hover) {
  color: #fff;
  border-color: #0e42d2;
  background-color: #0e42d2;
}

.header-actions :deep(.arco-btn.action-btn:disabled) {
  cursor: not-allowed;
  opacity: 0.6;
  color: #fff;
  border-color: #94bfff;
  background-color: #94bfff;
}

.header-actions :deep(.arco-btn.action-btn:disabled:hover) {
  color: #fff;
  border-color: #94bfff;
  background-color: #94bfff;
}

.header-actions :deep(.arco-btn.history-btn) {
  color: #fff;
  border-color: #ff7d00;
  background-color: #ff7d00;
}

.header-actions :deep(.arco-btn.history-btn:hover) {
  color: #fff;
  border-color: #d25f00;
  background-color: #d25f00;
}
</style> 