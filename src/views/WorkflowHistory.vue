<template>
  <div class="history-container">
    <!-- 页面头部 -->
    <div class="history-header">
      <button class="back-button" @click="goBack">
        <span class="back-icon">←</span> 返回
      </button>
      <h1 class="page-title">工作流更新记录</h1>
    </div>

    <!-- 页面内容 -->
    <div class="history-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-indicator">
        <a-spin size="large">
          <template #tip><span class="loading-text">加载数据中...</span></template>
        </a-spin>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-message">
        <a-result status="error" :subtitle="error">
          <template #extra>
            <a-button type="primary" @click="fetchHistoryData">
              重试
            </a-button>
          </template>
        </a-result>
      </div>

      <!-- 版本列表 -->
      <div v-else class="history-list">
        <a-table
          :data="historyList"
          :columns="columns"
          :pagination="pagination"
          :bordered="false"
          :stripe="true"
          :scroll="{ x: '100%' }"
          @page-change="handlePageChange"
          @page-size-change="handlePageSizeChange"
        >
          <!-- 版本号列 -->
          <template #index="{ rowIndex }">
            <span class="version-tag">v{{ pagination.total - rowIndex }}</span>
          </template>

          <!-- 更新时间列 -->
          <template #updateTime="{ record }">
            {{ record.updateTime }}
          </template>

          <!-- 修改人列 -->
          <template #updateBy="{ record }">
            <span class="update-by">{{ record.updateBy || '-' }}</span>
          </template>

          <!-- 操作列 -->
          <template #actions="{ record }">
            <a-space>
              <a-button
                type="primary"
                size="small"
                @click="handleCompare(record)"
                class="action-btn compare-btn"
              >
                与当前版本对比
              </a-button>
              <a-button
                type="outline"
                status="danger"
                size="small"
                @click="handleRollback(record)"
                :loading="rollbackLoading === record.id"
                class="action-btn rollback-btn"
              >
                回滚到此版本
              </a-button>
            </a-space>
          </template>
        </a-table>
      </div>

      <!-- 回滚确认对话框 -->
      <a-modal
        v-model:visible="rollbackVisible"
        title="回滚确认"
        @ok="confirmRollback"
        @cancel="cancelRollback"
      >
        <p>确定要回滚到版本 v{{ selectedVersion?.versionNumber }} 吗？</p>
        <p class="warning-text">此操作将覆盖当前工作流，且不可撤销！</p>
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getWorkflowCache, rollbackWorkflow } from '../api/workflow'
import { saveCurrentWorkflowJson } from '../utils/storage'
import 'vue-json-pretty/lib/styles.css'
import { Message } from '@arco-design/web-vue'

const route = useRoute()
const router = useRouter()
const clientKey = route.params.clientKey

// 状态管理
const loading = ref(false)
const error = ref(null)
const historyList = ref([])
const rollbackVisible = ref(false)
const rollbackLoading = ref(null)
const selectedVersion = ref(null)

// 分页配置
const pagination = ref({
  total: 0,
  current: 1,
  pageSize: 10,
  showTotal: true,
  showJumper: true,
  showPageSize: true,
  pageSizeOptions: [10, 20, 30]
})

// 表格列定义
const columns = [
  {
    title: '版本号',
    slotName: 'index',
    width: 100,
    fixed: 'left'
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    slotName: 'updateTime',
    width: 200,
  },
  {
    title: '修改人',
    dataIndex: 'updateBy',
    slotName: 'updateBy',
    width: 120,
  },
  {
    title: '描述',
    dataIndex: 'description',
    ellipsis: true,
    width: 300,
  },
  {
    title: '操作',
    slotName: 'actions',
    width: 300,
    fixed: 'right',
  },
]

// 版本详情描述项
const versionDetails = computed(() => {
  if (!selectedVersion.value) return []
  
  return [
    {
      label: '版本号',
      value: 'v' + selectedVersion.value.version,
    },
    {
      label: '修改时间',
      value: formatDate(selectedVersion.value.updateTime),
    },
    {
      label: '修改人',
      value: selectedVersion.value.operator,
    },
    {
      label: '备注',
      value: selectedVersion.value.remark || '-',
    },
  ]
})

// 获取历史记录数据
const fetchHistoryData = async () => {
  loading.value = true
  error.value = null
  
  try {
    const response = await getWorkflowCache(
      clientKey,
      pagination.value.pageSize,
      pagination.value.current
    )
    if (response?.code === 200) {
      // 设置总数
      pagination.value.total = response.data.total || 0
      // 设置当前页数据
      historyList.value = response.data.list || []
    } else {
      throw new Error(response?.message || '获取历史记录失败')
    }
  } catch (err) {
    console.error('获取历史记录失败:', err)
    error.value = err.message
  } finally {
    loading.value = false
  }
}

// 处理页码变化
const handlePageChange = (page) => {
  pagination.value.current = page
  fetchHistoryData()
}

// 处理每页条数变化
const handlePageSizeChange = (pageSize) => {
  pagination.value.pageSize = pageSize
  pagination.value.current = 1 // 重置到第一页
  fetchHistoryData()
}

// 版本对比
const handleCompare = (record) => {
  router.push({
    path: '/compare',
    query: {
      clientKey,
      cacheId: record.id,
      versionInfo: `v${record.version || '-'}-${formatDate(record.updateTime)}-${record.updateBy || '未知'}`
    }
  })
}

// 处理回滚
const handleRollback = (record) => {
  selectedVersion.value = {
    ...record,
    versionNumber: record.version || '-'
  }
  rollbackVisible.value = true
}

// 确认回滚
const confirmRollback = async () => {
  if (!selectedVersion.value) return
  
  rollbackLoading.value = selectedVersion.value.id
  try {
    // 调用回滚 API
    const response = await rollbackWorkflow({
      clientKey,
      id: selectedVersion.value.id
    })

    if (response?.code === 200) {
      Message.success('回滚成功')
      
      if (selectedVersion.value.graphString) {
        try {
          const workflowData = JSON.parse(selectedVersion.value.graphString)
          saveCurrentWorkflowJson(workflowData)
        } catch (err) {
          console.error('更新工作流数据失败:', err)
        }
      }
      
      // 跳转到工作流页面，并刷新缓存
      router.push({
        path: '/workflow',
        query: { 
          clientKey,
          refresh: 'true',
          fitView: 'true'
        }
      })
    } else {
      throw new Error(response?.message || '回滚失败')
    }
  } catch (err) {
    Message.error('回滚失败：' + (err.message || '未知错误'))
  } finally {
    rollbackLoading.value = null
    rollbackVisible.value = false
    selectedVersion.value = null
  }
}

// 取消回滚
const cancelRollback = () => {
  rollbackVisible.value = false
  selectedVersion.value = null
}

// 返回上一页
const goBack = () => {
  // 直接返回到编辑器对比页面
  router.push({
    path: '/compare',
    query: {
      clientKey,
      source: 'editor'
    }
  })
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })
}

// 生命周期钩子
onMounted(() => {
  if (!clientKey) {
    error.value = '缺少客户端标识'
    return
  }
  fetchHistoryData()
})
</script>

<style scoped>
.history-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.history-header {
  display: flex;
  align-items: center;
  padding: 16px 24px;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.back-button {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: #2196f3;
  cursor: pointer;
  padding: 8px;
  transition: all 0.3s;
}

.back-button:hover {
  color: #1976d2;
}

.back-icon {
  font-size: 20px;
  margin-right: 4px;
}

.page-title {
  margin: 0 0 0 16px;
  font-size: 20px;
  color: #333;
}

.history-content {
  flex: 1;
  padding: 24px;
  overflow: auto;
}

.history-list {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 16px;
  width: 100%;
  overflow-x: auto;
}

.version-tag {
  display: inline-block;
  padding: 4px 8px;
  background-color: #e3f2fd;
  border-radius: 4px;
  color: #1976d2;
  font-family: monospace;
}

.update-by {
  color: #666;
  font-weight: 500;
}

.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.error-message {
  padding: 40px;
}

.warning-text {
  color: #ff4d4f;
  margin-top: 8px;
}

.history-list :deep(.arco-table) .arco-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  border-radius: 4px;
  padding: 4px 12px;
  font-size: 14px;
  transition: all 0.3s;
}

/* 对比按钮样式 */
.history-list :deep(.arco-table) .arco-btn.compare-btn {
  color: #fff;
  border-color: #165dff;
  background-color: #165dff;
}

.history-list :deep(.arco-table) .arco-btn.compare-btn:hover {
  color: #fff;
  border-color: #0e42d2;
  background-color: #0e42d2;
}

/* 回滚按钮样式 */
.history-list :deep(.arco-table) .arco-btn.rollback-btn {
  color: #ff4d4f;
  border-color: #ff4d4f;
  background-color: transparent;
}

.history-list :deep(.arco-table) .arco-btn.rollback-btn:hover {
  color: #fff;
  border-color: #ff4d4f;
  background-color: #ff4d4f;
}

.history-list :deep(.arco-table) .arco-btn[loading] {
  opacity: 0.8;
}

:deep(.arco-table-container) {
  overflow-x: auto !important;
}

:deep(.arco-table-body) {
  overflow-x: auto !important;
}

:deep(.arco-table-th) {
  background-color: var(--color-fill-2) !important;
}

:deep(.arco-drawer-header) {
  border-bottom: 1px solid var(--color-border);
}

:deep(.arco-descriptions-item-label) {
  width: 100px;
}
</style> 