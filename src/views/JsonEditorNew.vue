<template>
  <div class="json-editor-page">
    <div class="editor-header">
      <div class="header-left">
        <a-button class="back-button" type="outline" @click="goBack">
          <template #icon><icon-left /></template>
          返回
        </a-button>
        <h1 class="page-title">JSON 编辑器</h1>
        
        <!-- 编辑器指示器 -->
        <div class="editor-indicators">
          <a-space>
            <a-button 
              v-for="(_, index) in editors" 
              :key="index"
              size="small"
              :type="currentEditorIndex === index ? 'primary' : 'outline'"
              @click="scrollToEditor(index)"
            >
              {{ index + 1 }}
            </a-button>
          </a-space>
        </div>
      </div>
      <div class="header-right">
        <a-button type="primary" status="success" @click="saveJson" style="margin-right: 12px;">
          <template #icon><icon-save /></template>
          下载JSON
        </a-button>
        <a-space>
          <a-dropdown @select="addEditorWithFormat">
            <a-button type="primary">
              <template #icon><icon-plus /></template>
              添加编辑器
              <icon-down />
            </a-button>
            <template #content>
              <a-doption value="json">JSON 编辑器</a-doption>
              <a-doption value="yaml">YAML 编辑器</a-doption>
              <a-doption value="markdown">Markdown 编辑器</a-doption>
            </template>
          </a-dropdown>
          <a-button type="primary" status="warning" @click="removeLastEditor" :disabled="editors.length <= 1">
            <template #icon><icon-minus /></template>
            减少编辑器
          </a-button>
        </a-space>
      </div>
    </div>
    
    <div class="editor-main">
      <div class="editors-container" ref="editorsContainer">
        <div v-for="(editor, index) in editors" :key="`editor-${index}`" class="editor-section" :ref="el => { if (el) editorRefs[index] = el }">
          <div class="editor-header-section">
            <div class="editor-title">
              编辑器 {{ index + 1 }} 
              <span class="format-badge">{{ (editor.format || 'json').toUpperCase() }}</span>
            </div>
          </div>
          <MultiFormatEditor
            v-model="editor.data"
            :initial-format="editor.format || 'json'"
            @change="(newData) => handleJsonChange(index, newData)"
            @format-change="(newFormat) => handleFormatChange(index, newFormat)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { IconLeft, IconSave, IconPlus, IconMinus, IconDown } from '@arco-design/web-vue/es/icon'
import { Message } from '@arco-design/web-vue'
import MultiFormatEditor from '@/components/editors/common/MultiFormatEditor.vue'

const router = useRouter()
const editors = ref([
  { data: {}, format: 'json' },
  { data: {}, format: 'yaml' }
])
const currentEditorIndex = ref(0)
const editorsContainer = ref(null)
const editorRefs = reactive({})

// 返回上一页
const goBack = () => {
  router.push('/')
}

// 处理JSON变化
const handleJsonChange = (index, newData) => {
  console.log(`JSON数据${index + 1}已更改:`, newData)
}

// 处理格式变化
const handleFormatChange = (index, newFormat) => {
  console.log(`[DEBUG] 接收到格式变化事件 - 编辑器${index + 1}, 新格式: ${newFormat}, 当前格式: ${editors.value[index].format}`)
  // 只更新格式，不触发数据变化
  if (editors.value[index].format !== newFormat) {
    editors.value[index].format = newFormat
    console.log(`[DEBUG] 格式更新后 - 编辑器${index + 1}格式:`, editors.value[index].format)
  }
}

// 滚动到指定编辑器
const scrollToEditor = (index) => {
  currentEditorIndex.value = index
  
  if (editorRefs[index]) {
    const container = editorsContainer.value
    const editorElement = editorRefs[index]
    
    if (container && editorElement) {
      container.scrollTo({
        left: editorElement.offsetLeft - container.offsetLeft,
        behavior: 'smooth'
      })
    }
  }
}

// 添加指定格式的编辑器
const addEditorWithFormat = (format) => {
  if (editors.value.length >= 20) {
    Message.warning('最多只能添加20个编辑器')
    return
  }
  
  const defaultData = getDefaultDataForFormat(format)
  editors.value.push({ data: defaultData, format })
  Message.success(`已添加新的 ${format.toUpperCase()} 编辑器`)
  
  // 添加后自动滚动到新编辑器位置
  setTimeout(() => {
    const newIndex = editors.value.length - 1
    scrollToEditor(newIndex)
  }, 100)
}

// 获取格式的默认数据
const getDefaultDataForFormat = (format) => {
  switch (format) {
    case 'json':
      return {}
    case 'yaml':
      return '# YAML content\n'
    case 'markdown':
      return '# Markdown\n\n内容...'
    default:
      return {}
  }
}

// 删除最后一个编辑器
const removeLastEditor = () => {
  if (editors.value.length > 1) {
    const lastIndex = editors.value.length - 1
    editors.value.pop()
    
    // 如果删除的是当前选中的编辑器，则选中前一个
    if (currentEditorIndex.value === lastIndex) {
      currentEditorIndex.value = lastIndex - 1
      scrollToEditor(currentEditorIndex.value)
    }
    
    Message.success('已删除编辑器')
  } else {
    Message.warning('至少保留一个编辑器')
  }
}

// 保存JSON
const saveJson = () => {
  try {
    // 将所有编辑器的JSON数据合并到一个对象中
    const jsonToSave = {}
    
    editors.value.forEach((editor, index) => {
      jsonToSave[`editor${index + 1}`] = editor.data
    })
    
    const jsonString = JSON.stringify(jsonToSave, null, 2)
    const blob = new Blob([jsonString], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `workflow_data.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    Message.success('JSON保存成功')
  } catch (error) {
    Message.error(`保存失败: ${error.message}`)
  }
}

// 组件挂载时初始化
onMounted(() => {
  // 初始化已在ref中完成
})
</script>

<style scoped>
.json-editor-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--color-bg-1);
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: var(--color-bg-2);
  border-bottom: 1px solid var(--color-border);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.page-title {
  margin: 0;
  font-size: 1.5rem;
  color: var(--color-text-1);
}

.editor-indicators {
  margin-left: 16px;
}

.editor-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: visible;
  padding: 16px;
  min-height: 0;
}

.editors-container {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  flex: 1;
  gap: 16px;
  height: 100%;
  overflow-x: auto;
  padding-bottom: 12px; /* 为滚动条留出空间 */
  scroll-behavior: smooth;
}

.editor-section {
  flex: 0 0 calc(50% - 8px);
  display: flex;
  flex-direction: column;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  overflow: visible;
  min-height: 500px;
}

.editor-header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 8px;
}

.editor-title {
  padding: 8px 16px;
  font-weight: 500;
  background-color: var(--color-bg-2);
  border-bottom: 1px solid var(--color-border);
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.format-badge {
  display: inline-block;
  padding: 2px 8px;
  font-size: 12px;
  font-weight: 600;
  border-radius: 12px;
  background-color: var(--color-primary-light-1);
  color: var(--color-primary-6);
  text-transform: uppercase;
}

/* 自定义滚动条样式 */
.editors-container::-webkit-scrollbar {
  height: 8px;
}

.editors-container::-webkit-scrollbar-track {
  background: var(--color-bg-2);
  border-radius: 4px;
}

.editors-container::-webkit-scrollbar-thumb {
  background: var(--color-fill-3);
  border-radius: 4px;
}

.editors-container::-webkit-scrollbar-thumb:hover {
  background: var(--color-fill-4);
}
</style>