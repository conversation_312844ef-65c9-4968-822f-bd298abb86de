<template>
  <div class="details-container">
    <div class="details-header">
      <a-button class="back-button" type="text" @click="goBack">
        <template #icon><icon-left /></template>
        返回
      </a-button>
      <h1 class="page-title">{{ app ? app.name : '应用详情' }}</h1>
    </div>
    
    <div class="details-content">
      <div v-if="loading" class="loading-indicator">
        <a-spin dot size="large" />
        <span>加载应用详情中...</span>
      </div>
      
      <div v-else-if="error" class="error-message">
        <a-alert type="error" :content="error" />
        <a-button type="primary" status="danger" @click="fetchAppDetails" class="retry-btn">
          重试
        </a-button>
      </div>
      
      <div v-else-if="app" class="json-wrapper">
        <!-- 工作流 JSON 数据展示 -->
        <div class="workflow-json-section" v-if="workflowData">
          <div class="json-header">
            <h2 class="section-title">工作流 JSON 数据</h2>
            <div class="json-actions">
              <a-select 
                v-if="cacheList.length" 
                v-model="selectedCacheId"
                placeholder="选择版本"
                class="cache-select"
                @change="handleCacheSelect"
              >
                <a-option value="">当前版本</a-option>
                <a-option v-for="item in cacheList" :key="item.id" :value="item.id">
                  {{ item.description }}
                </a-option>
              </a-select>
              <a-button type="primary" @click="editWorkflow" class="edit-workflow-btn">
                编辑工作流
              </a-button>
              <a-button status="success" @click="copyWorkflowJson" class="copy-json-btn">
                <span v-if="!copySuccess">复制 JSON</span>
                <span v-else>复制成功 <icon-check /></span>
              </a-button>
            </div>
          </div>
          <div class="json-container">
            <!-- 使用 vue-json-pretty 组件 -->
            <vue-json-pretty
              :data="workflowData"
              :deep="5"
              :show-double-quotes="true"
              :show-length="true"
              :show-line="true"
              :show-icon="true"
              :line-numbers="true"
              :collapsed-on-click-brackets="true"
              class="json-viewer"
            />
          </div>
        </div>
        
        <!-- 工作流数据加载中 -->
        <div v-else-if="loadingWorkflow" class="workflow-loading">
          <a-spin dot />
          <span>加载工作流数据中...</span>
        </div>
        
        <!-- 工作流数据加载失败 -->
        <div v-else-if="workflowError" class="workflow-error">
          <a-alert type="error" :content="workflowError" />
          <a-button type="primary" status="danger" @click="fetchWorkflowData" class="retry-btn small-btn">
            重试
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getAppList } from '../api/app'
import { getWorkflowCache, getWorkflowGraph } from '../api/workflow'
import VueJsonPretty from 'vue-json-pretty'
import 'vue-json-pretty/lib/styles.css'
// 导入 Arco 图标
import { IconLeft, IconCheck } from '@arco-design/web-vue/es/icon'
import { saveWorkflowData } from '../utils/storage'

const route = useRoute()
const router = useRouter()
const clientKey = route.params.clientKey
const app = ref(null)
const loading = ref(true)
const error = ref(null)

// 工作流数据相关
const workflowData = ref(null)
const loadingWorkflow = ref(false)
const workflowError = ref(null)
const copySuccess = ref(false)

// 工作流缓存相关
const cacheList = ref([])
const selectedCacheId = ref('')

// 默认工作流数据
const defaultWorkflowData = {
  nodes: [],
  edges: []
}

// 格式化 JSON 显示
const formattedJson = computed(() => {
  if (!workflowData.value) return ''
  try {
    return JSON.stringify(workflowData.value, null, 2)
  } catch (err) {
    console.error('JSON 格式化错误:', err)
    return JSON.stringify(workflowData.value)
  }
})

// 模拟数据，用于测试
const mockData = {
  success: true,
  message: "操作成功！",
  code: 200,
  data: [
    {
      clientKey: "1",
      name: "AI技术助理",
      icon: "https://h5.myroki.com/img/logo.svg",
      description: "我知晓技术标准、经验案例，随时为你答疑解惑",
      agentId: "standard_assistant"
    },
    {
      clientKey: "2",
      name: "AI专利助理",
      icon: "https://h5.myroki.com/img/logo2.svg",
      description: "帮助您搜索、草拟、分析和管理专利",
      agentId: "130635006376615936"
    },
    {
      clientKey: "3",
      name: "AI产品助理",
      icon: "https://roki-ai-web-cdn.oss-cn-hangzhou.aliyuncs.com/wx-img/logo3.svg",
      description: "帮助您搜索、草拟、分析和管理专利",
      agentId: "product_assistant"
    },
    {
      clientKey: "5",
      name: "Roki食神",
      icon: "https://h5.myroki.com/img/IMG_8115.png",
      description: "为热爱烹饪的用户提供多样化的菜谱选择",
      agentId: "105085634255589376"
    },
    {
      clientKey: "7",
      name: "AI技术助理（自研）",
      icon: "https://h5.myroki.com/img/logo2.svg",
      description: "帮助您搜索、草拟、分析和管理专利",
      agentId: "112582798850535424"
    },
    {
      clientKey: "8",
      name: "AI产品助理（agent）",
      icon: "https://h5.myroki.com/img/logo.svg",
      description: "帮助您搜索、草拟、分析和管理专利",
      agentId: "999999"
    },
    {
      clientKey: "11",
      name: "魔盒V1",
      agentId: "333"
    },
    {
      clientKey: "13",
      name: "Roki食神-dev-测试",
      icon: "https://h5.myroki.com/img/IMG_8115.png",
      agentId: "888888"
    }
  ]
};

// 获取应用详情
const fetchAppDetails = async () => {
  // 如果是新建Agent，直接设置默认值
  if (clientKey === 'new') {
    app.value = {
      clientKey: 'new',
      name: '新建Agent',
      description: '创建一个新的智能体工作流'
    }
    workflowData.value = defaultWorkflowData
    loading.value = false
    return
  }

  loading.value = true
  error.value = null
  
  try {
    console.log('开始获取应用详情, clientKey:', clientKey)
    
    // 尝试从API获取数据
    let response;
    try {
      response = await getAppList()
      console.log('API 响应:', response)
    } catch (apiError) {
      console.warn('API请求失败，使用模拟数据:', apiError)
      response = mockData
    }
    
    // 检查响应结构并查找指定的应用
    if (response && response.data) {
      const appData = response.data.find(item => item.clientKey === clientKey)
      if (appData) {
        app.value = appData
        console.log('找到应用详情:', app.value)
        // 获取工作流数据
        fetchWorkflowData()
      } else {
        error.value = `未找到ID为 ${clientKey} 的应用`
        console.error('未找到应用:', clientKey)
      }
    } else {
      console.error('API 响应格式不符合预期:', response)
      // 尝试从模拟数据中查找
      const mockApp = mockData.data.find(item => item.clientKey === clientKey)
      if (mockApp) {
        app.value = mockApp
        console.log('使用模拟数据:', app.value)
        // 获取工作流数据
        fetchWorkflowData()
      } else {
        error.value = `未找到ID为 ${clientKey} 的应用`
      }
    }
  } catch (err) {
    console.error('获取应用详情出错:', err)
    error.value = `获取应用详情时发生错误: ${err.message}`
    
    // 尝试从模拟数据中查找
    const mockApp = mockData.data.find(item => item.clientKey === clientKey)
    if (mockApp) {
      app.value = mockApp
      // 获取工作流数据
      fetchWorkflowData()
    }
  } finally {
    loading.value = false
  }
}

// 获取工作流数据
const fetchWorkflowData = async () => {
  if (!app.value || !app.value.clientKey) return
  
  loadingWorkflow.value = true
  workflowError.value = null
  
  try {
    // 使用新的接口获取工作流图数据
    const response = await getWorkflowGraph(app.value.clientKey)
    
    if (response && response.code === 200 && response.data) {
      workflowData.value = response.data
    } else {
      workflowError.value = '获取工作流数据失败: ' + (response?.message || '未知错误')
    }
  } catch (err) {
    console.error('获取工作流数据失败:', err)
    workflowError.value = err.message || '获取工作流数据失败，请稍后重试'
  } finally {
    loadingWorkflow.value = false
  }
}

// 获取工作流缓存列表
const fetchWorkflowCache = async () => {
  try {
    const response = await getWorkflowCache(clientKey, 30, 1) // 获取前30条数据
    if (response?.code === 200 && response?.data) {
      // 处理新的数据结构，使用 list 字段
      cacheList.value = response.data.list || []
    }
  } catch (err) {
    console.error('获取工作流缓存列表失败:', err)
  }
}

// 处理缓存版本选择
const handleCacheSelect = async () => {
  if (!selectedCacheId.value) {
    // 如果选择"当前版本"，重新获取最新数据
    await fetchWorkflowData()
    return
  }
  
  // 跳转到比较页面
  router.push({
    path: '/compare',
    query: {
      clientKey,
      cacheId: selectedCacheId.value
    }
  })
}

// 返回上一页
const goBack = () => {
  router.push('/')
}

// 进入工作流编辑页面
const editWorkflow = () => {
  router.push(`/workflow?clientKey=${app.value.clientKey}`)
}

// 复制 JSON 数据 - 兼容HTTP环境
const copyWorkflowJson = async () => {
  if (!workflowData.value) return

  try {
    // 对 workflowData 进行排序处理
    const sortedData = {
      edges: (workflowData.value.edges || [])
        .map(edge => ({...edge}))
        .sort((a, b) => a.id.localeCompare(b.id)),
      nodes: (workflowData.value.nodes || [])
        .map(node => ({...node}))
        .sort((a, b) => a.id.localeCompare(b.id))
    }

    const jsonStr = JSON.stringify(sortedData, null, 2)

    // 优先使用现代 Clipboard API（HTTPS/localhost）
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(jsonStr)
      copySuccess.value = true
      setTimeout(() => {
        copySuccess.value = false
      }, 2000)
    } else {
      // 降级方案：使用传统方法（HTTP环境）
      const textArea = document.createElement('textarea')
      textArea.value = jsonStr
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()

      try {
        const successful = document.execCommand('copy')
        if (successful) {
          copySuccess.value = true
          setTimeout(() => {
            copySuccess.value = false
          }, 2000)
        } else {
          throw new Error('execCommand failed')
        }
      } catch (fallbackErr) {
        console.error('复制失败，请手动复制:', fallbackErr)
        alert('复制失败，请手动复制')
      } finally {
        document.body.removeChild(textArea)
      }
    }
  } catch (err) {
    console.error('复制 JSON 失败:', err)
    alert('复制失败，请手动复制')
  }
}

// 对对象属性进行排序
const sortObject = (obj) => {
  if (!obj || typeof obj !== 'object') return obj
  
  if (Array.isArray(obj)) {
    return obj.map(item => sortObject(item))
  }
  
  const sorted = {}
  Object.keys(obj)
    .sort()
    .forEach(key => {
      if (typeof obj[key] === 'object' && obj[key] !== null) {
        sorted[key] = sortObject(obj[key])
      } else {
        sorted[key] = obj[key]
      }
    })
  
  return sorted
}

onMounted(() => {
  fetchAppDetails()
  fetchWorkflowCache() // 获取缓存列表
})
</script>

<style scoped>
.details-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--color-bg-1);
}

.details-header {
  display: flex;
  align-items: center;
  padding: 1rem 2rem;
  background-color: var(--color-bg-2);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.back-button {
  display: flex;
  align-items: center;
  color: var(--color-text-2);
  font-size: 1rem;
  transition: all 0.3s;
}

.back-button:hover {
  color: rgb(var(--primary-6));
  transform: translateX(-3px);
}

.page-title {
  flex: 1;
  font-size: 1.8rem;
  color: var(--color-text-1);
  margin: 0 0 0 1rem;
}

.details-content {
  flex: 1;
  padding: 2rem;
  overflow: hidden;
  height: calc(100vh - 80px); /* 减去头部高度 */
}

.json-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.workflow-json-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.json-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.section-title {
  font-size: 1.5rem;
  color: var(--color-text-1);
  margin: 0;
}

.json-container {
  flex: 1;
  background-color: var(--color-bg-2);
  border-radius: 8px;
  padding: 1.5rem;
  overflow: auto;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  height: calc(100vh - 200px);
}

/* vue-json-pretty 自定义样式 */
.json-viewer {
  font-family: 'Monaco', 'Menlo', 'Consolas', 'Courier New', monospace;
  font-size: 14px;
  background-color: var(--color-bg-2);
  padding: 0;
}

.json-actions {
  display: flex;
  gap: 10px;
}

.loading-indicator, .workflow-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  height: 100%;
  gap: 16px;
}

.error-message, .workflow-error {
  text-align: center;
  padding: 2rem;
  background-color: var(--color-bg-2);
  border-radius: 8px;
  margin: 2rem auto;
  max-width: 600px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.retry-btn {
  margin-top: 1rem;
}

.small-btn {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .details-header {
    padding: 1rem;
    flex-wrap: wrap;
  }
  
  .page-title {
    font-size: 1.5rem;
    width: 100%;
    margin: 0.5rem 0;
    order: -1;
  }
  
  .back-button {
    margin-right: 0;
  }
  
  .json-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .json-actions {
    flex-direction: column;
    width: 100%;
    gap: 8px;
  }
  
  .cache-select {
    width: 100%;
    margin-right: 0;
  }
}

.cache-select {
  min-width: 200px;
  margin-right: 10px;
}
</style> 