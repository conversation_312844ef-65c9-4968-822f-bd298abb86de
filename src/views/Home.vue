<template>
  <div class="home-container">
    <div class="home-content">
      <h1 class="home-title">Robam Agents</h1>
      <p class="home-description">老板电器 智能体 工作流编辑</p>
      
      <!-- 应用列表 -->
      <div class="app-list-section">
        <h2 class="section-title">选择应用进行编辑</h2>
      <p class="navigation-tip">点击应用卡片将直接进入工作流编辑页面</p>
        
        <div v-if="loading" class="loading-indicator">
          <div class="spinner"></div>
          <span>加载应用列表中...</span>
        </div>
        
        <div v-else-if="error" class="error-message">
          <p>{{ error }}</p>
          <button @click="fetchAppList" class="retry-btn">重试</button>
        </div>
        
        <div v-else class="app-grid">
          <!-- 日志追踪卡片，放最前面 -->
          <div class="app-card log-trace-card" @click="goLogTrace">
            <div class="app-icon">
              <div class="default-icon">L</div>
            </div>
            <div class="app-info">
              <h3 class="app-name">日志追踪</h3>
              <p class="app-description">查看和追踪工作流日志</p>
            </div>
          </div>
          
          <!-- 新的JSON编辑器卡片 -->
          <div class="app-card json-editor-card" @click="goJsonEditor">
            <div class="app-icon">
              <div class="default-icon">J</div>
            </div>
            <div class="app-info">
              <h3 class="app-name">JSON编辑器</h3>
              <p class="app-description">专用JSON文件编辑工具</p>
            </div>
          </div>
          
          <!-- Prompt调试卡片 -->
          <div class="app-card prompt-debug-card" @click="goPromptDebug">
            <div class="app-icon">
              <div class="default-icon">P</div>
            </div>
            <div class="app-info">
              <h3 class="app-name">Prompt调试</h3>
              <p class="app-description">测试和调试AI提示词效果</p>
            </div>
          </div>

          <!-- 应用卡片列表 -->
          <div 
            v-for="app in appList" 
            :key="app.clientKey" 
            class="app-card"
          >
            <div class="app-icon" @click="openWorkflow(app.clientKey)">
              <img v-if="app.icon" :src="app.icon" :alt="app.name">
              <div v-else class="default-icon">{{ app.name.charAt(0) }}</div>
            </div>
            <div class="app-info" @click="openWorkflow(app.clientKey)">
              <h3 class="app-name">{{ app.name }}</h3>
              <p v-if="app.description" class="app-description">{{ app.description }}</p>
              <p v-else class="app-description">无描述</p>
            </div>
            <div class="app-actions">
              <a-button 
                class="config-btn" 
                type="outline" 
                shape="circle" 
                size="mini" 
                @click.stop="configAgent(app.clientKey)"
              >
                <template #icon><icon-settings /></template>
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="background-decoration"></div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getAppList } from '../api/app'
import { IconSettings } from '@arco-design/web-vue/es/icon'

const router = useRouter()
const appList = ref([])
const loading = ref(true)
const error = ref(null)

// 模拟数据，用于测试
const mockData = {
  success: true,
  message: "操作成功！",
  code: 200,
  data: [
    {
      clientKey: "1",
      name: "AI技术助理",
      icon: "https://h5.myroki.com/img/logo.svg",
      description: "我知晓技术标准、经验案例，随时为你答疑解惑",
      agentId: "standard_assistant"
    },
    {
      clientKey: "2",
      name: "AI专利助理",
      icon: "https://h5.myroki.com/img/logo2.svg",
      description: "帮助您搜索、草拟、分析和管理专利",
      agentId: "130635006376615936"
    },
    {
      clientKey: "3",
      name: "AI产品助理",
      icon: "https://roki-ai-web-cdn.oss-cn-hangzhou.aliyuncs.com/wx-img/logo3.svg",
      description: "帮助您搜索、草拟、分析和管理专利",
      agentId: "product_assistant"
    },
    {
      clientKey: "5",
      name: "Roki食神",
      icon: "https://h5.myroki.com/img/IMG_8115.png",
      description: "为热爱烹饪的用户提供多样化的菜谱选择",
      agentId: "105085634255589376"
    },
    {
      clientKey: "7",
      name: "AI技术助理（自研）",
      icon: "https://h5.myroki.com/img/logo2.svg",
      description: "帮助您搜索、草拟、分析和管理专利",
      agentId: "112582798850535424"
    },
    {
      clientKey: "8",
      name: "AI产品助理（agent）",
      icon: "https://h5.myroki.com/img/logo.svg",
      description: "帮助您搜索、草拟、分析和管理专利",
      agentId: "999999"
    },
    {
      clientKey: "11",
      name: "魔盒V1",
      agentId: "333"
    },
    {
      clientKey: "13",
      name: "Roki食神-dev-测试",
      icon: "https://h5.myroki.com/img/IMG_8115.png",
      agentId: "888888"
    }
  ]
};

// 获取应用列表
const fetchAppList = async () => {
  loading.value = true
  error.value = null
  
  try {
    console.log('开始获取应用列表...')
    
    // 尝试从API获取数据
    let response;
    try {
      response = await getAppList()
      console.log('API 响应:', response)
    } catch (apiError) {
      console.warn('API请求失败，使用模拟数据:', apiError)
      response = mockData
    }
    
    // 检查响应结构
    if (response && response.data) {
      const filteredData = response.data.filter(item => !isNaN(item.agentId));
      console.log('过滤后的应用列表数据:', filteredData);
      appList.value = filteredData;
      console.log('设置后的 appList:', appList.value);
    } else {
      console.error('API 响应格式不符合预期:', response)
      // 使用模拟数据作为备份
      appList.value = mockData.data
      console.log('使用模拟数据:', appList.value)
    }
  } catch (err) {
    console.error('获取应用列表出错:', err)
    error.value = `获取应用列表时发生错误: ${err.message}`
    // 使用模拟数据作为备份
    appList.value = mockData.data
  } finally {
    loading.value = false
  }
}

// 直接打开工作流编辑页面
const openWorkflow = (clientKey) => {
  console.log('直接打开工作流编辑页面, clientKey:', clientKey)
  router.push(`/workflow?clientKey=${clientKey}`)
}



const goLogTrace = () => {
  router.push('/log-trace')
}

// 导航到JSON编辑器
const goJsonEditor = () => {
  router.push('/json-editor-new')
}

// 导航到Prompt调试页面
const goPromptDebug = () => {
  router.push('/prompt-debug')
}

// 打开Agent配置页面
const configAgent = (clientKey) => {
  console.log('打开Agent配置页面, clientKey:', clientKey)
  router.push(`/agent-config/${clientKey}`)
}

onMounted(() => {
  fetchAppList()
})
</script>

<style scoped>
.home-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #f5f5f5;
  overflow-y: auto; /* 修改为垂直滚动 */
  z-index: 0;
  padding: 2rem 0; /* 添加上下内边距 */
}

.home-content {
  z-index: 2;
  text-align: center;
  padding: 2rem;
  width: 90%;
  max-width: 1200px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 10px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  margin: 0 auto; /* 修改上下边距 */
  flex: 0 0 auto; /* 防止内容被压缩 */
}

.home-title {
  font-size: 3.5rem;
  margin-bottom: 1rem;
  color: #2196f3;
  font-weight: 700;
  letter-spacing: -1px;
}

.home-description {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  color: #555;
  line-height: 1.6;
}

.background-decoration {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  z-index: 1;
  min-height: 100vh; /* 确保至少覆盖一屏高度 */
}

.background-decoration::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(33, 150, 243, 0.1) 0%, transparent 70%);
  animation: rotate 30s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 应用列表样式 */
.app-list-section {
  margin-top: 2rem;
  width: 100%;
}

.section-title {
  font-size: 1.8rem;
  color: #333;
  margin-bottom: 0.5rem;
  text-align: center;
}

.navigation-tip {
  font-size: 0.95rem;
  color: #2196f3;
  margin-bottom: 1.5rem;
  text-align: center;
  font-style: italic;
}

.app-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
  width: 100%;
  align-items: stretch; /* 确保所有卡片高度一致 */
}

.app-card {
  position: relative;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 1.5rem;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid #eee;
  height: 100%;
  overflow: hidden;
}

.app-icon {
  width: 70px;
  height: 70px;
  border-radius: 14px;
  overflow: hidden;
  margin-right: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  flex-shrink: 0;
}

.app-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.default-icon {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #4caf50, #2196f3);
  color: white;
  font-size: 2rem;
  font-weight: bold;
}

.app-info {
  flex: 1;
  text-align: left;
}

.app-name {
  font-size: 1.3rem;
  margin: 0 0 0.5rem 0;
  color: #333;
}

.app-description {
  font-size: 0.95rem;
  color: #666;
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 加载和错误状态样式 */
.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(33, 150, 243, 0.3);
  border-radius: 50%;
  border-top-color: #2196f3;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error-message {
  text-align: center;
  color: #f44336;
  padding: 1.5rem;
  background-color: rgba(244, 67, 54, 0.1);
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.retry-btn {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 0.5rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 1rem;
  transition: background-color 0.3s;
}

.retry-btn:hover {
  background-color: #d32f2f;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .home-title {
    font-size: 2.5rem;
  }
  
  .home-description {
    font-size: 1rem;
  }
  
  .app-grid {
    grid-template-columns: 1fr;
  }
  
  .home-content {
    width: 95%;
    padding: 1.5rem;
  }
}

.log-trace-card {
  background: #fff;
  color: #f44336;
  border: 2px solid #f44336;
  box-shadow: 0 6px 24px rgba(244, 67, 54, 0.12);
  position: relative;
  transition: transform 0.2s, box-shadow 0.2s, border-color 0.2s;
}
.log-trace-card .default-icon {
  font-size: 2.5rem;
  background: rgba(244,67,54,0.12);
  color: #f44336;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: logtrace-bounce 1.2s infinite alternate;
}
@keyframes logtrace-bounce {
  0% { transform: scale(1);}
  100% { transform: scale(1.15);}
}
.log-trace-card:hover {
  transform: scale(1.06);
  box-shadow: 0 12px 32px rgba(244, 67, 54, 0.22);
  border-color: #d32f2f;
}
.log-trace-card .app-name, .log-trace-card .app-description {
  color: #f44336;
}

.app-actions {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease, transform 0.2s ease;
  z-index: 10;
  transform: translateY(-5px);
}

.app-card:hover .app-actions {
  opacity: 1;
  transform: translateY(0);
}

.config-btn {
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid #e0e0e0;
  color: #2196f3;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.config-btn:hover {
  background-color: #2196f3;
  color: white;
  border-color: #2196f3;
}

/* JSON编辑器卡片样式 */
.json-editor-card {
  background: #fff;
  color: #0b69a3;
  border: 2px solid #0b69a3;
  box-shadow: 0 6px 24px rgba(11, 105, 163, 0.12);
  position: relative;
  transition: transform 0.2s, box-shadow 0.2s, border-color 0.2s;
}

.json-editor-card .default-icon {
  font-size: 2.5rem;
  background: rgba(11, 105, 163, 0.12);
  color: #0b69a3;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: jsoneditor-pulse 2s infinite;
}

@keyframes jsoneditor-pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.08); }
  100% { transform: scale(1); }
}

.json-editor-card:hover {
  transform: scale(1.06);
  box-shadow: 0 12px 32px rgba(11, 105, 163, 0.22);
  border-color: #085482;
}

.json-editor-card .app-name, .json-editor-card .app-description {
  color: #0b69a3;
}

/* Prompt调试卡片样式 */
.prompt-debug-card {
  background: #fff;
  color: #7c3aed;
  border: 2px solid #7c3aed;
  box-shadow: 0 6px 24px rgba(124, 58, 237, 0.12);
  position: relative;
  transition: transform 0.2s, box-shadow 0.2s, border-color 0.2s;
}

.prompt-debug-card .default-icon {
  font-size: 2.5rem;
  background: rgba(124, 58, 237, 0.12);
  color: #7c3aed;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: prompt-glow 2s infinite;
}

@keyframes prompt-glow {
  0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(124, 58, 237, 0.4); }
  50% { transform: scale(1.05); box-shadow: 0 0 0 10px rgba(124, 58, 237, 0); }
  100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(124, 58, 237, 0); }
}

.prompt-debug-card:hover {
  transform: scale(1.06);
  box-shadow: 0 12px 32px rgba(124, 58, 237, 0.22);
  border-color: #5b21b6;
}

.prompt-debug-card .app-name, .prompt-debug-card .app-description {
  color: #7c3aed;
}


</style>