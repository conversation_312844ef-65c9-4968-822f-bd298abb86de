<template>
  <div class="prompt-debug-container">
    <!-- 背景装饰 -->
    <div class="background-decoration"></div>
    
    <div class="debug-header">
      <h1 class="debug-title">Prompt 调试工具</h1>
      <p class="debug-description">测试和优化AI提示词效果</p>
    </div>

    <!-- 对照组管理 -->
    <div class="comparison-controls">
      <a-space>
        <a-button @click="addComparisonGroup" :disabled="comparisonGroups.length >= 3" type="primary">
          <template #icon><icon-plus /></template>
          添加对照组 ({{ comparisonGroups.length }}/3)
        </a-button>
        <a-button @click="runAllComparisons" :loading="isRunningAll" type="primary" :disabled="comparisonGroups.length === 0">
          <template #icon><icon-play-arrow /></template>
          {{ isRunningAll ? '运行中...' : '运行所有对照组' }}
        </a-button>
        <a-button @click="showComparisonView" :disabled="!hasResults" type="outline">
          <template #icon><icon-eye /></template>
          对比查看
        </a-button>
        <a-button @click="clearAllResults" :disabled="comparisonGroups.length === 0">
          <template #icon><icon-refresh /></template>
          清空所有结果
        </a-button>
        <a-button @click="toggleGlobalRenderMode" type="outline">
          {{ globalRenderMode === 'text' ? '📝 纯文本' : '📋 Markdown' }}
        </a-button>
        <a-button @click="goHome" type="outline">
          <template #icon><icon-home /></template>
          返回首页
        </a-button>
      </a-space>
    </div>

    <!-- 对照组列表 - 横向排列 -->
    <div v-if="comparisonGroups.length > 0" class="comparison-groups-horizontal">
      <div v-for="(group, groupIndex) in comparisonGroups" :key="group.id" class="comparison-group-card">
        <div class="group-header">
          <h3>对照组 {{ groupIndex + 1 }}</h3>
          <a-space>
            <a-button @click="openConfigModal(groupIndex)" size="small" type="outline">
              <template #icon><icon-settings /></template>
              配置
            </a-button>
            <a-button @click="runSingleComparison(groupIndex)" :loading="group.isRunning" size="small" type="primary">
              <template #icon><icon-play-arrow /></template>
              {{ group.isRunning ? '运行中' : '运行' }}
            </a-button>
            <a-button @click="removeComparisonGroup(groupIndex)" size="small" status="danger">
              <template #icon><icon-delete /></template>
            </a-button>
          </a-space>
        </div>
        
        <div class="group-content-vertical">
          <!-- 提示词编辑区 -->
          <div class="prompt-section">
            <div class="prompt-header">
              <h4>提示词编辑</h4>
              <a-space>
                <a-button @click="showGroupPromptDetail(groupIndex)" size="small" type="outline">
                  <template #icon><icon-eye /></template>
                  详情编辑
                </a-button>
                <a-button @click="addMessage(groupIndex, 'system')" size="small" type="outline">
                  <template #icon><icon-plus /></template>
                  系统
                </a-button>
                <a-button @click="addMessage(groupIndex, 'user')" size="small" type="outline">
                  <template #icon><icon-plus /></template>
                  用户
                </a-button>
                <a-button @click="addMessage(groupIndex, 'assistant')" size="small" type="outline">
                  <template #icon><icon-plus /></template>
                  助手
                </a-button>
              </a-space>
            </div>
            
            <div class="messages-container">
              <div v-for="(message, index) in group.messages" :key="index" class="message-item">
                <div class="message-header">
                  <div class="role-tag" :class="`role-${message.role}`">
                    <icon-user v-if="message.role === 'user'" />
                    <icon-robot v-else-if="message.role === 'assistant'" />
                    <icon-settings v-else />
                    {{ getRoleLabel(message.role) }}
                  </div>
                  <div class="message-actions">
                    <a-button @click="moveMessage(groupIndex, index, -1)" :disabled="index === 0" size="mini" type="text">
                      <template #icon><icon-up /></template>
                    </a-button>
                    <a-button @click="moveMessage(groupIndex, index, 1)" :disabled="index === group.messages.length - 1" size="mini" type="text">
                      <template #icon><icon-down /></template>
                    </a-button>
                    <a-button @click="removeMessage(groupIndex, index)" size="mini" type="text" status="danger">
                      <template #icon><icon-delete /></template>
                    </a-button>
                  </div>
                </div>
                <a-textarea 
                  v-model="message.content" 
                  :placeholder="getPlaceholder(message.role)"
                  :auto-size="{ minRows: 2, maxRows: 4 }"
                  class="message-content"
                />
              </div>
              
              <div v-if="group.messages.length === 0" class="empty-messages">
                <icon-plus class="empty-icon" />
                <p>点击上方按钮添加提示词消息</p>
              </div>
            </div>
          </div>

          <!-- 调试结果区 -->
          <div class="result-section">
            <div class="result-container">
              <!-- 统一的响应容器 - 支持流式和完成状态 -->
              <div v-if="group.isRunning || group.result" class="unified-response-container" :class="{ 'completed': !group.isRunning && group.result }">
                <div class="response-header">
                  <span class="response-title">
                    {{ group.isRunning ? '实时响应' : '' }}
                  </span>
                  <div class="response-tags">
                    <a-tag v-if="group.isRunning" color="blue" size="small">流式输出</a-tag>
                    <template v-else>
                      <a-tag size="small">{{ group.config.provider }}</a-tag>
                      <a-tag size="small">{{ group.config.model }}</a-tag>
                      <a-tag size="small">温度: {{ group.config.temperature }}</a-tag>
                      <a-button @click="toggleGlobalRenderMode" size="mini" type="text" v-if="group.result">
                        {{ globalRenderMode === 'text' ? 'Markdown' : '纯文本' }}
                      </a-button>
                    </template>
                  </div>
                </div>

                <!-- 对话模式界面 -->
                <div class="chat-container">
                  <!-- 显示对话历史 -->
                  <div v-for="(chat, chatIndex) in group.chatHistory || []" :key="chatIndex" class="chat-message">
                    <div v-if="chat.role === 'user'" class="user-message">
                      <div class="message-avatar user-avatar">U</div>
                      <div class="message-bubble user-bubble">
                        <div class="message-content">{{ chat.content }}</div>
                        <div class="message-actions">
                          <a-button size="mini" type="text" @click="copyResult(chat.content)">
                            <template #icon><icon-copy /></template>
                          </a-button>
                          <a-button size="mini" type="text" status="danger" @click="deleteMessage(groupIndex, chatIndex)">
                            <template #icon><icon-delete /></template>
                          </a-button>
                        </div>
                      </div>
                    </div>

                    <div v-else-if="chat.role === 'assistant'" class="assistant-message">
                      <div class="message-avatar assistant-avatar">AI</div>
                      <div class="message-bubble assistant-bubble">
                        <div class="message-content">
                          <pre v-if="globalRenderMode === 'text'">{{ chat.content }}</pre>
                          <div v-else class="markdown-content" v-html="renderMarkdown(chat.content)"></div>
                        </div>
                        <div class="message-meta">
                          <span class="message-time">{{ formatTime(chat.timestamp) }}</span>
                          <span class="message-tokens" v-if="chat.usage">
                            Tokens: {{ chat.usage.total_tokens }}
                          </span>
                        </div>
                        <div class="message-actions">
                          <a-button size="mini" type="text" @click="copyResult(chat.content)">
                            <template #icon><icon-copy /></template>
                          </a-button>
                          <a-button size="mini" type="text" @click="showResultDetail(chat, groupIndex)">
                            <template #icon><icon-eye /></template>
                          </a-button>
                          <a-button size="mini" type="text" @click="regenerateResponse(groupIndex, chatIndex)">
                            <template #icon><icon-refresh /></template>
                          </a-button>
                          <a-button size="mini" type="text" status="danger" @click="deleteMessage(groupIndex, chatIndex)">
                            <template #icon><icon-delete /></template>
                          </a-button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 当前流式输出 -->
                  <div v-if="group.isRunning && group.streamingText" class="assistant-message streaming">
                    <div class="message-avatar assistant-avatar">AI</div>
                    <div class="message-bubble assistant-bubble">
                      <div class="message-content">
                        <pre v-if="globalRenderMode === 'text'">{{ group.streamingText }}</pre>
                        <div v-else class="markdown-content" v-html="renderMarkdown(group.streamingText)"></div>
                      </div>
                      <div class="typing-indicator">
                        <div class="typing-dots">
                          <span></span>
                          <span></span>
                          <span></span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 等待状态 -->
                  <div v-else-if="group.isRunning" class="assistant-message">
                    <div class="message-avatar assistant-avatar">AI</div>
                    <div class="message-bubble assistant-bubble">
                      <div class="waiting-indicator">
                        <div class="typing-dots">
                          <span></span>
                          <span></span>
                          <span></span>
                        </div>
                        <p>正在思考中...</p>
                      </div>
                    </div>
                  </div>

                  <!-- 空状态提示 -->
                  <div v-if="!group.chatHistory || group.chatHistory.length === 0" class="chat-empty-state">
                    <div class="empty-chat-icon">💬</div>
                    <h4>开始对话</h4>
                    <p>在下方输入框中输入消息，开始与AI进行多轮对话</p>
                  </div>

                  <!-- 输入框 -->
                  <div class="chat-input-container">
                    <div class="chat-input-wrapper">
                      <a-textarea
                        v-model="group.currentInput"
                        placeholder="输入消息进行多轮对话..."
                        :auto-size="{ minRows: 1, maxRows: 4 }"
                        class="chat-input"
                        @keydown="handleChatKeydown($event, groupIndex)"
                      />
                      <div class="input-actions">
                        <a-button
                          type="primary"
                          size="small"
                          :loading="group.isRunning"
                          :disabled="!group.currentInput?.trim()"
                          @click="sendMessage(groupIndex)"
                        >
                          发送
                        </a-button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div v-else-if="group.error" class="error-result">
                <icon-exclamation-circle-fill class="error-icon" />
                <h3>调试失败</h3>
                <p>{{ group.error }}</p>
                <a-button @click="runSingleComparison(groupIndex)" type="primary">重试</a-button>
              </div>
              
              <div v-else class="empty-result">
                <icon-play-arrow class="empty-icon" />
                <p>点击"运行"开始测试提示词</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 共享问题输入区域 -->
    <div v-if="comparisonGroups.length > 0" class="shared-input-section">
      <a-card title="统一提问" class="shared-input-card">
        <template #extra>
          <a-space>
            <a-button @click="askSharedQuestion" type="primary" :disabled="!sharedInputContent.trim()">
              <template #icon><icon-send /></template>
              提问所有组
            </a-button>
            <a-button @click="clearSharedInput" type="outline">
              <template #icon><icon-delete /></template>
              清空
            </a-button>
          </a-space>
        </template>
        
        <a-textarea 
          v-model="sharedInputContent" 
          placeholder="在此输入问题，点击'提问所有组'后，将使用此问题同时向所有对照组发起提问（支持Ctrl+Enter或Cmd+Enter发送）"
          :auto-size="{ minRows: 3, maxRows: 8 }"
          class="shared-input-textarea"
          @keydown="handleSharedInputKeydown"
        />
      </a-card>
    </div>

    <!-- 原始单一调试模式（当没有对照组时显示） -->
    <div v-else class="debug-content">
      <!-- 左侧配置面板 -->
      <div class="config-panel">
        <a-card title="模型配置" class="config-card">
          <div class="form-group">
            <label>模型提供商</label>
            <a-select v-model="config.provider" placeholder="选择模型提供商" @change="onProviderChange" :loading="loadingModels">
              <a-option
                v-for="option in providerOptions"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </a-option>
            </a-select>
          </div>
          
          <div class="form-group">
            <label>模型</label>
            <a-select v-model="config.model" placeholder="选择模型" :loading="modelsLoading">
              <a-option v-for="model in availableModels" :key="model.value" :value="model.value">
                {{ model.label }}
              </a-option>
            </a-select>
          </div>
          
          <div class="form-group">
            <label>温度 ({{ config.temperature }})</label>
            <a-slider v-model="config.temperature" :min="0" :max="2" :step="0.1" />
          </div>
          
          <div class="form-group">
            <label>最大Token数</label>
            <a-input-number v-model="config.maxTokens" :min="1" :max="4000" placeholder="最大Token数" />
          </div>
        </a-card>

        <a-card title="快速配置" class="config-card">
          <div class="preset-buttons">
            <a-button @click="loadPreset('creative')" size="small">创意写作</a-button>
            <a-button @click="loadPreset('analytical')" size="small">分析推理</a-button>
            <a-button @click="loadPreset('coding')" size="small">代码生成</a-button>
            <a-button @click="loadPreset('translation')" size="small">翻译任务</a-button>
          </div>
        </a-card>
      </div>

      <!-- 中间提示词编辑区 -->
      <div class="prompt-panel">
        <a-card title="提示词编辑" class="prompt-card">
          <template #extra>
            <a-space>
              <a-button @click="addMessage('system')" size="small" type="outline">
                <template #icon><icon-plus /></template>
                系统
              </a-button>
              <a-button @click="addMessage('user')" size="small" type="outline">
                <template #icon><icon-plus /></template>
                用户
              </a-button>
              <a-button @click="addMessage('assistant')" size="small" type="outline">
                <template #icon><icon-plus /></template>
                助手
              </a-button>
            </a-space>
          </template>
          
          <div class="messages-container">
            <div v-for="(message, index) in messages" :key="index" class="message-item">
              <div class="message-header">
                <div class="role-tag" :class="`role-${message.role}`">
                  <icon-user v-if="message.role === 'user'" />
                  <icon-robot v-else-if="message.role === 'assistant'" />
                  <icon-settings v-else />
                  {{ getRoleLabel(message.role) }}
                </div>
                <div class="message-actions">
                  <a-button @click="moveMessage(index, -1)" :disabled="index === 0" size="mini" type="text">
                    <template #icon><icon-up /></template>
                  </a-button>
                  <a-button @click="moveMessage(index, 1)" :disabled="index === messages.length - 1" size="mini" type="text">
                    <template #icon><icon-down /></template>
                  </a-button>
                  <a-button @click="removeMessage(index)" size="mini" type="text" status="danger">
                    <template #icon><icon-delete /></template>
                  </a-button>
                </div>
              </div>
              <a-textarea 
                v-model="message.content" 
                :placeholder="getPlaceholder(message.role)"
                :auto-size="{ minRows: 3, maxRows: 10 }"
                class="message-content"
              />
            </div>
            
            <div v-if="messages.length === 0" class="empty-messages">
              <icon-plus class="empty-icon" />
              <p>点击上方按钮添加提示词消息</p>
            </div>
          </div>
        </a-card>
      </div>

      <!-- 右侧结果面板 -->
      <div class="result-panel">
        <a-card class="result-card">
          <template #extra>
            <a-space>
              <a-button @click="runDebug" :loading="isRunning" type="primary">
                <template #icon><icon-play-arrow /></template>
                {{ isRunning ? '运行中...' : '运行调试' }}
              </a-button>
              <a-button @click="clearResult" :disabled="!result" size="small">
                <template #icon><icon-refresh /></template>
                清空
              </a-button>
            </a-space>
          </template>
          
          <div class="result-container">
            <!-- 统一的响应容器 - 支持流式和完成状态 -->
            <div v-if="isRunning || result" class="unified-response-container" :class="{ 'completed': !isRunning && result }">
              <div class="response-header">
                <span class="response-title">
                  {{ isRunning ? '实时响应' : '' }}
                </span>
                <div class="response-tags">
                  <a-tag v-if="isRunning" color="blue" size="small">流式输出</a-tag>
                  <template v-else>
                    <a-tag size="small">{{ config.provider }}</a-tag>
                    <a-tag size="small">{{ config.model }}</a-tag>
                    <a-tag size="small">温度: {{ config.temperature }}</a-tag>
                    <a-button @click="toggleGlobalRenderMode" size="mini" type="text" v-if="result">
                      {{ globalRenderMode === 'text' ? 'Markdown' : '纯文本' }}
                    </a-button>
                  </template>
                </div>
              </div>

              <div class="response-content">
                <div v-if="streamingText || result" class="response-text">
                  <pre v-if="globalRenderMode === 'text'">{{ isRunning ? streamingText : result.content }}</pre>
                  <div v-else class="markdown-content" v-html="renderMarkdown(isRunning ? streamingText : result.content)"></div>
                </div>
                <div v-else class="response-waiting">
                  <div class="waiting-indicator">
                    <div class="typing-dots">
                      <span></span>
                      <span></span>
                      <span></span>
                    </div>
                    <p>等待响应中...</p>
                  </div>
                </div>
              </div>

              <!-- 完成后显示操作按钮 -->
              <div v-if="!isRunning && result" class="response-actions">
                <a-space>
                  <a-button @click="copyResult" size="small">
                    <template #icon><icon-copy /></template>
                    复制结果
                  </a-button>
                  <a-button @click="showSingleResultDetail" size="small" type="outline">
                    <template #icon><icon-eye /></template>
                    查看详情
                  </a-button>
                  <a-button @click="saveAsTemplate" size="small">
                    <template #icon><icon-save /></template>
                    保存为模板
                  </a-button>
                </a-space>
              </div>
            </div>
            
            <div v-else-if="error" class="error-result">
              <icon-exclamation-circle-fill class="error-icon" />
              <h3>调试失败</h3>
              <p>{{ error }}</p>
              <a-button @click="runDebug" type="primary">重试</a-button>
            </div>
            
            <div v-else class="empty-result">
              <icon-play-arrow class="empty-icon" />
              <p>点击"运行调试"开始测试提示词</p>
            </div>
          </div>
        </a-card>
      </div>
    </div>



    <!-- 模板管理弹窗 -->
    <a-modal v-model:visible="templateModalVisible" title="提示词模板" width="800px">
      <div class="template-content">
        <div class="template-actions">
          <a-input v-model="newTemplateName" placeholder="输入模板名称" style="width: 200px;" />
          <a-button @click="saveTemplate" :disabled="!newTemplateName" type="primary">保存当前配置</a-button>
        </div>
        
        <div class="template-list">
          <div v-for="template in templates" :key="template.id" class="template-item">
            <div class="template-info">
              <h4>{{ template.name }}</h4>
              <p>{{ template.description }}</p>
              <small>{{ formatDate(template.createdAt) }}</small>
            </div>
            <div class="template-actions">
              <a-button @click="loadTemplate(template)" size="small">加载</a-button>
              <a-button @click="deleteTemplate(template.id)" size="small" status="danger">删除</a-button>
            </div>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 配置弹窗 -->
    <a-modal v-model:visible="configModalVisible" title="模型配置" width="500px" @ok="saveConfig" @cancel="cancelConfig">
      <div class="config-modal" v-if="currentConfigGroupIndex !== -1">
        <div class="form-group">
          <label>模型提供商</label>
          <a-select v-model="tempConfig.provider" placeholder="选择模型提供商" @change="onTempProviderChange" :loading="loadingModels">
            <a-option
              v-for="option in providerOptions"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </a-option>
          </a-select>
        </div>
        
        <div class="form-group">
          <label>模型</label>
          <a-select v-model="tempConfig.model" placeholder="选择模型">
            <a-option v-for="model in getAvailableModels(tempConfig.provider)" :key="model.value" :value="model.value">
              {{ model.label }}
            </a-option>
          </a-select>
        </div>
        
        <div class="form-group">
          <label>温度 ({{ tempConfig.temperature }})</label>
          <a-slider v-model="tempConfig.temperature" :min="0" :max="2" :step="0.1" />
        </div>
        
        <div class="form-group">
          <label>最大Token数</label>
          <a-input-number v-model="tempConfig.maxTokens" :min="1" :max="4000" placeholder="最大Token数" />
        </div>
      </div>
    </a-modal>
    
    <!-- 结果详情模态框 -->
    <a-modal 
      v-model:visible="resultDetailVisible" 
      title="调试结果详情" 
      width="80%"
      :footer="false"
      :mask-closable="true"
    >
      <div v-if="currentResultDetail" class="result-detail-modal">
        <div class="result-detail-meta">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="提供商">{{ currentResultDetail.provider || '未知' }}</a-descriptions-item>
            <a-descriptions-item label="模型">{{ currentResultDetail.model || '未知' }}</a-descriptions-item>
            <a-descriptions-item label="温度">{{ currentResultDetail.temperature || '未知' }}</a-descriptions-item>
            <a-descriptions-item label="Token使用" v-if="currentResultDetail.usage">
              总计: {{ currentResultDetail.usage.total_tokens }} 
              (输入: {{ currentResultDetail.usage.prompt_tokens }}, 输出: {{ currentResultDetail.usage.completion_tokens }})
            </a-descriptions-item>
          </a-descriptions>
        </div>
        
        <div class="result-detail-content">
          <div class="result-detail-header">
            <h4>完整内容</h4>
            <a-button @click="toggleGlobalRenderMode" size="mini" type="text">
              {{ globalRenderMode === 'text' ? 'Markdown' : '纯文本' }}
            </a-button>
          </div>
          <div class="result-detail-text">
            <pre v-if="globalRenderMode === 'text'">{{ currentResultDetail.content }}</pre>
            <div v-else class="markdown-content" v-html="renderMarkdown(currentResultDetail.content)"></div>
          </div>
        </div>
        
        <div class="result-detail-actions">
          <a-space>
            <a-button @click="copyResult(currentResultDetail.content)" type="primary">
              <template #icon><icon-copy /></template>
              复制内容
            </a-button>
            <a-button @click="resultDetailVisible = false">
              关闭
            </a-button>
          </a-space>
        </div>
      </div>
    </a-modal>
    
    <!-- 对比查看模态框 -->
    <a-modal
      v-model:visible="comparisonViewVisible"
      title="对照组结果对比"
      width="95%"
      :footer="false"
      :mask-closable="true"
      class="comparison-view-modal"
    >
      <div class="comparison-view-content">
        <div class="comparison-results-container">
          <div
            v-for="group in comparisonGroups.filter(g => (g.chatHistory && g.chatHistory.length > 0) || g.error || g.isRunning)"
            :key="group.id"
            class="comparison-result-card"
          >
            <!-- 对照组标题和元数据 -->
            <div class="comparison-result-header">
              <h3>对照组 {{ comparisonGroups.findIndex(g => g.id === group.id) + 1 }}</h3>
              <div class="comparison-result-meta">
                <div class="meta-tags">
                  <a-tag color="blue">{{ group.config.provider }}</a-tag>
                  <a-tag color="green">{{ group.config.model }}</a-tag>
                  <a-tag color="orange">温度: {{ group.config.temperature }}</a-tag>
                </div>
              </div>
            </div>

            <!-- 结果内容 -->
            <div class="comparison-result-content">
              <div v-if="group.isRunning" class="comparison-loading">
                <a-spin />
                <p>正在运行中...</p>
              </div>

              <!-- 显示聊天历史 -->
              <div v-else-if="group.chatHistory && group.chatHistory.length > 0" class="comparison-chat-history">
                <div class="comparison-chat-header">
                  <h4>对话历史</h4>
                  <a-button @click="toggleComparisonRenderMode(group.id)" size="mini" type="text">
                    {{ getComparisonRenderMode(group.id) === 'text' ? 'Markdown' : '纯文本' }}
                  </a-button>
                </div>

                <div class="comparison-chat-messages">
                  <div v-for="(chat, chatIndex) in group.chatHistory" :key="chatIndex" class="comparison-chat-message">
                    <div v-if="chat.role === 'user'" class="comparison-user-message">
                      <div class="comparison-message-avatar comparison-user-avatar">U</div>
                      <div class="comparison-message-bubble comparison-user-bubble">
                        <div class="comparison-message-content">{{ chat.content }}</div>
                      </div>
                    </div>

                    <div v-else-if="chat.role === 'assistant'" class="comparison-assistant-message">
                      <div class="comparison-message-avatar comparison-assistant-avatar">AI</div>
                      <div class="comparison-message-bubble comparison-assistant-bubble">
                        <div class="comparison-message-content">
                          <pre v-if="getComparisonRenderMode(group.id) === 'text'">{{ chat.content }}</pre>
                          <div v-else class="markdown-content" v-html="renderMarkdown(chat.content)"></div>
                        </div>
                        <div class="comparison-message-meta">
                          <span class="comparison-message-time">{{ formatTime(chat.timestamp) }}</span>
                          <span class="comparison-message-tokens" v-if="chat.usage">
                            Tokens: {{ chat.usage.total_tokens }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="comparison-result-actions">
                  <a-space>
                    <a-button @click="copyAllChatHistory(group)" type="primary" size="small">
                      <template #icon><icon-copy /></template>
                      复制对话
                    </a-button>
                    <a-button @click="showChatHistoryDetail(group, comparisonGroups.findIndex(g => g.id === group.id))" size="small" type="outline">
                      <template #icon><icon-eye /></template>
                      单独查看
                    </a-button>
                  </a-space>
                </div>
              </div>

              <div v-else-if="group.error" class="comparison-error">
                <icon-exclamation-circle-fill class="error-icon" />
                <h4>运行失败</h4>
                <p>{{ group.error }}</p>
                <a-button @click="runSingleComparison(comparisonGroups.findIndex(g => g.id === group.id))" size="small" type="primary">
                  重试
                </a-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 如果没有任何结果 -->
        <div v-if="comparisonGroups.filter(g => (g.chatHistory && g.chatHistory.length > 0) || g.error || g.isRunning).length === 0" class="comparison-empty-state">
          <icon-play-arrow class="empty-icon" />
          <h3>暂无对比结果</h3>
          <p>请先运行对照组调试以查看结果对比</p>
          <a-button @click="runAllComparisons" :loading="isRunningAll" type="primary">
            <template #icon><icon-play-arrow /></template>
            运行所有对照组
          </a-button>
        </div>

        <div class="comparison-view-actions">
          <a-space>
            <a-button @click="runAllComparisons" :loading="isRunningAll" type="primary">
              <template #icon><icon-play-arrow /></template>
              重新运行所有
            </a-button>
            <a-button @click="comparisonViewVisible = false">
              关闭
            </a-button>
          </a-space>
        </div>
      </div>
    </a-modal>

    <!-- 对照组提示词详情编辑模态框 -->
    <a-modal
      v-model:visible="groupPromptDetailVisible"
      title="提示词详情编辑"
      width="80%"
      :footer="false"
      @cancel="closeGroupPromptDetail"
    >
      <div v-if="currentEditingGroup !== null" class="group-prompt-detail">
        <div class="detail-header">
          <h3>对照组 {{ currentEditingGroup + 1 }} - 提示词编辑</h3>
          <a-space>
            <a-button @click="addMessage(currentEditingGroup, 'system')" type="outline">
              <template #icon><icon-plus /></template>
              添加系统消息
            </a-button>
            <a-button @click="addMessage(currentEditingGroup, 'user')" type="outline">
              <template #icon><icon-plus /></template>
              添加用户消息
            </a-button>
            <a-button @click="addMessage(currentEditingGroup, 'assistant')" type="outline">
              <template #icon><icon-plus /></template>
              添加助手消息
            </a-button>
          </a-space>
        </div>

        <div class="detail-messages">
          <div v-for="(message, index) in comparisonGroups[currentEditingGroup]?.messages || []" :key="index" class="detail-message-item">
            <div class="detail-message-header">
              <div class="detail-role-tag" :class="`role-${message.role}`">
                <icon-user v-if="message.role === 'user'" />
                <icon-robot v-else-if="message.role === 'assistant'" />
                <icon-settings v-else />
                {{ getRoleLabel(message.role) }}
              </div>
              <div class="detail-message-actions">
                <a-button @click="moveMessage(currentEditingGroup, index, -1)" :disabled="index === 0" size="small" type="text">
                  <template #icon><icon-up /></template>
                  上移
                </a-button>
                <a-button @click="moveMessage(currentEditingGroup, index, 1)" :disabled="index === (comparisonGroups[currentEditingGroup]?.messages.length || 0) - 1" size="small" type="text">
                  <template #icon><icon-down /></template>
                  下移
                </a-button>
                <a-button @click="removeMessage(currentEditingGroup, index)" size="small" type="text" status="danger">
                  <template #icon><icon-delete /></template>
                  删除
                </a-button>
              </div>
            </div>
            <a-textarea
              v-model="message.content"
              :placeholder="getPlaceholder(message.role)"
              :auto-size="{ minRows: 4, maxRows: 20 }"
              class="detail-message-content"
            />
          </div>

          <div v-if="(comparisonGroups[currentEditingGroup]?.messages || []).length === 0" class="detail-empty-messages">
            <icon-plus class="empty-icon" />
            <p>暂无提示词消息，点击上方按钮添加</p>
          </div>
        </div>

        <div class="detail-footer">
          <a-space>
            <a-button @click="closeGroupPromptDetail">关闭</a-button>
            <a-button type="primary" @click="closeGroupPromptDetail">保存并关闭</a-button>
          </a-space>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { Message } from '@arco-design/web-vue'
import { useRouter } from 'vue-router'
import {
  IconPlus, IconUser, IconRobot, IconSettings, IconUp, IconDown, IconDelete,
  IconPlayArrow, IconRefresh, IconCopy, IconSave, IconExclamationCircleFill, IconEye, IconSend, IconHome
} from '@arco-design/web-vue/es/icon'

import { getLlmModels, chatWithLLM } from '@/api/workflow'
import { marked } from 'marked'
import hljs from 'highlight.js'

// 配置 marked
marked.setOptions({
  highlight: function(code, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(code, { language: lang }).value
      } catch (err) {}
    }
    return hljs.highlightAuto(code).value
  },
  breaks: true,
  gfm: true
})

// 响应式数据

// 路由
const router = useRouter()

// 对照组数据
const comparisonGroups = ref([])
const isRunningAll = ref(false)
let groupIdCounter = 0

// 原始单一调试模式数据
const config = reactive({
  provider: 'doubao',
  model: 'ep-20250616092839-v2ntc',
  temperature: 0.7,
  maxTokens: 1000
})

const messages = ref([
  {
    role: 'system',
    content: `你是一个专业的AI提示词优化专家。请帮我优化以下prompt，并按照以下格式返回：

# Role: [角色名称]

## Profile
- language: [语言]
- description: [详细的角色描述]
- background: [角色背景]
- personality: [性格特征]
- expertise: [专业领域]
- target_audience: [目标用户群]

## Skills

1. [核心技能类别]
   - [具体技能]: [简要说明]
   - [具体技能]: [简要说明]
   - [具体技能]: [简要说明]
   - [具体技能]: [简要说明]

2. [辅助技能类别]
   - [具体技能]: [简要说明]
   - [具体技能]: [简要说明]
   - [具体技能]: [简要说明]
   - [具体技能]: [简要说明]

## Rules

1. [基本原则]：
   - [具体规则]: [详细说明]
   - [具体规则]: [详细说明]
   - [具体规则]: [详细说明]
   - [具体规则]: [详细说明]

2. [行为准则]：
   - [具体规则]: [详细说明]
   - [具体规则]: [详细说明]
   - [具体规则]: [详细说明]
   - [具体规则]: [详细说明]

3. [限制条件]：
   - [具体限制]: [详细说明]
   - [具体限制]: [详细说明]
   - [具体限制]: [详细说明]
   - [具体限制]: [详细说明]

## Workflows

- 目标: [明确目标]
- 步骤 1: [详细说明]
- 步骤 2: [详细说明]
- 步骤 3: [详细说明]
- 预期结果: [说明]

## Initialization
作为[角色名称]，你必须遵守上述Rules，按照Workflows执行任务。

请基于以上模板，优化并扩展以下prompt，确保内容专业、完整且结构清晰，注意不要携带任何引导词或解释，不要使用代码块包围：`
  },
  {
    role: 'user',
    content: '请优化这个提示词：你是一个有用的AI助手。'
  }
])

const result = ref(null)
const error = ref(null)
const isRunning = ref(false)
const streamingText = ref('')
const cleanupStream = ref(null)
const modelsLoading = ref(false)
const templateModalVisible = ref(false)
const newTemplateName = ref('')
const templates = ref([])

// 配置弹窗
const configModalVisible = ref(false)
const currentConfigGroupIndex = ref(-1)
const tempConfig = reactive({
  provider: 'doubao',
  model: 'ep-20250616092839-v2ntc',
  temperature: 0.7,
  maxTokens: 1000
})

// 共享问题输入
    const sharedInputContent = ref('')
    
    // 结果详情模态框相关
    const resultDetailVisible = ref(false)
    const currentResultDetail = ref(null)
    
    // 对比查看模态框相关
    const comparisonViewVisible = ref(false)

    // 对照组提示词详情编辑相关
    const groupPromptDetailVisible = ref(false)
    const currentEditingGroup = ref(null)

    // Markdown 渲染模式
    const globalRenderMode = ref('text') // 全局渲染模式
    const comparisonRenderModes = ref({}) // 对比查看中每个对照组的独立渲染模式

// 模型列表相关状态
const llmModels = ref([])
const loadingModels = ref(false)

// 获取模型列表
const fetchModels = async () => {
  loadingModels.value = true
  try {
    const response = await getLlmModels()
    if (response.code === 200) {
      llmModels.value = response.data
    } else {
      Message.error('获取模型列表失败: ' + response.message)
    }
  } catch (error) {
    console.error('获取模型列表失败:', error)
    Message.error('获取模型列表失败: ' + error.message)
  } finally {
    loadingModels.value = false
  }
}

// 获取提供商选项
const providerOptions = computed(() => {
  const providers = new Set(llmModels.value.map(provider => provider.name))
  return Array.from(providers).map(name => ({
    label: name,
    value: name
  }))
})

// 获取模型选项（根据提供商筛选）
const getModelOptionsByProvider = (provider) => {
  if (!provider) return []
  const providerData = llmModels.value.find(p => p.name === provider)
  return providerData ? providerData.supportModels.map(model => ({
    label: model,
    value: model
  })) : []
}

// 计算属性
const availableModels = computed(() => {
  return getModelOptionsByProvider(config.provider)
})

// 检查是否有结果可以对比
const hasResults = computed(() => {
  return comparisonGroups.value.some(group => (group.chatHistory && group.chatHistory.length > 0) || group.error)
})

// 获取指定提供商的可用模型
const getAvailableModels = (provider) => {
  return getModelOptionsByProvider(provider)
}

// 预设配置
const presets = {
  creative: {
    temperature: 1.2,
    maxTokens: 2000,
    messages: [
      { role: 'system', content: '你是一个富有创意的写作助手，擅长创作有趣、生动的内容。' },
      { role: 'user', content: '请写一个关于未来科技的短故事。' }
    ]
  },
  analytical: {
    temperature: 0.3,
    maxTokens: 1500,
    messages: [
      { role: 'system', content: '你是一个逻辑严谨的分析师，擅长深入分析问题并提供客观的见解。' },
      { role: 'user', content: '请分析人工智能对就业市场的影响。' }
    ]
  },
  coding: {
    temperature: 0.1,
    maxTokens: 2000,
    messages: [
      { role: 'system', content: '你是一个专业的程序员，擅长编写高质量的代码并提供详细的解释。' },
      { role: 'user', content: '请用Python实现一个简单的计算器类。' }
    ]
  },
  translation: {
    temperature: 0.2,
    maxTokens: 1000,
    messages: [
      { role: 'system', content: '你是一个专业的翻译员，能够准确地在中英文之间进行翻译。' },
      { role: 'user', content: '请将以下中文翻译成英文：人工智能正在改变我们的生活方式。' }
    ]
  }
}

// 方法

// Markdown 渲染函数
const renderMarkdown = (content) => {
  if (!content) return ''
  return marked(content)
}

// 切换全局渲染模式
const toggleGlobalRenderMode = () => {
  globalRenderMode.value = globalRenderMode.value === 'text' ? 'markdown' : 'text'
}

// 切换对比查看中特定对照组的渲染模式
const toggleComparisonRenderMode = (groupId) => {
  if (!comparisonRenderModes.value[groupId]) {
    comparisonRenderModes.value[groupId] = 'text'
  }
  comparisonRenderModes.value[groupId] = comparisonRenderModes.value[groupId] === 'text' ? 'markdown' : 'text'
}

// 获取对比查看中特定对照组的渲染模式
const getComparisonRenderMode = (groupId) => {
  return comparisonRenderModes.value[groupId] || 'text'
}

const getRoleLabel = (role) => {
  const labels = {
    system: '系统',
    user: '用户',
    assistant: '助手'
  }
  return labels[role] || role
}

const getPlaceholder = (role) => {
  const placeholders = {
    system: '输入系统提示词，定义AI的角色和行为...',
    user: '输入用户消息，描述你的需求或问题...',
    assistant: '输入助手回复，用于few-shot示例...'
  }
  return placeholders[role] || '输入消息内容...'
}

const loadPreset = (presetName) => {
  const preset = presets[presetName]
  if (preset) {
    config.temperature = preset.temperature
    config.maxTokens = preset.maxTokens
    messages.value = [...preset.messages]
    Message.success(`已加载${presetName}预设配置`)
  }
}

const runDebug = async () => {
  if (messages.value.length === 0) {
    Message.warning('请先添加提示词消息')
    return
  }

  if (!config.provider || !config.model) {
    Message.warning('请先选择模型提供商和模型')
    return
  }

  // 清理之前的流
  if (cleanupStream.value) {
    console.log('🧹 PromptDebug单一模式 - 清理之前的流')
    cleanupStream.value()
    cleanupStream.value = null
  }

  isRunning.value = true
  error.value = null
  result.value = null
  streamingText.value = ''

  try {
    // 调用真实的LLM API
    cleanupStream.value = await chatWithLLM({
      provider: config.provider,
      modelName: config.model,
      messages: messages.value,
      temperature: config.temperature,
      stream: true
    }, {
      onMessage: (data) => {
        console.log('🔄 PromptDebug单一模式 - 收到流式数据:', data)
        if (data.content) {
          streamingText.value += data.content
          console.log('📝 PromptDebug单一模式 - 当前流式内容长度:', streamingText.value.length)
        }
        // 自动滚动到底部
        nextTick(() => {
          const streamingElement = document.querySelector('.streaming-content')
          if (streamingElement) {
            streamingElement.scrollTop = streamingElement.scrollHeight
          }
        })
      },
      onError: (error) => {
        console.error('❌ PromptDebug单一模式 - 流式错误:', error)
        error.value = error.message || '调试失败，请重试'
        Message.error('调试失败')
        isRunning.value = false
      },
      onComplete: () => {
        console.log('✅ PromptDebug单一模式 - 调试完成')
        result.value = {
          content: streamingText.value,
          usage: {
            prompt_tokens: 0,
            completion_tokens: 0,
            total_tokens: 0
          }
        }
        if (!streamingText.value) {
          Message.warning('模型未返回任何内容')
        }
        // 单个调试完成不显示成功消息，避免过多弹窗
        isRunning.value = false
      }
    })
  } catch (err) {
    console.error('❌ PromptDebug单一模式 - 调试失败:', err)
    error.value = err.message || '调试失败，请重试'
    Message.error('调试失败')
    isRunning.value = false
  }
}



const clearResult = () => {
  // 清理流
  if (cleanupStream.value) {
    console.log('🧹 PromptDebug单一模式 - 清理结果时清理流')
    cleanupStream.value()
    cleanupStream.value = null
  }

  result.value = null
  error.value = null
  streamingText.value = ''
}



const saveAsTemplate = () => {
  templateModalVisible.value = true
}

const saveTemplate = () => {
  if (!newTemplateName.value) return
  
  const template = {
    id: Date.now(),
    name: newTemplateName.value,
    description: `${config.provider} - ${config.model}`,
    config: { ...config },
    messages: [...messages.value],
    createdAt: new Date()
  }
  
  templates.value.push(template)
  localStorage.setItem('promptDebugTemplates', JSON.stringify(templates.value))
  
  newTemplateName.value = ''
  templateModalVisible.value = false
  Message.success('模板保存成功')
}

const loadTemplate = (template) => {
  Object.assign(config, template.config)
  messages.value = [...template.messages]
  templateModalVisible.value = false
  Message.success(`已加载模板: ${template.name}`)
}

const deleteTemplate = (id) => {
  const index = templates.value.findIndex(t => t.id === id)
  if (index > -1) {
    templates.value.splice(index, 1)
    localStorage.setItem('promptDebugTemplates', JSON.stringify(templates.value))
    Message.success('模板删除成功')
  }
}

const formatDate = (date) => {
  return new Date(date).toLocaleString('zh-CN')
}

const formatTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
}

// 对照组相关方法
const addComparisonGroup = () => {
  if (comparisonGroups.value.length >= 3) return

  const newGroup = {
    id: ++groupIdCounter,
    config: {
      provider: 'doubao',
      model: 'ep-20250616092839-v2ntc',
      temperature: 0.7,
      maxTokens: 1000
    },
    // 深拷贝当前提示词编辑区域的消息到新对照组
    messages: messages.value.length > 0 ?
      messages.value.map(msg => ({ role: msg.role, content: msg.content })) : [
        {
          role: 'system',
          content: `你是一个专业的AI提示词优化专家。请帮我优化以下prompt，并按照以下格式返回：

# Role: [角色名称]

## Profile
- language: [语言]
- description: [详细的角色描述]
- background: [角色背景]
- personality: [性格特征]
- expertise: [专业领域]
- target_audience: [目标用户群]

## Skills

1. [核心技能类别]
   - [具体技能]: [简要说明]
   - [具体技能]: [简要说明]
   - [具体技能]: [简要说明]
   - [具体技能]: [简要说明]

2. [辅助技能类别]
   - [具体技能]: [简要说明]
   - [具体技能]: [简要说明]
   - [具体技能]: [简要说明]
   - [具体技能]: [简要说明]

## Rules

1. [基本原则]：
   - [具体规则]: [详细说明]
   - [具体规则]: [详细说明]
   - [具体规则]: [详细说明]
   - [具体规则]: [详细说明]

2. [行为准则]：
   - [具体规则]: [详细说明]
   - [具体规则]: [详细说明]
   - [具体规则]: [详细说明]
   - [具体规则]: [详细说明]

3. [限制条件]：
   - [具体限制]: [详细说明]
   - [具体限制]: [详细说明]
   - [具体限制]: [详细说明]
   - [具体限制]: [详细说明]

## Workflows

- 目标: [明确目标]
- 步骤 1: [详细说明]
- 步骤 2: [详细说明]
- 步骤 3: [详细说明]
- 预期结果: [说明]

## Initialization
作为[角色名称]，你必须遵守上述Rules，按照Workflows执行任务。

请基于以上模板，优化并扩展以下prompt，确保内容专业、完整且结构清晰，注意不要携带任何引导词或解释，不要使用代码块包围：`
        },
        {
          role: 'user',
          content: '请优化这个提示词：你是一个有用的AI助手。'
        }
      ],
    result: null,
    error: null,
    isRunning: false,
    streamingText: '',
    cleanupStream: null,
    chatHistory: [],  // 对话历史
    currentInput: ''  // 当前输入
  }

  comparisonGroups.value.push(newGroup)
  // 添加对照组不显示消息，避免过多弹窗
}

const removeComparisonGroup = (index) => {
  const group = comparisonGroups.value[index]

  // 清理流
  if (group && group.cleanupStream) {
    console.log(`🧹 PromptDebug 对照组${index + 1} - 删除时清理流`)
    group.cleanupStream()
  }

  comparisonGroups.value.splice(index, 1)
  // 删除对照组不显示消息，避免过多弹窗
}

const onProviderChange = (groupIndex) => {
  if (groupIndex !== undefined) {
    // 对照组模式
    const group = comparisonGroups.value[groupIndex]
    if (group && group.config) {
      const models = getAvailableModels(group.config.provider)
      if (models.length > 0) {
        group.config.model = models[0].value
      } else {
        group.config.model = ''
      }
    }
  } else {
    // 单一调试模式
    const models = availableModels.value
    if (models.length > 0) {
      config.model = models[0].value
    } else {
      config.model = ''
    }
  }
}

const addMessage = (groupIndexOrRole, role) => {
  if (typeof groupIndexOrRole === 'number') {
    // 对照组模式
    const groupIndex = groupIndexOrRole
    comparisonGroups.value[groupIndex].messages.push({
      role,
      content: ''
    })
  } else {
    // 单一调试模式
    role = groupIndexOrRole
    messages.value.push({
      role,
      content: ''
    })
  }
}

const removeMessage = (groupIndexOrIndex, messageIndex) => {
  if (typeof messageIndex === 'number') {
    // 对照组模式
    const groupIndex = groupIndexOrIndex
    comparisonGroups.value[groupIndex].messages.splice(messageIndex, 1)
  } else {
    // 单一调试模式
    const index = groupIndexOrIndex
    messages.value.splice(index, 1)
  }
}

const moveMessage = (groupIndexOrIndex, messageIndexOrDirection, direction) => {
  if (typeof direction === 'number') {
    // 对照组模式
    const groupIndex = groupIndexOrIndex
    const messageIndex = messageIndexOrDirection
    const group = comparisonGroups.value[groupIndex]
    const newIndex = messageIndex + direction
    if (newIndex >= 0 && newIndex < group.messages.length) {
      const temp = group.messages[messageIndex]
      group.messages[messageIndex] = group.messages[newIndex]
      group.messages[newIndex] = temp
    }
  } else {
    // 单一调试模式
    const index = groupIndexOrIndex
    const dir = messageIndexOrDirection
    const newIndex = index + dir
    if (newIndex >= 0 && newIndex < messages.value.length) {
      const temp = messages.value[index]
      messages.value[index] = messages.value[newIndex]
      messages.value[newIndex] = temp
    }
  }
}

const runSingleComparison = async (groupIndex) => {
  const group = comparisonGroups.value[groupIndex]

  if (group.messages.length === 0) {
    Message.warning('请先添加提示词消息')
    return
  }

  if (!group.config.provider || !group.config.model) {
    Message.warning(`对照组 ${groupIndex + 1} 请先选择模型提供商和模型`)
    return
  }

  // 初始化聊天历史（如果还没有的话）
  if (!group.chatHistory) {
    group.chatHistory = []
  }

  // 如果聊天历史为空，创建一个初始的用户消息
  if (group.chatHistory.length === 0) {
    // 从系统提示词中提取最后一个用户消息作为初始消息
    const lastUserMessage = group.messages.filter(msg => msg.role === 'user').pop()
    if (lastUserMessage) {
      group.chatHistory.push({
        role: 'user',
        content: lastUserMessage.content,
        timestamp: Date.now()
      })
    }
  }

  // 构建完整的消息历史
  const fullMessages = [
    ...group.messages,  // 系统提示词
    ...group.chatHistory  // 聊天历史
  ]

  // 运行聊天完成
  await runChatCompletion(groupIndex, fullMessages)
}



const runAllComparisons = async () => {
  if (comparisonGroups.value.length === 0) return
  
  isRunningAll.value = true
  
  try {
    // 并行运行所有对照组
    const promises = comparisonGroups.value.map((group, index) => {
      if (group.messages.length === 0) {
        return Promise.resolve()
      }
      return runSingleComparison(index)
    })
    
    await Promise.all(promises)
    // 批量调试完成，不显示消息提示
  } catch (err) {
    Message.error('批量调试过程中出现错误')
  } finally {
    isRunningAll.value = false
  }
}

const clearAllResults = () => {
  comparisonGroups.value.forEach((group, index) => {
    // 清理流
    if (group.cleanupStream) {
      console.log(`🧹 PromptDebug 对照组${index + 1} - 清理所有结果时清理流`)
      group.cleanupStream()
      group.cleanupStream = null
    }

    group.result = null
    group.error = null
    group.streamingText = ''
    group.isRunning = false
    group.chatHistory = []  // 清理聊天历史
    group.currentInput = ''  // 清理当前输入
  })
  Message.success('已清空所有对话历史')
}



const copyResult = async (content) => {
  const textToCopy = content || result.value?.content
  if (textToCopy) {
    try {
      await navigator.clipboard.writeText(textToCopy)
      Message.success('已复制')
    } catch (err) {
      Message.error('复制失败')
    }
  }
}

// 配置弹窗相关方法
const openConfigModal = (groupIndex) => {
  currentConfigGroupIndex.value = groupIndex
  const group = comparisonGroups.value[groupIndex]
  Object.assign(tempConfig, group.config)
  configModalVisible.value = true
}

const saveConfig = () => {
  if (currentConfigGroupIndex.value !== -1) {
    const group = comparisonGroups.value[currentConfigGroupIndex.value]
    Object.assign(group.config, tempConfig)
    Message.success('配置已保存')
  }
  configModalVisible.value = false
  currentConfigGroupIndex.value = -1
}

const cancelConfig = () => {
  configModalVisible.value = false
  currentConfigGroupIndex.value = -1
}

const onTempProviderChange = () => {
  const models = getAvailableModels(tempConfig.provider)
  if (models.length > 0) {
    tempConfig.model = models[0].value
  }
}

// 共享问题输入相关方法
  const askSharedQuestion = async () => {
    if (!sharedInputContent.value.trim()) {
      Message.warning('请输入问题内容')
      return
    }
    
    // 为所有对照组设置相同的用户问题
    comparisonGroups.value.forEach(group => {
      // 如果最后一条消息是用户消息，则替换；否则添加新的用户消息
      const lastMessage = group.messages[group.messages.length - 1]
      if (lastMessage && lastMessage.role === 'user') {
        lastMessage.content = sharedInputContent.value
      } else {
        group.messages.push({
          role: 'user',
          content: sharedInputContent.value
        })
      }
    })
    
    Message.success(`已设置统一问题，开始向所有 ${comparisonGroups.value.length} 个对照组提问`)
    
    // 自动执行所有对照组的调试
    await runAllComparisons()
  }
  
  const clearSharedInput = () => {
    sharedInputContent.value = ''
  }
  
  // 处理共享输入的键盘事件
  const handleSharedInputKeydown = (event) => {
    // 检测 Ctrl+Enter 或 Cmd+Enter
    if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
      event.preventDefault()
      askSharedQuestion()
    }
  }
  
  // 显示结果详情
  const showResultDetail = (result, groupIndex) => {
    const group = comparisonGroups.value[groupIndex]
    currentResultDetail.value = {
      ...result,
      provider: group.config.provider,
      model: group.config.model,
      temperature: group.config.temperature
    }
    resultDetailVisible.value = true
  }

  // 显示单一调试模式的结果详情
  const showSingleResultDetail = () => {
    if (result.value) {
      currentResultDetail.value = {
        ...result.value,
        provider: config.provider,
        model: config.model,
        temperature: config.temperature
      }
      resultDetailVisible.value = true
    }
  }
  
  // 显示对比查看模态框
  const showComparisonView = () => {
    comparisonViewVisible.value = true
  }

  // 显示对照组提示词详情编辑模态框
  const showGroupPromptDetail = (groupIndex) => {
    currentEditingGroup.value = groupIndex
    groupPromptDetailVisible.value = true
  }

  // 关闭对照组提示词详情编辑模态框
  const closeGroupPromptDetail = () => {
    groupPromptDetailVisible.value = false
    currentEditingGroup.value = null
  }

  // 聊天相关方法
  const sendMessage = async (groupIndex) => {
    const group = comparisonGroups.value[groupIndex]
    const userMessage = group.currentInput.trim()

    if (!userMessage) return

    // 添加用户消息到历史
    if (!group.chatHistory) {
      group.chatHistory = []
    }

    group.chatHistory.push({
      role: 'user',
      content: userMessage,
      timestamp: Date.now()
    })

    // 清空输入框
    group.currentInput = ''

    // 构建完整的消息历史（系统提示词 + 聊天历史）
    const fullMessages = [
      ...group.messages,  // 系统提示词
      ...group.chatHistory  // 聊天历史
    ]

    // 开始AI响应
    await runChatCompletion(groupIndex, fullMessages)
  }

  const runChatCompletion = async (groupIndex, messages) => {
    const group = comparisonGroups.value[groupIndex]

    if (!group.config.provider || !group.config.model) {
      Message.warning(`对照组 ${groupIndex + 1} 请先选择模型提供商和模型`)
      return
    }

    // 清理之前的流
    if (group.cleanupStream) {
      console.log(`🧹 PromptDebug 对照组${groupIndex + 1} - 清理之前的流`)
      group.cleanupStream()
      group.cleanupStream = null
    }

    group.isRunning = true
    group.error = null
    group.streamingText = ''

    try {
      // 调用真实的LLM API
      group.cleanupStream = await chatWithLLM({
        provider: group.config.provider,
        modelName: group.config.model,
        messages: messages,
        temperature: group.config.temperature,
        stream: true
      }, {
        onMessage: (data) => {
          console.log(`🔄 PromptDebug 对照组${groupIndex + 1} - 收到流式数据:`, data)
          if (data.content) {
            group.streamingText += data.content
            console.log(`📝 PromptDebug 对照组${groupIndex + 1} - 当前流式内容长度:`, group.streamingText.length)
          }
        },
        onError: (error) => {
          console.error(`❌ PromptDebug 对照组${groupIndex + 1} - 流式错误:`, error)
          group.error = error.message || '调试失败，请重试'
          Message.error(`对照组 ${groupIndex + 1} 调试失败`)
          group.isRunning = false
        },
        onComplete: () => {
          console.log(`✅ PromptDebug 对照组${groupIndex + 1} - 调试完成`)

          // 添加AI响应到历史
          if (!group.chatHistory) {
            group.chatHistory = []
          }

          group.chatHistory.push({
            role: 'assistant',
            content: group.streamingText,
            timestamp: Date.now(),
            usage: {
              prompt_tokens: 0,
              completion_tokens: 0,
              total_tokens: 0
            }
          })

          if (!group.streamingText) {
            Message.warning(`对照组 ${groupIndex + 1} 模型未返回任何内容`)
          }

          group.isRunning = false
          group.streamingText = ''
        }
      })
    } catch (err) {
      console.error(`❌ PromptDebug 对照组${groupIndex + 1} - 调试失败:`, err)
      group.error = err.message || '调试失败，请重试'
      Message.error(`对照组 ${groupIndex + 1} 调试失败`)
      group.isRunning = false
    }
  }

  const handleChatKeydown = (event, groupIndex) => {
    // 检测 Ctrl+Enter 或 Cmd+Enter
    if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
      event.preventDefault()
      sendMessage(groupIndex)
    }
  }

  const deleteMessage = (groupIndex, chatIndex) => {
    const group = comparisonGroups.value[groupIndex]
    if (group.chatHistory && group.chatHistory[chatIndex]) {
      group.chatHistory.splice(chatIndex, 1)
    }
  }

  const regenerateResponse = async (groupIndex, chatIndex) => {
    const group = comparisonGroups.value[groupIndex]
    if (!group.chatHistory || !group.chatHistory[chatIndex]) return

    const targetMessage = group.chatHistory[chatIndex]
    if (targetMessage.role !== 'assistant') return

    // 删除这条AI回复及之后的所有消息
    group.chatHistory.splice(chatIndex)

    // 重新构建消息历史并发送
    const fullMessages = [
      ...group.messages,  // 系统提示词
      ...group.chatHistory  // 聊天历史（不包括被删除的AI回复）
    ]

    await runChatCompletion(groupIndex, fullMessages)
  }



  // 返回首页
  const goHome = () => {
    router.push('/')
  }

  // 复制整个聊天历史
  const copyAllChatHistory = async (group) => {
    if (!group.chatHistory || group.chatHistory.length === 0) return

    let chatText = `对话历史 - ${group.config.provider} ${group.config.model}\n\n`

    group.chatHistory.forEach((chat, index) => {
      const roleLabel = chat.role === 'user' ? '用户' : 'AI助手'
      const timestamp = formatTime(chat.timestamp)
      chatText += `${roleLabel} [${timestamp}]:\n${chat.content}\n\n`
    })

    try {
      await navigator.clipboard.writeText(chatText)
      Message.success('已复制完整对话历史')
    } catch (err) {
      Message.error('复制失败')
    }
  }

  // 显示聊天历史详情
  const showChatHistoryDetail = (group, groupIndex) => {
    if (!group.chatHistory || group.chatHistory.length === 0) return

    // 构建一个类似result的对象用于详情显示
    const lastAssistantMessage = group.chatHistory.filter(chat => chat.role === 'assistant').pop()
    if (lastAssistantMessage) {
      currentResultDetail.value = {
        content: formatChatHistoryForDetail(group.chatHistory),
        provider: group.config.provider,
        model: group.config.model,
        temperature: group.config.temperature,
        usage: lastAssistantMessage.usage
      }
      resultDetailVisible.value = true
    }
  }

  // 格式化聊天历史用于详情显示
  const formatChatHistoryForDetail = (chatHistory) => {
    let formattedText = ''

    chatHistory.forEach((chat, index) => {
      const roleLabel = chat.role === 'user' ? '👤 用户' : '🤖 AI助手'
      const timestamp = formatTime(chat.timestamp)

      formattedText += `${roleLabel} [${timestamp}]\n`
      formattedText += `${chat.content}\n`

      if (index < chatHistory.length - 1) {
        formattedText += '\n' + '─'.repeat(50) + '\n\n'
      }
    })

    return formattedText
  }

// 监听提供商变化，清空模型选择
watch(() => config.provider, (newProvider) => {
  if (newProvider) {
    config.model = ''
  }
})

watch(() => tempConfig.provider, (newProvider) => {
  if (newProvider) {
    tempConfig.model = ''
  }
})

// 生命周期
onMounted(() => {
  // 加载保存的模板
  const savedTemplates = localStorage.getItem('promptDebugTemplates')
  if (savedTemplates) {
    templates.value = JSON.parse(savedTemplates)
  }

  // 获取模型列表
  fetchModels()
})

// 组件卸载时清理所有流
onUnmounted(() => {
  console.log('🧹 PromptDebug - 组件卸载，清理所有流')

  // 清理单一调试模式的流
  if (cleanupStream.value) {
    cleanupStream.value()
    cleanupStream.value = null
  }

  // 清理所有对照组的流
  comparisonGroups.value.forEach((group, index) => {
    if (group.cleanupStream) {
      console.log(`🧹 PromptDebug 对照组${index + 1} - 组件卸载时清理流`)
      group.cleanupStream()
      group.cleanupStream = null
    }
  })
})
</script>

<style>
@import 'highlight.js/styles/github.css';
</style>

<style scoped>
.prompt-debug-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.background-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  z-index: 0;
}

.background-decoration::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(33, 150, 243, 0.1) 0%, transparent 70%);
  transform: translate(-50%, -50%) rotate(0deg);
  animation: rotate 30s linear infinite;
}

@keyframes rotate {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.debug-header {
  text-align: center;
  margin-bottom: 30px;
  color: #1d2129;
  position: relative;
  z-index: 2;
}

.debug-title {
  font-size: 2.5rem;
  margin-bottom: 10px;
  font-weight: 600;
}

.debug-description {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 30px;
}



.debug-content {
  display: grid;
  grid-template-columns: 300px 1fr 400px;
  gap: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.config-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.config-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.preset-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.prompt-panel {
  min-height: 600px;
}

.prompt-card {
  height: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.messages-container {
  max-height: 500px;
  overflow-y: auto;
}

.message-item {
  margin-bottom: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.role-tag {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
}

.role-system {
  background: #dbeafe;
  color: #1e40af;
}

.role-user {
  background: #dcfce7;
  color: #166534;
}

.role-assistant {
  background: #fef3c7;
  color: #92400e;
}

.message-actions {
  display: flex;
  gap: 4px;
}

.message-content {
  border: none;
  border-radius: 0;
}

.empty-messages {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.result-panel {
  display: flex;
  flex-direction: column;
}

.result-card {
  height: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* 单一调试模式结果容器样式优化 - 扩大尺寸 */
.result-container {
  min-height: 600px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.loading-result {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 8px;
  margin: 16px;
  border: 1px dashed rgba(148, 163, 184, 0.4);
}

.loading-header {
  text-align: center;
  padding: 60px 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 16px;
}

.loading-header p {
  color: #64748b;
  font-size: 15px;
  font-weight: 500;
  margin: 0;
}

/* 流式输出容器样式优化 */
.streaming-container {
  margin: 16px;
  border: 1px solid rgba(34, 197, 94, 0.2);
  border-radius: 12px;
  background: linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%);
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.08);
  animation: streamingGlow 2s ease-in-out infinite alternate;
}

@keyframes streamingGlow {
  0% {
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.08);
  }
  100% {
    box-shadow: 0 6px 20px rgba(34, 197, 94, 0.15);
  }
}

.streaming-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 14px 18px;
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border-bottom: 1px solid rgba(34, 197, 94, 0.15);
}

.streaming-title {
  font-weight: 600;
  color: #166534;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.streaming-title::before {
  content: '●';
  color: #22c55e;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.streaming-content {
  min-height: 300px;
  max-height: 60vh;
  overflow-y: auto;
  background: rgba(255, 255, 255, 0.7);
}

.streaming-text {
  margin: 0;
  padding: 18px;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.7;
  color: #1f2937;
  background: transparent;
  border: none;
  min-height: 120px;
  position: relative;
}

.streaming-text::after {
  content: '|';
  color: #1890ff;
  animation: blink 1s infinite;
  margin-left: 2px;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

.streaming-text:empty::after {
  display: none;
}

/* 单一调试模式结果内容样式优化 */
.result-content {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.result-meta {
  margin-bottom: 18px;
  padding-bottom: 14px;
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
}

.result-text {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 18px;
  min-height: 400px;
  max-height: 60vh;
  overflow-y: auto;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  flex: 1;
}

.result-text:hover {
  border-color: rgba(99, 102, 241, 0.3);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.result-text pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.7;
  color: #334155;
  letter-spacing: 0.01em;
}

/* 结果操作按钮样式优化 */
.result-actions {
  display: flex;
  gap: 10px;
  padding-top: 4px;
}

.result-actions .arco-btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.result-actions .arco-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 错误状态和空状态样式优化 */
.error-result {
  text-align: center;
  padding: 40px 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border-radius: 12px;
  margin: 16px;
  border: 1px solid rgba(239, 68, 68, 0.2);
  gap: 16px;
}

.error-result h3 {
  color: #dc2626;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.error-result p {
  color: #991b1b;
  font-size: 14px;
  margin: 0;
  max-width: 300px;
  line-height: 1.5;
}

.error-icon {
  font-size: 48px;
  color: #ef4444;
  opacity: 0.8;
}

.empty-result {
  text-align: center;
  padding: 50px 20px;
  color: #64748b;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  margin: 16px;
  border: 1px dashed rgba(148, 163, 184, 0.4);
  gap: 16px;
}

.empty-result p {
  font-size: 15px;
  font-weight: 500;
  margin: 0;
  color: #64748b;
}

.template-content {
  max-height: 500px;
  overflow-y: auto;
}

.template-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  align-items: center;
}

.template-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.template-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #f9fafb;
}

.template-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 500;
}

.template-info p {
  margin: 0 0 4px 0;
  color: #6b7280;
  font-size: 14px;
}

.template-info small {
  color: #9ca3af;
  font-size: 12px;
}

.template-actions {
  display: flex;
  gap: 8px;
}



/* 对照组样式 */
.comparison-controls {
  margin-bottom: 24px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 横向排列的对照组 - 优化布局 */
.comparison-groups-horizontal {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
  position: relative;
  z-index: 2;
  max-width: 100%;
  overflow-x: auto;
}

.comparison-group-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  min-height: 800px;
  max-height: 90vh;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(229, 230, 235, 0.8);
  flex-shrink: 0;
}

.group-header h3 {
  margin: 0;
  color: #1d2129;
  font-weight: 600;
  font-size: 16px;
}

.group-content-vertical {
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1;
}

.prompt-section {
  flex: 0 0 300px;
  min-height: 300px;
  max-height: 350px;
  overflow-y: auto;
}

.prompt-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.prompt-header h4 {
  margin: 0;
  color: #1d2129;
  font-weight: 500;
  font-size: 14px;
}

.result-section {
  flex: 1;
  min-height: 400px;
  display: flex;
  flex-direction: column;
}

.result-section h4 {
  margin: 0 0 12px 0;
  color: #1d2129;
  font-weight: 500;
  font-size: 14px;
}

/* 对照组结果容器样式优化 - 扩大尺寸 */
.result-container {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 12px;
  padding: 0;
  min-height: 300px;
  max-height: 500px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.result-container:hover {
  border-color: rgba(99, 102, 241, 0.3);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.result-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  height: 100%;
  padding: 16px;
}

.result-meta {
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
}

.result-text {
  flex: 1;
  overflow-y: auto;
  background: rgba(248, 250, 252, 0.6);
  border: 1px solid rgba(226, 232, 240, 0.5);
  border-radius: 8px;
  padding: 16px;
  margin: 8px 0 12px 0;
  backdrop-filter: blur(5px);
  transition: all 0.2s ease;
  min-height: 200px;
  max-height: 350px;
}

.result-text:hover {
  background: rgba(255, 255, 255, 0.8);
  border-color: rgba(99, 102, 241, 0.2);
}

.result-text pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.6;
  color: #334155;
  letter-spacing: 0.01em;
}

/* 结果详情模态框样式 */
.result-detail-modal {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.result-detail-meta {
  margin-bottom: 16px;
}

.result-detail-content h4 {
  margin: 0 0 12px 0;
  color: #1d2129;
  font-weight: 600;
}

.result-detail-text {
  max-height: 60vh;
  overflow-y: auto;
  background: #f7f8fa;
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  padding: 16px;
}

.result-detail-text pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.6;
  color: #1d2129;
}

.result-detail-actions {
  display: flex;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #e5e6eb;
}

/* 配置弹窗样式 */
.config-modal .form-group {
  margin-bottom: 20px;
}

.config-modal .form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #1d2129;
}

/* 共享输入区域样式 */
.shared-input-section {
  position: relative;
  z-index: 2;
  margin-top: 30px;
  margin-bottom: 30px;
}

.shared-input-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.shared-input-textarea {
  font-size: 14px;
  line-height: 1.6;
}

.shared-input-textarea .arco-textarea {
  background: rgba(247, 248, 250, 0.8);
  border: 1px solid #e5e6eb;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.shared-input-textarea .arco-textarea:focus {
  background: rgba(255, 255, 255, 0.9);
  border-color: #165dff;
  box-shadow: 0 0 0 2px rgba(22, 93, 255, 0.1);
}

/* 对比查看模态框样式 */
.comparison-view-modal .arco-modal {
  max-width: none;
  width: 100vw !important;
  height: 100vh !important;
  margin: 0 !important;
  top: 0 !important;
  left: 0 !important;
  transform: none !important;
}

.comparison-view-modal .arco-modal-container {
  padding: 0 !important;
}

.comparison-view-modal .arco-modal-content {
  height: 100vh;
  border-radius: 0;
}

.comparison-view-content {
  height: calc(100vh - 60px);
  overflow-y: auto;
  padding: 20px;
}

.comparison-results-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.comparison-result-card {
  border: 1px solid #e5e6eb;
  border-radius: 12px;
  background: #fff;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.comparison-result-header {
  padding: 20px;
  background: linear-gradient(135deg, #f7f8fa 0%, #f0f2f5 100%);
  border-bottom: 1px solid #e5e6eb;
}

.comparison-result-header h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1d2129;
}

.comparison-result-meta {
  margin: 0;
}

.meta-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.meta-tags .arco-tag {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.comparison-result-content {
  padding: 20px;
}

.comparison-success h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1d2129;
}

.comparison-result-text {
  background: #f7f8fa;
  border: 1px solid #e5e6eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.comparison-result-text pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.6;
  color: #1f2937;
}

.comparison-result-actions {
  display: flex;
  justify-content: flex-start;
}

/* 加载状态样式 */
.comparison-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  color: #6366f1;
  font-size: 14px;
  font-weight: 500;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
}

/* 错误状态样式 */
.comparison-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  color: #dc2626;
  font-size: 14px;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border: 1px solid rgba(220, 38, 38, 0.2);
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
}

.comparison-error .error-icon {
  font-size: 24px;
  color: #dc2626;
}

.comparison-error h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #dc2626;
}

.comparison-error p {
  margin: 0 0 16px 0;
  color: #7f1d1d;
}



/* 空状态样式 */
.comparison-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 60px 20px;
  text-align: center;
  color: #6b7280;
}

.comparison-empty-state .empty-icon {
  font-size: 48px;
  color: #d1d5db;
}

.comparison-empty-state h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.comparison-empty-state p {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
}

/* 对比查看操作按钮样式 */
.comparison-view-actions {
  position: sticky;
  bottom: 0;
  background: #fff;
  border-top: 1px solid #e5e6eb;
  padding: 16px 20px;
  display: flex;
  justify-content: center;
  gap: 12px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
}

/* Markdown 渲染样式 */
.markdown-content {
  padding: 16px;
  background: transparent;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #1f2937;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.25;
}

.markdown-content h1 {
  font-size: 2em;
  border-bottom: 1px solid #e5e6eb;
  padding-bottom: 8px;
}

.markdown-content h2 {
  font-size: 1.5em;
  border-bottom: 1px solid #e5e6eb;
  padding-bottom: 8px;
}

.markdown-content p {
  margin-bottom: 16px;
}

.markdown-content code {
  background: #f6f8fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 85%;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.markdown-content pre {
  background: #f6f8fa;
  padding: 16px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 16px 0;
}

.markdown-content pre code {
  background: transparent;
  padding: 0;
}

.markdown-content blockquote {
  border-left: 4px solid #d1d5db;
  padding-left: 16px;
  margin: 16px 0;
  color: #6b7280;
}

.markdown-content ul,
.markdown-content ol {
  margin: 16px 0;
  padding-left: 24px;
}

.markdown-content li {
  margin: 4px 0;
}

.markdown-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 16px 0;
}

.markdown-content th,
.markdown-content td {
  border: 1px solid #e5e6eb;
  padding: 8px 12px;
  text-align: left;
}

.markdown-content th {
  background: #f6f8fa;
  font-weight: 600;
}

/* 标题和按钮布局 */
.comparison-success-header,
.result-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.comparison-success-header h4,
.result-detail-header h4 {
  margin: 0;
}

.comparison-result-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 8px;
  padding: 12px;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

/* 对照组结果预览样式优化 */
.result-meta-small {
  display: flex;
  gap: 6px;
  margin-bottom: 8px;
}

.result-meta-small .arco-tag {
  border-radius: 4px;
  font-weight: 500;
  font-size: 11px;
  padding: 2px 6px;
  border: 1px solid rgba(99, 102, 241, 0.2);
  background: rgba(99, 102, 241, 0.05);
  color: #4338ca;
}

.result-text-preview {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: 8px;
  padding: 16px;
  flex: 1;
  overflow-y: auto;
  min-height: 250px;
  max-height: 400px;
  transition: all 0.2s ease;
  backdrop-filter: blur(5px);
}

.result-text-preview:hover {
  border-color: rgba(99, 102, 241, 0.3);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.result-text-preview pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #334155;
  letter-spacing: 0.01em;
}

.result-actions-small {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
}

.result-actions-small .arco-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.result-actions-small .arco-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
}

/* 对照组错误和空状态样式优化 */
.comparison-error {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  color: #dc2626;
  font-size: 13px;
  font-weight: 500;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 8px;
  padding: 16px 12px;
  margin: 8px 0;
}

.error-icon-small {
  font-size: 18px;
  color: #ef4444;
  opacity: 0.8;
}

.comparison-empty {
  color: #64748b;
  font-size: 13px;
  font-weight: 500;
  text-align: center;
  padding: 24px 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px dashed rgba(148, 163, 184, 0.4);
  border-radius: 8px;
  margin: 8px 0;
}

/* 自定义滚动条样式 - 优化体验 */
.result-text::-webkit-scrollbar,
.streaming-content::-webkit-scrollbar,
.result-container::-webkit-scrollbar,
.result-text-preview::-webkit-scrollbar,
.prompt-section::-webkit-scrollbar {
  width: 8px;
}

.result-text::-webkit-scrollbar-track,
.streaming-content::-webkit-scrollbar-track,
.result-container::-webkit-scrollbar-track,
.result-text-preview::-webkit-scrollbar-track,
.prompt-section::-webkit-scrollbar-track {
  background: rgba(241, 245, 249, 0.3);
  border-radius: 4px;
  margin: 2px;
}

.result-text::-webkit-scrollbar-thumb,
.streaming-content::-webkit-scrollbar-thumb,
.result-container::-webkit-scrollbar-thumb,
.result-text-preview::-webkit-scrollbar-thumb,
.prompt-section::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.5);
  border-radius: 4px;
  transition: all 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.result-text::-webkit-scrollbar-thumb:hover,
.streaming-content::-webkit-scrollbar-thumb:hover,
.result-container::-webkit-scrollbar-thumb:hover,
.result-text-preview::-webkit-scrollbar-thumb:hover,
.prompt-section::-webkit-scrollbar-thumb:hover {
  background: rgba(99, 102, 241, 0.6);
  border-color: rgba(99, 102, 241, 0.2);
}

/* 结果标签样式优化 */
.result-meta .arco-tag {
  border-radius: 6px;
  font-weight: 500;
  font-size: 12px;
  padding: 4px 8px;
  border: 1px solid rgba(99, 102, 241, 0.2);
  background: rgba(99, 102, 241, 0.05);
  color: #4338ca;
}

/* 流式输出标签特殊样式 */
.streaming-header .arco-tag {
  border-radius: 12px;
  font-weight: 600;
  font-size: 11px;
  padding: 3px 8px;
  border: 1px solid rgba(34, 197, 94, 0.3);
  background: rgba(34, 197, 94, 0.1);
  color: #166534;
}

/* 统一响应容器样式 - 平滑过渡 */
.unified-response-container {
  margin: 16px;
  border: 1px solid rgba(34, 197, 94, 0.2);
  border-radius: 12px;
  background: linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%);
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.08);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  max-height: 500px;
}

.unified-response-container:hover {
  box-shadow: 0 6px 20px rgba(34, 197, 94, 0.15);
}

.response-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 14px;
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border-bottom: 1px solid rgba(34, 197, 94, 0.15);
  transition: all 0.3s ease;
  min-height: 44px;
}

.response-title {
  font-weight: 600;
  color: #166534;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
  flex: 1;
  min-width: 0;
}

.response-title:empty {
  display: none;
}

.response-title:not(:empty)::before {
  content: '●';
  color: #22c55e;
  animation: pulse 1.5s ease-in-out infinite;
  transition: all 0.3s ease;
}

/* 完成状态时的样式 */
.unified-response-container.completed .response-title:not(:empty)::before {
  animation: none;
  color: #16a34a;
}

.unified-response-container.completed .response-header {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-bottom-color: rgba(59, 130, 246, 0.15);
}

.unified-response-container.completed {
  border-color: rgba(59, 130, 246, 0.2);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.08);
}

.unified-response-container.completed .response-content {
  max-height: 300px;
  overflow-y: auto;
}

.response-tags {
  display: flex;
  gap: 4px;
  align-items: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.response-tags .arco-tag {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

/* 完成状态下的标签样式 */
.unified-response-container.completed .response-tags .arco-tag {
  background-color: rgba(59, 130, 246, 0.1);
  color: #1e40af;
  border-color: rgba(59, 130, 246, 0.2);
}

/* 完成状态下的操作按钮样式 */
.unified-response-container.completed .response-actions {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-top-color: rgba(59, 130, 246, 0.15);
}

.unified-response-container.completed .response-actions .arco-btn {
  border-color: rgba(59, 130, 246, 0.2);
  color: #1e40af;
}

.unified-response-container.completed .response-actions .arco-btn:hover {
  background-color: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
}

.response-content {
  background: rgba(255, 255, 255, 0.7);
  transition: all 0.3s ease;
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  max-height: 300px;
  scrollbar-width: thin;
  scrollbar-color: rgba(34, 197, 94, 0.3) transparent;
}

.response-content::-webkit-scrollbar {
  width: 6px;
}

.response-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.response-content::-webkit-scrollbar-thumb {
  background: rgba(34, 197, 94, 0.3);
  border-radius: 3px;
}

.response-content::-webkit-scrollbar-thumb:hover {
  background: rgba(34, 197, 94, 0.5);
}

.response-text {
  margin: 0;
  padding: 18px;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.7;
  color: #1f2937;
  background: transparent;
  border: none;
  min-height: 120px;
  transition: all 0.3s ease;
}

.response-actions {
  padding: 12px 18px;
  background: rgba(248, 250, 252, 0.8);
  border-top: 1px solid rgba(34, 197, 94, 0.1);
  opacity: 1;
  transform: translateY(0);
  animation: slideInUp 0.4s ease forwards;
  position: sticky;
  bottom: 0;
  z-index: 10;
  backdrop-filter: blur(8px);
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.response-actions .arco-btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.response-actions .arco-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 等待响应状态样式 */
.response-waiting {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 120px;
  padding: 40px 20px;
  flex: 1;
}

.waiting-indicator {
  text-align: center;
  color: #6b7280;
}

.waiting-indicator p {
  margin: 16px 0 0 0;
  font-size: 14px;
  color: #9ca3af;
}

/* 打字机动画点 */
.typing-dots {
  display: inline-flex;
  gap: 4px;
  align-items: center;
  margin-bottom: 8px;
}

.typing-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #22c55e;
  animation: typingDots 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

.typing-dots span:nth-child(3) {
  animation-delay: 0s;
}

@keyframes typingDots {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* 响应式设计优化 */
@media (max-width: 1200px) {
  .comparison-groups-horizontal {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  }

  .comparison-group-card {
    min-height: 700px;
  }
}

@media (max-width: 768px) {
  .comparison-groups-horizontal {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .comparison-group-card {
    min-height: 600px;
    padding: 16px;
  }

  .result-container {
    min-height: 250px;
    max-height: 400px;
  }

  .result-text {
    min-height: 150px;
    max-height: 300px;
  }

  .result-text-preview {
    min-height: 200px;
    max-height: 300px;
  }
}

/* 大屏幕优化 */
@media (min-width: 1600px) {
  .comparison-groups-horizontal {
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  }

  .comparison-group-card {
    min-height: 900px;
  }

  .result-container {
    min-height: 400px;
    max-height: 600px;
  }
}

.comparison-view-actions {
  display: flex;
  justify-content: center;
  padding-top: 16px;
  border-top: 1px solid #e5e6eb;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .comparison-groups-horizontal {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 16px;
  }
  
  .comparison-group-card {
    min-height: 500px;
  }
}

@media (max-width: 1200px) {
  .comparison-groups-horizontal {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .debug-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .config-panel {
    flex-direction: row;
    gap: 20px;
  }
  
  .config-card {
    flex: 1;
  }
}

@media (max-width: 768px) {
  .prompt-debug-container {
    padding: 10px;
  }
  
  .debug-title {
    font-size: 2rem;
  }
  
  .config-panel {
    flex-direction: column;
  }
  
  .preset-buttons {
    grid-template-columns: 1fr;
  }
  
  .comparison-controls {
    padding: 16px;
  }
  
  .comparison-group-card {
    padding: 16px;
    min-height: 450px;
  }
  
  .prompt-section {
    min-height: 200px;
  }
  
  .result-section {
    min-height: 150px;
  }
}

/* 对照组提示词详情编辑模态框样式 */
.group-prompt-detail {
  padding: 20px 0;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.detail-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
}

.detail-messages {
  max-height: 60vh;
  overflow-y: auto;
  margin-bottom: 24px;
}

.detail-message-item {
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
}

.detail-message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.detail-role-tag {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 14px;
  padding: 4px 12px;
  border-radius: 16px;
  color: #fff;
}

.detail-role-tag.role-system {
  background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
}

.detail-role-tag.role-user {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.detail-role-tag.role-assistant {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.detail-message-actions {
  display: flex;
  gap: 4px;
}

.detail-message-content {
  margin: 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.6;
}

.detail-message-content:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.detail-empty-messages {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
  background: #f9fafb;
  border-radius: 8px;
  border: 2px dashed #d1d5db;
}

.detail-empty-messages .empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.detail-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

/* 聊天界面样式 */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 400px;
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
}

.chat-message {
  margin-bottom: 16px;
  padding: 0 16px;
}

.user-message, .assistant-message {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.user-message {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: white;
  flex-shrink: 0;
}

.user-avatar {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.assistant-avatar {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.message-bubble {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 16px;
  position: relative;
  word-wrap: break-word;
}

.user-bubble {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  border-bottom-right-radius: 4px;
}

.assistant-bubble {
  background: white;
  border: 1px solid #e5e7eb;
  border-bottom-left-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.message-content {
  margin-bottom: 8px;
  line-height: 1.5;
}

.message-content pre {
  margin: 0;
  white-space: pre-wrap;
  font-family: inherit;
}

.message-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 8px;
}

.message-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.message-bubble:hover .message-actions {
  opacity: 1;
}

.user-bubble .message-actions {
  justify-content: flex-end;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dots span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #3b82f6;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.chat-input-container {
  margin-top: auto;
  padding: 16px;
  background: white;
  border-top: 1px solid #e5e7eb;
}

.chat-input-wrapper {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.chat-input {
  flex: 1;
  border-radius: 20px;
  border: 1px solid #d1d5db;
  padding: 8px 16px;
  resize: none;
}

.chat-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input-actions {
  display: flex;
  gap: 8px;
}

.streaming .message-bubble {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.waiting-indicator {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #6b7280;
  font-size: 14px;
}

.waiting-indicator p {
  margin: 0;
}

/* 聊天空状态样式 */
.chat-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  color: #6b7280;
  flex: 1;
}

.empty-chat-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.chat-empty-state h4 {
  margin: 0 0 8px 0;
  color: #374151;
  font-size: 18px;
  font-weight: 600;
}

.chat-empty-state p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

/* 对比查看中的聊天历史样式 */
.comparison-chat-history {
  max-height: 600px;
  overflow-y: auto;
}

.comparison-chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.comparison-chat-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.comparison-chat-messages {
  padding: 16px;
  background: #f8f9fa;
}

.comparison-chat-message {
  margin-bottom: 16px;
}

.comparison-user-message, .comparison-assistant-message {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.comparison-user-message {
  flex-direction: row-reverse;
}

.comparison-message-avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: 600;
  color: white;
  flex-shrink: 0;
}

.comparison-user-avatar {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.comparison-assistant-avatar {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.comparison-message-bubble {
  max-width: 75%;
  padding: 10px 14px;
  border-radius: 14px;
  word-wrap: break-word;
}

.comparison-user-bubble {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  border-bottom-right-radius: 4px;
}

.comparison-assistant-bubble {
  background: white;
  border: 1px solid #e5e7eb;
  border-bottom-left-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.comparison-message-content {
  margin-bottom: 6px;
  line-height: 1.4;
  font-size: 14px;
}

.comparison-message-content pre {
  margin: 0;
  white-space: pre-wrap;
  font-family: inherit;
  font-size: inherit;
}

.comparison-message-meta {
  display: flex;
  gap: 10px;
  font-size: 11px;
  color: #6b7280;
}

.comparison-result-actions {
  padding: 12px 16px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}
</style>