<template>
  <div class="agent-config-container">
    <div class="header-section">
      <h1 class="page-title">{{ route.params.agentName || route.query.agentName || '' }} {{ basicAgentInfo.name || 'Agent' }} 配置</h1>
      <div class="actions">
        <a-button @click="goBack">返回</a-button>
      </div>
    </div>

    <div v-if="loading" class="loading-state">
      <a-spin tip="加载中..."></a-spin>
    </div>
    <div v-else-if="error" class="error-state">
      <a-result status="error" :title="error" :subtitle="'加载数据失败'">
        <template #extra>
          <a-button type="primary" @click="fetchWorkflowConfig">重试</a-button>
          <a-button @click="goBack">返回</a-button>
        </template>
      </a-result>
    </div>
    <div v-else class="config-content">
      <a-tabs default-active-key="features">
        <!-- 功能特性标签页 -->
        <a-tab-pane key="features" title="功能特性">
          <a-alert type="info" class="mb-4">
            当前Agent的工作流功能特性配置
          </a-alert>
          <div class="features-actions">
            <a-button type="primary" size="small" @click="addNewFeature">
              添加功能特性
            </a-button>
          </div>
          <div class="features-container">
            <div v-for="(value, key) in workflowConfig.features" :key="key" class="feature-item">
              <a-card 
                class="feature-card"
                :title="formatFeatureName(key)"
                :bordered="true"
              >
                <template #extra>
                  <div class="feature-actions">
                    <a-button size="mini" class="edit-btn" @click="openFeatureEditor(key, value)">
                      编辑
                    </a-button>
                    <a-button size="mini" class="save-btn" @click="saveFeatureToServer(key)">
                      保存
                    </a-button>
                  </div>
                </template>
                <div class="feature-content">
                  <vue-json-pretty
                    :data="value"
                    :deep="2"
                    :show-double-quotes="true"
                    :show-length="true"
                    :show-line="true"
                    class="json-viewer"
                  />
                </div>
              </a-card>
            </div>
          </div>
        </a-tab-pane>

        <!-- 环境变量标签页 -->
        <a-tab-pane key="env" title="环境变量">
          <a-alert type="info" class="mb-4">
            当前Agent的工作流环境变量配置
          </a-alert>
          <div class="env-actions">
            <a-button size="small" class="edit-btn" @click="openEnvEditor(workflowConfig.environmentVariables)">
              编辑环境变量
            </a-button>
            <a-button size="small" class="save-btn" @click="saveEnvToServer()">
              保存环境变量
            </a-button>
          </div>
          <div class="env-container">
            <vue-json-pretty
              :data="workflowConfig.environmentVariables"
              :deep="2"
              :show-double-quotes="true"
              :show-length="true"
              :show-line="true"
              class="json-viewer"
            />
          </div>
        </a-tab-pane>
        
        <!-- 完整配置标签页 -->
        <a-tab-pane key="fullConfig" title="完整配置">
          <a-alert type="info" class="mb-4">
            当前Agent的工作流完整配置
          </a-alert>
          <div v-if="fullConfigLoading" class="loading-state">
            <a-spin tip="加载中..."></a-spin>
          </div>
          <div v-else-if="fullConfigError" class="error-state">
            <a-alert type="error" :message="fullConfigError" />
            <div class="mt-4">
              <a-button type="primary" @click="fetchWorkflowFullConfig">重试</a-button>
            </div>
          </div>
          <div v-else>
            <div class="full-config-actions">
              <a-button type="primary" size="small" @click="addNewFullConfigItem">
                添加配置项
              </a-button>
            </div>
            <div class="full-config-container">
              <div v-for="(value, key) in workflowFullConfig" :key="key" class="feature-item">
                <a-card 
                  class="feature-card"
                  :title="formatFeatureName(key)"
                  :bordered="true"
                >
                  <template #extra>
                    <div class="feature-actions">
                      <a-button size="mini" class="edit-btn" @click="openFullConfigItemEditor(key, value)">
                        编辑
                      </a-button>
                      <a-button size="mini" class="save-btn" @click="saveFullConfigItemToServer(key)">
                        保存
                      </a-button>
                    </div>
                  </template>
                  <div class="feature-content">
                    <vue-json-pretty
                      :data="value"
                      :deep="2"
                      :show-double-quotes="true"
                      :show-length="true"
                      :show-line="true"
                      class="json-viewer"
                    />
                  </div>
                </a-card>
              </div>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 特性编辑对话框 -->
    <a-modal
      v-model:visible="featureEditorVisible"
      :title="`编辑${formatFeatureName(currentFeatureKey)}配置`"
      :width="800"
      :mask-closable="false"
      @ok="saveFeatureConfig"
      @cancel="featureEditorVisible = false"
    >
      <div class="monaco-editor-container">
        <MonacoEditor
          v-if="featureEditorVisible"
          v-model:value="featureEditorContent"
          :options="editorOptions"
          language="json"
          @change="validateJsonContent"
        />
      </div>
      <div v-if="editorError" class="editor-error">
        {{ editorError }}
      </div>
    </a-modal>

    <!-- 环境变量编辑对话框 -->
    <a-modal
      v-model:visible="envEditorVisible"
      title="编辑环境变量配置"
      :width="800"
      :mask-closable="false"
      @ok="saveEnvConfig"
      @cancel="envEditorVisible = false"
    >
      <div class="monaco-editor-container">
        <MonacoEditor
          v-if="envEditorVisible"
          v-model:value="envEditorContent"
          :options="editorOptions"
          language="json"
          @change="validateJsonContent"
        />
      </div>
      <div v-if="editorError" class="editor-error">
        {{ editorError }}
      </div>
    </a-modal>
    
    <!-- 添加新功能特性对话框 -->
    <a-modal
      v-model:visible="newFeatureVisible"
      title="添加功能特性"
      :width="800"
      :mask-closable="false"
      @ok="saveNewFeature"
      @cancel="newFeatureVisible = false"
    >
      <div class="new-feature-form">
        <a-form :model="newFeatureForm" layout="vertical">
          <a-form-item label="特性名称" required>
            <a-input v-model="newFeatureForm.key" placeholder="请输入特性名称（仅英文和数字）" />
          </a-form-item>
        </a-form>
      </div>
      <div class="monaco-editor-container">
        <MonacoEditor
          v-if="newFeatureVisible"
          v-model:value="newFeatureForm.value"
          :options="editorOptions"
          language="json"
          @change="validateJsonContent"
        />
      </div>
      <div v-if="editorError" class="editor-error">
        {{ editorError }}
      </div>
    </a-modal>
    
    <!-- 添加完整配置项对话框 -->
    <a-modal
      v-model:visible="newFullConfigItemVisible"
      title="添加完整配置项"
      :width="800"
      :mask-closable="false"
      @ok="saveNewFullConfigItem"
      @cancel="newFullConfigItemVisible = false"
    >
      <div class="new-config-form">
        <a-form :model="newFullConfigItemForm" layout="vertical">
          <a-form-item label="配置项名称" required>
            <a-input v-model="newFullConfigItemForm.key" placeholder="请输入配置项名称（仅英文和数字）" />
          </a-form-item>
        </a-form>
      </div>
      <div class="monaco-editor-container">
        <MonacoEditor
          v-if="newFullConfigItemVisible"
          v-model:value="newFullConfigItemForm.value"
          :options="editorOptions"
          language="json"
          @change="validateJsonContent"
        />
      </div>
      <div v-if="editorError" class="editor-error">
        {{ editorError }}
      </div>
    </a-modal>
    
    <!-- 完整配置编辑对话框 -->
    <a-modal
      v-model:visible="fullConfigEditorVisible"
      :title="currentFeatureKey ? `编辑${formatFeatureName(currentFeatureKey)}配置` : '编辑完整配置'"
      :width="800"
      :mask-closable="false"
      @ok="saveFullConfig"
      @cancel="cancelFullConfigEdit"
    >
      <div class="monaco-editor-container">
        <MonacoEditor
          v-if="fullConfigEditorVisible"
          v-model:value="fullConfigEditorContent"
          :options="editorOptions"
          language="json"
          @change="validateJsonContent"
        />
      </div>
      <div v-if="editorError" class="editor-error">
        {{ editorError }}
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { Message } from '@arco-design/web-vue';
import request from '@/utils/request';
import VueJsonPretty from 'vue-json-pretty';
import MonacoEditor from '@guolao/vue-monaco-editor';
import { getWorkflowFullConfig } from '../api/app';
import { 
  saveWorkflowConfig, 
  saveWorkflowFeature, 
  saveWorkflowEnvVariables,
  updateWorkflowFullConfig
} from '../api/workflow';

const router = useRouter();
const route = useRoute();
const clientKey = ref(route.params.clientKey || route.query.clientKey);

const loading = ref(true);
const error = ref(null);
const workflowConfig = ref({
  features: {},
  environmentVariables: {}
});

// 基本Agent信息
const basicAgentInfo = ref({
  name: '',
  description: '',
  icon: '',
  agentId: ''
});

// 完整配置相关
const workflowFullConfig = ref({});
const fullConfigLoading = ref(false);
const fullConfigError = ref('');

// 编辑器相关
const featureEditorVisible = ref(false);
const envEditorVisible = ref(false);
const fullConfigEditorVisible = ref(false);
const currentFeatureKey = ref('');
const featureEditorContent = ref('');
const envEditorContent = ref('');
const fullConfigEditorContent = ref('');
const editorError = ref('');
const editorOptions = {
  theme: 'vs',
  automaticLayout: true,
  tabSize: 2,
  fontSize: 14,
  minimap: {
    enabled: true
  }
};

// 添加新功能特性表单
const newFeatureVisible = ref(false);
const newFeatureForm = ref({
  key: '',
  value: '{}'
});

// 添加新完整配置项表单
const newFullConfigItemVisible = ref(false);
const newFullConfigItemForm = ref({
  key: '',
  value: '{}'
});

// 格式化特性名称
const formatFeatureName = (key) => {
  // 直接返回原始键名，不做任何处理
  return key;
};

// 获取Agent基本信息
const fetchAgentBasicInfo = () => {
  // 直接从路由参数获取Agent信息
  const agentName = route.params.agentName || route.query.agentName || '';
  const agentId = route.params.agentId || route.query.agentId || '';
  
  basicAgentInfo.value = {
    name: route.params.agentName || route.query.agentName || 'Agent',
    description: '',
    icon: '',
    agentId: agentId
  };
};

// 获取工作流配置
const fetchWorkflowConfig = async () => {
  // 检查路由参数中是否已包含配置信息
  if (route.params.workflowConfig || route.query.workflowConfig) {
    try {
      const configData = route.params.workflowConfig || JSON.parse(route.query.workflowConfig);
      workflowConfig.value = {
        features: configData.features || {},
        environmentVariables: configData.environmentVariables || {}
      };
      loading.value = false;
      return; // 如果已有数据，直接返回，不再调用API
    } catch (e) {
      console.warn('解析路由传递的配置信息失败:', e);
      // 解析失败时继续使用API获取
    }
  }
  
  loading.value = true;
  error.value = null;
  
  try {
    const response = await request.get(`/llm/workflow/config/feature?clientKey=${clientKey.value}`);
    if (response && response.data) {
      workflowConfig.value = {
        features: response.data.features || {},
        environmentVariables: response.data.environmentVariables || {}
      };
      
      console.log('工作流配置数据:', workflowConfig.value);
    } else {
      throw new Error('获取工作流配置数据为空');
    }
  } catch (err) {
    console.error('获取工作流配置失败:', err);
    error.value = err.message || '获取工作流配置失败';
  } finally {
    loading.value = false;
  }
};

// 获取工作流完整配置
const fetchWorkflowFullConfig = async () => {
  // 检查路由参数中是否已包含完整配置信息
  if (route.params.workflowFullConfig || route.query.workflowFullConfig) {
    try {
      const fullConfigData = route.params.workflowFullConfig || JSON.parse(route.query.workflowFullConfig);
      workflowFullConfig.value = fullConfigData || {};
      fullConfigLoading.value = false;
      return; // 如果已有数据，直接返回，不再调用API
    } catch (e) {
      console.warn('解析路由传递的完整配置信息失败:', e);
      // 解析失败时继续使用API获取
    }
  }
  
  fullConfigLoading.value = true;
  fullConfigError.value = '';
  
  try {
    const response = await getWorkflowFullConfig(clientKey.value);
    if (response && response.data) {
      workflowFullConfig.value = response.data || {};
      
      console.log('工作流完整配置数据:', workflowFullConfig.value);
    } else {
      throw new Error('获取工作流完整配置数据为空');
    }
  } catch (err) {
    console.error('获取工作流完整配置失败:', err);
    fullConfigError.value = err.message || '获取工作流完整配置失败';
  } finally {
    fullConfigLoading.value = false;
  }
};

// 打开特性编辑器
const openFeatureEditor = (key, value) => {
  currentFeatureKey.value = key;
  featureEditorContent.value = JSON.stringify(value, null, 2);
  editorError.value = '';
  featureEditorVisible.value = true;
  
  // 确保在下一个渲染周期中编辑器能正确获取到内容
  setTimeout(() => {
    if (featureEditorVisible.value) {
      featureEditorContent.value = JSON.stringify(value, null, 2);
    }
  }, 100);
};

// 打开环境变量编辑器
const openEnvEditor = (value) => {
  envEditorContent.value = JSON.stringify(value, null, 2);
  editorError.value = '';
  envEditorVisible.value = true;
  
  // 确保在下一个渲染周期中编辑器能正确获取到内容
  setTimeout(() => {
    if (envEditorVisible.value) {
      envEditorContent.value = JSON.stringify(value, null, 2);
    }
  }, 100);
};

// 添加新的功能特性
const addNewFeature = () => {
  newFeatureForm.value = {
    key: '',
    value: '{}'
  };
  editorError.value = '';
  newFeatureVisible.value = true;
};

// 保存新功能特性
const saveNewFeature = () => {
  if (!newFeatureForm.value.key) {
    Message.error('特性名称不能为空');
    return;
  }
  
  if (!validateJsonContent(newFeatureForm.value.value)) {
    return;
  }
  
  try {
    const key = newFeatureForm.value.key;
    const value = JSON.parse(newFeatureForm.value.value);
    
    // 检查是否已存在同名特性
    if (workflowConfig.value.features[key]) {
      Message.warning(`特性"${key}"已存在，如需修改请使用编辑功能`);
      return;
    }
    
    // 添加新特性
    workflowConfig.value.features[key] = value;
    newFeatureVisible.value = false;
    
    // 保存到服务器
    saveFeatureToServer(key);
    
    Message.success(`功能特性"${key}"添加成功`);
  } catch (err) {
    editorError.value = `保存失败: ${err.message}`;
  }
};

// 添加新的完整配置项
const addNewFullConfigItem = () => {
  newFullConfigItemForm.value = {
    key: '',
    value: '{}'
  };
  editorError.value = '';
  newFullConfigItemVisible.value = true;
};

// 保存新完整配置项
const saveNewFullConfigItem = () => {
  if (!newFullConfigItemForm.value.key) {
    Message.error('配置项名称不能为空');
    return;
  }
  
  if (!validateJsonContent(newFullConfigItemForm.value.value)) {
    return;
  }
  
  try {
    const key = newFullConfigItemForm.value.key;
    const value = JSON.parse(newFullConfigItemForm.value.value);
    
    // 检查是否已存在同名配置项
    if (workflowFullConfig.value[key]) {
      Message.warning(`配置项"${key}"已存在，如需修改请使用编辑功能`);
      return;
    }
    
    // 添加新配置项
    workflowFullConfig.value[key] = value;
    newFullConfigItemVisible.value = false;
    
    // 保存到服务器
    saveFullConfigItemToServer(key);
    
    Message.success(`配置项"${key}"添加成功`);
  } catch (err) {
    editorError.value = `保存失败: ${err.message}`;
  }
};

// 打开完整配置编辑器
const openFullConfigEditor = (value) => {
  fullConfigEditorContent.value = JSON.stringify(value, null, 2);
  editorError.value = '';
  fullConfigEditorVisible.value = true;
  
  // 确保在下一个渲染周期中编辑器能正确获取到内容
  setTimeout(() => {
    if (fullConfigEditorVisible.value) {
      fullConfigEditorContent.value = JSON.stringify(value, null, 2);
    }
  }, 100);
};

// 打开完整配置项编辑器
const openFullConfigItemEditor = (key, value) => {
  currentFeatureKey.value = key;
  fullConfigEditorContent.value = JSON.stringify(value, null, 2);
  editorError.value = '';
  fullConfigEditorVisible.value = true;
  
  // 确保在下一个渲染周期中编辑器能正确获取到内容
  setTimeout(() => {
    if (fullConfigEditorVisible.value) {
      fullConfigEditorContent.value = JSON.stringify(value, null, 2);
    }
  }, 100);
};

// 验证JSON内容
const validateJsonContent = (content) => {
  try {
    JSON.parse(content);
    editorError.value = '';
    return true;
  } catch (err) {
    editorError.value = `JSON格式错误: ${err.message}`;
    return false;
  }
};

// 保存特性配置
const saveFeatureConfig = () => {
  if (!validateJsonContent(featureEditorContent.value)) {
    return;
  }
  
  try {
    const updatedValue = JSON.parse(featureEditorContent.value);
    workflowConfig.value.features[currentFeatureKey.value] = updatedValue;
    featureEditorVisible.value = false;
    Message.success(`${formatFeatureName(currentFeatureKey.value)}配置更新成功`);
  } catch (err) {
    editorError.value = `保存失败: ${err.message}`;
  }
};

// 保存环境变量配置
const saveEnvConfig = () => {
  if (!validateJsonContent(envEditorContent.value)) {
    return;
  }
  
  try {
    const updatedValue = JSON.parse(envEditorContent.value);
    workflowConfig.value.environmentVariables = updatedValue;
    envEditorVisible.value = false;
    Message.success('环境变量配置更新成功');
  } catch (err) {
    editorError.value = `保存失败: ${err.message}`;
  }
};

// 保存完整配置
const saveFullConfig = () => {
  if (!validateJsonContent(fullConfigEditorContent.value)) {
    return;
  }
  
  try {
    const updatedValue = JSON.parse(fullConfigEditorContent.value);
    
    // 如果是编辑单个配置项
    if (currentFeatureKey.value) {
      workflowFullConfig.value[currentFeatureKey.value] = updatedValue;
      Message.success(`${formatFeatureName(currentFeatureKey.value)}配置更新成功`);
    } else {
      // 编辑整个配置
      workflowFullConfig.value = updatedValue;
      Message.success('完整配置更新成功');
    }
    
    fullConfigEditorVisible.value = false;
    currentFeatureKey.value = ''; // 重置当前编辑的键
  } catch (err) {
    editorError.value = `保存失败: ${err.message}`;
  }
};

// 取消编辑完整配置
const cancelFullConfigEdit = () => {
  fullConfigEditorVisible.value = false;
  currentFeatureKey.value = ''; // 重置当前编辑的键
};

// 保存特性配置到服务器
const saveFeatureToServer = async (key) => {
  try {
    const featureValue = workflowConfig.value.features[key];
    await saveWorkflowFeature(clientKey.value, key, featureValue);
    Message.success(`${formatFeatureName(key)}配置已保存到服务器`);
  } catch (err) {
    console.error('保存特性配置失败:', err);
    Message.error(`保存失败: ${err.message || '未知错误'}`);
  }
};

// 保存环境变量到服务器
const saveEnvToServer = async () => {
  try {
    await saveWorkflowEnvVariables(clientKey.value, workflowConfig.value.environmentVariables);
    Message.success('环境变量配置已保存到服务器');
  } catch (err) {
    console.error('保存环境变量失败:', err);
    Message.error(`保存失败: ${err.message || '未知错误'}`);
  }
};

// 保存完整配置项到服务器
const saveFullConfigItemToServer = async (key) => {
  try {
    // 创建包含单个配置项的对象
    const data = {
      [key]: workflowFullConfig.value[key]
    };
    
    // 使用完整配置更新API
    await updateWorkflowFullConfig(clientKey.value, data);
    Message.success(`${formatFeatureName(key)}配置已保存到服务器`);
  } catch (err) {
    console.error('保存配置项失败:', err);
    Message.error(`保存失败: ${err.message || '未知错误'}`);
  }
};

// 保存全部配置到服务器
const saveAllConfigToServer = async () => {
  try {
    // 使用完整配置更新API保存所有配置
    await updateWorkflowFullConfig(clientKey.value, workflowFullConfig.value);
    Message.success('全部配置已保存到服务器');
  } catch (err) {
    console.error('保存全部配置失败:', err);
    Message.error(`保存失败: ${err.message || '未知错误'}`);
  }
};

// 返回上一页
const goBack = () => {
  router.go(-1);
};

onMounted(() => {
  fetchAgentBasicInfo();
  fetchWorkflowConfig();
  fetchWorkflowFullConfig();
});
</script>

<style scoped>
.agent-config-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
  height: 100%;
  overflow-y: auto;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
}

.actions {
  display: flex;
  gap: 12px;
}

.config-content {
  background-color: #fff;
  border-radius: 4px;
}

.loading-state, .error-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}

.ml-2 {
  margin-left: 8px;
}

.features-container, .full-config-container {
  margin-top: 16px;
  max-height: none;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding-bottom: 40px;
}

.feature-item {
  margin-bottom: 16px;
}

.feature-card {
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

:deep(.arco-card-header) {
  border-bottom: 1px solid #e5e5e5 !important;
  padding-bottom: 12px !important;
}

:deep(.arco-card-header-title) {
  font-weight: 500;
  font-size: 16px;
  text-decoration: none;
}

.feature-content {
  margin-top: 12px;
  background-color: #f9f9f9;
  padding: 12px;
  border-radius: 4px;
  max-height: 400px;
  overflow-y: auto;
}

.feature-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.env-container {
  margin-top: 16px;
  background-color: #f9f9f9;
  padding: 16px;
  border-radius: 4px;
  max-height: 500px;
  overflow-y: auto;
}

.env-actions, .features-actions, .full-config-actions {
  margin-top: 16px;
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.monaco-editor-container {
  height: 400px;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
}

.editor-error {
  margin-top: 12px;
  color: #f5222d;
  font-size: 14px;
}

.json-viewer {
  overflow-y: auto;
  max-height: 100%;
}

.save-btn {
  margin-left: 8px;
  border-radius: 4px;
  background-color: #52c41a;
  color: white;
}

.edit-btn {
  border-radius: 4px;
}

.new-feature-form, .new-config-form {
  margin-top: 16px;
}
</style> 