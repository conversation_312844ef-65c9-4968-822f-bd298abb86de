{"nodes": [{"id": "1722935422791", "data": {"code": "def main(arg1: int, arg2: int) -> dict:\n    return {\n        \"result\": arg1 + arg2,\n    }\n", "desc": "conversationAnalysis", "type": "method", "title": "conversationAnalysis", "outputs": {"intent": {"type": "string", "children": null}, "products": {"type": "object", "children": null}, "normQuery": {"type": "string", "children": null}, "productTypes": {"type": "object", "children": null}, "productCompany": {"type": "object", "children": null}, "rewrittenQuery": {"type": "string", "children": null}, "productCategorys": {"type": "object", "children": null}, "recommendedProduct": {"type": "string", "children": null}, "unNormalizedProducts": {"type": "object", "children": null}, "categoryProductsModels": {"type": "object", "children": null}, "normalizedCmpTargetProducts": {"type": "object", "children": null}, "normalizedOurTargetProducts": {"type": "object", "children": null}}, "selected": false, "variables": [{"variable": "query", "value_selector": ["sys", "query"]}, {"variable": "messages", "value_selector": ["sys", "history"]}, {"variable": "agentId", "value_selector": ["sys", "agent_id"]}], "dependencies": [], "component_name": "conversationAnalysis", "success": true, "cost_time": 1940, "result_outputs": {"normQuery": "一体机微波功能可以用玻璃容器吗？", "recommendedProduct": "", "categoryProductsModels": [], "productCompany": {}, "normalizedCmpTargetProducts": [], "rewrittenQuery": "一体机微波功能可以用玻璃容器吗？", "normalizedOurTargetProducts": [], "productCategorys": [], "unNormalizedProducts": [], "productTypes": ["一体机"], "intent": "product_attribute_query", "products": []}}, "position": {"x": -495.4703783299843, "y": 1097.588012416873}}, {"id": "1728373697410", "data": {"desc": "", "type": "start", "title": "开始", "selected": false, "variables": [], "success": true, "cost_time": 85, "result_inputs": {"sys.query": "一体机微波功能可以用玻璃容器吗？", "user_name": "", "sys.session_id": "1930445140745428992", "city_area": "西南地区", "upload_files": [], "source": "", "sys.all_history": [{"role": "user", "content": "一体机微波功能可以用玻璃容器吗？", "reasoningContent": null, "extraData": null, "summary": null, "rawMessage": null, "audio": null, "toolMsg": false}], "version": "", "sys.current_date_info": "2025-06-05 10点，农历五月初十，星期四\n节气：芒种", "sys.user_id": "1874714578769649664", "sys.streaming": true, "sys.trace_id": "app-fwfewfwefwe2fw", "user_info": "{\"姓名\": \"雷政达\", \"简介\": \"您是我最尊贵的用户\"}", "sys.history": [{"role": "user", "content": "一体机微波功能可以用玻璃容器吗？", "reasoningContent": null, "extraData": null, "summary": null, "rawMessage": null, "audio": null, "toolMsg": false}], "session_files": [], "sys.agent_id": "105085634255589376", "weather": "成都市武侯区,天气阴，气温22℃，体感温度25℃，东风1级(风速0.5m/s)，湿度86%，能见度10.0km。今日日出时间06:01，日落时间20:04。温馨提示：冷热适宜，感觉很舒适。", "season": "夏", "location": "武侯区", "sys.debug": true, "weather_data_forecast": {"city": {"cityId": 285195, "counname": "中国", "ianatimezone": "Asia/Shanghai", "name": "武侯区", "pname": "四川省", "secondaryname": "成都市", "timezone": "8"}, "forecast": [{"conditionDay": "多云", "conditionIdDay": "1", "conditionNight": "阴", "conditionIdNight": "2", "humidity": "54", "pop": "0", "predictDate": "2025-06-05", "tempDay": "29", "tempNight": "20", "updatetime": "2025-06-05 07:00:00", "windDegreesDay": "0", "windDegreesNight": "0", "windDirDay": "北风", "windDirNight": "北风", "windLevelDay": "1", "windLevelNight": "1", "qpf": "0.0", "uvi": "7", "moonphase": null, "moonrise": null, "moonset": null, "sunrise": null, "sunset": null, "windSpeedDay": null, "windSpeedNight": null}, {"conditionDay": "多云", "conditionIdDay": "1", "conditionNight": "阴", "conditionIdNight": "2", "humidity": "54", "pop": "0", "predictDate": "2025-06-06", "tempDay": "31", "tempNight": "22", "updatetime": "2025-06-05 07:00:00", "windDegreesDay": "0", "windDegreesNight": "0", "windDirDay": "北风", "windDirNight": "北风", "windLevelDay": "1", "windLevelNight": "1", "qpf": "0.0", "uvi": "6", "moonphase": null, "moonrise": null, "moonset": null, "sunrise": null, "sunset": null, "windSpeedDay": null, "windSpeedNight": null}, {"conditionDay": "多云", "conditionIdDay": "1", "conditionNight": "阴", "conditionIdNight": "2", "humidity": "59", "pop": "0", "predictDate": "2025-06-07", "tempDay": "30", "tempNight": "20", "updatetime": "2025-06-05 07:00:00", "windDegreesDay": "0", "windDegreesNight": "0", "windDirDay": "北风", "windDirNight": "北风", "windLevelDay": "1", "windLevelNight": "1", "qpf": "0.0", "uvi": "5", "moonphase": null, "moonrise": null, "moonset": null, "sunrise": null, "sunset": null, "windSpeedDay": null, "windSpeedNight": null}, {"conditionDay": "多云", "conditionIdDay": "1", "conditionNight": "多云", "conditionIdNight": "31", "humidity": "56", "pop": "0", "predictDate": "2025-06-08", "tempDay": "31", "tempNight": "20", "updatetime": "2025-06-05 07:00:00", "windDegreesDay": "0", "windDegreesNight": "0", "windDirDay": "北风", "windDirNight": "北风", "windLevelDay": "1", "windLevelNight": "1", "qpf": "0.0", "uvi": "6", "moonphase": null, "moonrise": null, "moonset": null, "sunrise": null, "sunset": null, "windSpeedDay": null, "windSpeedNight": null}, {"conditionDay": "晴", "conditionIdDay": "0", "conditionNight": "晴", "conditionIdNight": "30", "humidity": "54", "pop": "0", "predictDate": "2025-06-09", "tempDay": "33", "tempNight": "20", "updatetime": "2025-06-05 07:00:00", "windDegreesDay": "0", "windDegreesNight": "0", "windDirDay": "北风", "windDirNight": "北风", "windLevelDay": "1", "windLevelNight": "1", "qpf": "0.0", "uvi": "7", "moonphase": null, "moonrise": null, "moonset": null, "sunrise": null, "sunset": null, "windSpeedDay": null, "windSpeedNight": null}, {"conditionDay": "晴", "conditionIdDay": "0", "conditionNight": "晴", "conditionIdNight": "30", "humidity": "49", "pop": "0", "predictDate": "2025-06-10", "tempDay": "37", "tempNight": "21", "updatetime": "2025-06-05 07:00:00", "windDegreesDay": "0", "windDegreesNight": "0", "windDirDay": "北风", "windDirNight": "北风", "windLevelDay": "1", "windLevelNight": "1", "qpf": "0.0", "uvi": "9", "moonphase": null, "moonrise": null, "moonset": null, "sunrise": null, "sunset": null, "windSpeedDay": null, "windSpeedNight": null}]}, "weatherForecast6": "成都市武侯区6天天气预报：\n2025-06-05: 多云，气温20-29℃，北风1级。\n2025-06-06: 多云，气温22-31℃，北风1级。\n2025-06-07: 多云，气温20-30℃，北风1级。\n2025-06-08: 多云，气温20-31℃，北风1级。\n2025-06-09: 晴，气温20-33℃，北风1级。\n2025-06-10: 晴，气温21-37℃，北风1级。\n", "weather_data_condition": {"city": {"cityId": 285195, "counname": "中国", "ianatimezone": "Asia/Shanghai", "name": "武侯区", "pname": "四川省", "secondaryname": "成都市", "timezone": "8"}, "condition": {"condition": "阴", "conditionId": "85", "humidity": "86", "icon": "2", "pressure": "951", "realFeel": "25", "sunRise": "2025-06-05 06:01:00", "sunSet": "2025-06-05 20:04:00", "temp": "22", "tips": "冷热适宜，感觉很舒适。", "updatetime": "2025-06-05 09:00:08", "uvi": "1", "vis": "10000", "windDegrees": "90", "windDir": "东风", "windLevel": "1", "windSpeed": "0.5"}}, "sys.workflow_start_time": 1749088963714}, "result_outputs": {"sys.query": "一体机微波功能可以用玻璃容器吗？", "user_name": "", "sys.session_id": "1930445140745428992", "city_area": "西南地区", "upload_files": [], "source": "", "sys.all_history": [{"role": "user", "content": "一体机微波功能可以用玻璃容器吗？", "reasoningContent": null, "extraData": null, "summary": null, "rawMessage": null, "audio": null, "toolMsg": false}], "version": "", "sys.current_date_info": "2025-06-05 10点，农历五月初十，星期四\n节气：芒种", "sys.user_id": "1874714578769649664", "sys.streaming": true, "sys.trace_id": "app-fwfewfwefwe2fw", "user_info": "{\"姓名\": \"雷政达\", \"简介\": \"您是我最尊贵的用户\"}", "sys.history": [{"role": "user", "content": "一体机微波功能可以用玻璃容器吗？", "reasoningContent": null, "extraData": null, "summary": null, "rawMessage": null, "audio": null, "toolMsg": false}], "session_files": [], "sys.agent_id": "105085634255589376", "weather": "成都市武侯区,天气阴，气温22℃，体感温度25℃，东风1级(风速0.5m/s)，湿度86%，能见度10.0km。今日日出时间06:01，日落时间20:04。温馨提示：冷热适宜，感觉很舒适。", "season": "夏", "location": "武侯区", "sys.debug": true, "weather_data_forecast": {"city": {"cityId": 285195, "counname": "中国", "ianatimezone": "Asia/Shanghai", "name": "武侯区", "pname": "四川省", "secondaryname": "成都市", "timezone": "8"}, "forecast": [{"conditionDay": "多云", "conditionIdDay": "1", "conditionNight": "阴", "conditionIdNight": "2", "humidity": "54", "pop": "0", "predictDate": "2025-06-05", "tempDay": "29", "tempNight": "20", "updatetime": "2025-06-05 07:00:00", "windDegreesDay": "0", "windDegreesNight": "0", "windDirDay": "北风", "windDirNight": "北风", "windLevelDay": "1", "windLevelNight": "1", "qpf": "0.0", "uvi": "7", "moonphase": null, "moonrise": null, "moonset": null, "sunrise": null, "sunset": null, "windSpeedDay": null, "windSpeedNight": null}, {"conditionDay": "多云", "conditionIdDay": "1", "conditionNight": "阴", "conditionIdNight": "2", "humidity": "54", "pop": "0", "predictDate": "2025-06-06", "tempDay": "31", "tempNight": "22", "updatetime": "2025-06-05 07:00:00", "windDegreesDay": "0", "windDegreesNight": "0", "windDirDay": "北风", "windDirNight": "北风", "windLevelDay": "1", "windLevelNight": "1", "qpf": "0.0", "uvi": "6", "moonphase": null, "moonrise": null, "moonset": null, "sunrise": null, "sunset": null, "windSpeedDay": null, "windSpeedNight": null}, {"conditionDay": "多云", "conditionIdDay": "1", "conditionNight": "阴", "conditionIdNight": "2", "humidity": "59", "pop": "0", "predictDate": "2025-06-07", "tempDay": "30", "tempNight": "20", "updatetime": "2025-06-05 07:00:00", "windDegreesDay": "0", "windDegreesNight": "0", "windDirDay": "北风", "windDirNight": "北风", "windLevelDay": "1", "windLevelNight": "1", "qpf": "0.0", "uvi": "5", "moonphase": null, "moonrise": null, "moonset": null, "sunrise": null, "sunset": null, "windSpeedDay": null, "windSpeedNight": null}, {"conditionDay": "多云", "conditionIdDay": "1", "conditionNight": "多云", "conditionIdNight": "31", "humidity": "56", "pop": "0", "predictDate": "2025-06-08", "tempDay": "31", "tempNight": "20", "updatetime": "2025-06-05 07:00:00", "windDegreesDay": "0", "windDegreesNight": "0", "windDirDay": "北风", "windDirNight": "北风", "windLevelDay": "1", "windLevelNight": "1", "qpf": "0.0", "uvi": "6", "moonphase": null, "moonrise": null, "moonset": null, "sunrise": null, "sunset": null, "windSpeedDay": null, "windSpeedNight": null}, {"conditionDay": "晴", "conditionIdDay": "0", "conditionNight": "晴", "conditionIdNight": "30", "humidity": "54", "pop": "0", "predictDate": "2025-06-09", "tempDay": "33", "tempNight": "20", "updatetime": "2025-06-05 07:00:00", "windDegreesDay": "0", "windDegreesNight": "0", "windDirDay": "北风", "windDirNight": "北风", "windLevelDay": "1", "windLevelNight": "1", "qpf": "0.0", "uvi": "7", "moonphase": null, "moonrise": null, "moonset": null, "sunrise": null, "sunset": null, "windSpeedDay": null, "windSpeedNight": null}, {"conditionDay": "晴", "conditionIdDay": "0", "conditionNight": "晴", "conditionIdNight": "30", "humidity": "49", "pop": "0", "predictDate": "2025-06-10", "tempDay": "37", "tempNight": "21", "updatetime": "2025-06-05 07:00:00", "windDegreesDay": "0", "windDegreesNight": "0", "windDirDay": "北风", "windDirNight": "北风", "windLevelDay": "1", "windLevelNight": "1", "qpf": "0.0", "uvi": "9", "moonphase": null, "moonrise": null, "moonset": null, "sunrise": null, "sunset": null, "windSpeedDay": null, "windSpeedNight": null}]}, "weatherForecast6": "成都市武侯区6天天气预报：\n2025-06-05: 多云，气温20-29℃，北风1级。\n2025-06-06: 多云，气温22-31℃，北风1级。\n2025-06-07: 多云，气温20-30℃，北风1级。\n2025-06-08: 多云，气温20-31℃，北风1级。\n2025-06-09: 晴，气温20-33℃，北风1级。\n2025-06-10: 晴，气温21-37℃，北风1级。\n", "weather_data_condition": {"city": {"cityId": 285195, "counname": "中国", "ianatimezone": "Asia/Shanghai", "name": "武侯区", "pname": "四川省", "secondaryname": "成都市", "timezone": "8"}, "condition": {"condition": "阴", "conditionId": "85", "humidity": "86", "icon": "2", "pressure": "951", "realFeel": "25", "sunRise": "2025-06-05 06:01:00", "sunSet": "2025-06-05 20:04:00", "temp": "22", "tips": "冷热适宜，感觉很舒适。", "updatetime": "2025-06-05 09:00:08", "uvi": "1", "vis": "10000", "windDegrees": "90", "windDir": "东风", "windLevel": "1", "windSpeed": "0.5"}}, "sys.workflow_start_time": 1749088963714}}, "position": {"x": -3401.1435709714237, "y": 469.5898703645564}}, {"id": "1728373706400", "data": {"code": "\ndef main(arg1: int, arg2: int) -> dict:\n    return {\n        \"result\": arg1 + arg2,\n    }\n", "desc": "", "type": "method", "title": "cookeryGodRetrieval", "outputs": {"qaGroups": {"type": "string", "children": null}, "cookbooks": {"type": "string", "children": null}, "weatherInfo": {"type": "string", "children": null}, "cookbookCards": {"type": "string", "children": null}, "placeholderMap": {"type": "object", "children": null}, "cookeryQaAnswer": {"type": "string", "children": null}, "otherFileChunks": {"type": "string", "children": null}, "regionalDietary": {"type": "string", "children": null}, "cookbookExamples": {"type": "string", "children": null}, "cookeryFileChunks": {"type": "string", "children": null}, "productFileChunks": {"type": "string", "children": null}, "foodstuffNutrition": {"type": "string", "children": null}, "foodstuffsFileChunks": {"type": "string", "children": null}, "mealPlanningCookbooks": {"type": "string", "children": null}, "obesityNutritionFileChunks": {"type": "string", "children": null}}, "selected": false, "variables": [{"variable": "rewritten<PERSON><PERSON><PERSON>", "value_selector": ["1741239358087", "output"]}, {"variable": "productNames", "value_selector": ["1728375675821", "productNames"]}, {"variable": "otherIntents", "value_selector": ["1728375675821", "otherIntents"]}, {"variable": "otherIntentsValues", "value_selector": ["1728375675821", "otherIntentsValues"]}, {"variable": "agentId", "value_selector": ["sys", "agent_id"]}, {"variable": "intent", "value_selector": ["1728375675821", "intent"]}, {"variable": "productTypes", "value_selector": ["1728375675821", "productTypes"]}, {"variable": "foodstuffs", "value_selector": ["1728375675821", "foodstuffs"]}, {"variable": "wellnessLabel", "value_selector": ["1728375675821", "wellnessLabel"]}, {"variable": "recommendedCookbooks", "value_selector": ["1728375675821", "recommendedCookbooks"]}, {"variable": "imageAnalysis", "value_selector": ["1739857618889", "imageAnalysis"]}], "component_name": "cookeryGodRetrieval", "success": true, "cost_time": 2438, "result_outputs": {"qaGroups": [{"question": "哪些材质的容器不能放进微波炉？", "answers": ["金属、搪瓷、锡纸、泡沫盒、方便面盒、漆器、木、竹、纸制品等有金属质地或者易燃的容器，都不可以放进微波炉。"], "score": 0.1019287109375}], "obesityNutritionFileChunks": [], "weatherInfo": "", "placeholderMap": {}, "cookeryFileChunks": [], "foodstuffNutrition": [], "mealPlanningCookbooks": {}, "productFileChunks": [{"chunks": [{"id": "181699269375143936_83", "content": "| 额定电压 | 220V~ |\n| 额定频率 | 50Hz |\n| 额定功率 | 2950W |\n| 微波输入功率 | 1500W |\n| 微波输出功率 | 900W |\n| 微波额定频率 | 2450MHz |\n| 总容积 | 45L |\n| 有效容积 | 36L |\n| 噪声(声功率级) | 65dB(A) |\n| 净重 | 38.1kg |\n| 外形尺寸（mm） 宽×高×深 | 595×455×535 |\n| 名称 | 数量 |\n| --- | --- |\n| 说明书 | 1份 |\n| ST3.5×30自攻螺钉 | 2枚 |\n| 烤架 | 1个 |\n| 序号 | 名称 | 数量 |\n| --- | --- | --- |\n| 4 | 烤盘 | 1个 |\n| 5 | 吸水球 | 1个 |\n| 6 | 除垢剂 | 2袋 |\n烹饪用具选择不同的烹饪方式,选择适当的烹饪容器十分必要,请参照下表使用。![](https://1)注：在使用微波模式时，建议使用广口的微波炉专用的玻璃容器或者陶瓷容器。例如:禁止使用缩口容器。例如:安装说明在橱柜的设定位置上，按下面安装图示设定方孔，将器具平稳嵌入该方孔，注意不得倾斜安置，具体开孔尺寸（mm）见下表:\n| 全嵌开孔尺寸（宽×高×深） | 600 | 460 | 565 |\n| --- | --- | --- | --- |\n| 半嵌开孔尺寸（宽×高×深） | 560 | 450 | 550 |\n", "orderNo": [83, 84, 85, 86, 87, 88, 89, 90, 91]}], "fileId": "181699269375143936", "fileName": "一体机-WZKQS-36-C9366A说明书.pdf", "knowledgeBaseId": 137933822469500928, "productNames": ["WZKQS-36-C9366A"], "productType": "一体机", "miniChunks": [{"id": "181699269375143936_83", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 181699269375143936, "content": "{{T0}}", "type": 3, "url": "https://roki-ai-rag.oss-cn-hangzhou.aliyuncs.com/rag/chunk/2025-05-16/1747358965832-bc0610d3.png", "table": "\n\n| 型号 | WZKQS-36-C9366A |\n| --- | --- |\n| 额定电压 | 220V~ |\n| 额定频率 | 50Hz |\n| 额定功率 | 2950W |\n| 微波输入功率 | 1500W |\n| 微波输出功率 | 900W |\n| 微波额定频率 | 2450MHz |\n| 总容积 | 45L |\n| 有效容积 | 36L |\n| 噪声(声功率级) | 65dB(A) |\n| 净重 | 38.1kg |\n| 外形尺寸（mm） 宽×高×深 | 595×455×535 |\n\n", "orderNo": 83, "createdAt": "2025-05-16T09:30:07", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "181699269375143936_84", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 181699269375143936, "content": "{{T1}}", "type": 3, "url": "https://roki-ai-rag.oss-cn-hangzhou.aliyuncs.com/rag/chunk/2025-05-16/1747358967751-0ba51900.png", "table": "\n\n| 名称 | 数量 |\n| --- | --- |\n| 说明书 | 1份 |\n| ST3.5×30自攻螺钉 | 2枚 |\n| 烤架 | 1个 |\n\n", "orderNo": 84, "createdAt": "2025-05-16T09:30:07", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "181699269375143936_85", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 181699269375143936, "content": "{{T2}}", "type": 3, "url": "https://roki-ai-rag.oss-cn-hangzhou.aliyuncs.com/rag/chunk/2025-05-16/1747358967980-1144aa21.png", "table": "\n\n| 序号 | 名称 | 数量 |\n| --- | --- | --- |\n| 4 | 烤盘 | 1个 |\n| 5 | 吸水球 | 1个 |\n| 6 | 除垢剂 | 2袋 |\n\n", "orderNo": 85, "createdAt": "2025-05-16T09:30:07", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "181699269375143936_86", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 181699269375143936, "content": "烹饪用具选择不同的烹饪方式,选择适当的烹饪容器十分必要,请参照下表使用。", "type": 1, "url": "", "table": "", "orderNo": 86, "createdAt": "2025-05-16T09:30:07", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "181699269375143936_87", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 181699269375143936, "content": "{{I3}}", "type": 2, "url": "https://roki-ai-rag.oss-cn-hangzhou.aliyuncs.com/rag/chunk/2025-05-16/1747358970959-22a6ec39.png", "table": "", "orderNo": 87, "createdAt": "2025-05-16T09:30:07", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "181699269375143936_88", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 181699269375143936, "content": "注：在使用微波模式时，建议使用广口的微波炉专用的玻璃容器或者陶瓷容器。例如:禁止使用缩口容器。例如:", "type": 1, "url": "", "table": "", "orderNo": 88, "createdAt": "2025-05-16T09:30:07", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "181699269375143936_89", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 181699269375143936, "content": "安装说明在橱柜的设定位置上，按下面安装图示设定方孔，将器具平稳嵌入该方孔，注意不得倾斜安置，具体开孔", "type": 1, "url": "", "table": "", "orderNo": 89, "createdAt": "2025-05-16T09:30:07", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "181699269375143936_90", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 181699269375143936, "content": "尺寸（mm）见下表:", "type": 1, "url": "", "table": "", "orderNo": 90, "createdAt": "2025-05-16T09:30:07", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "181699269375143936_91", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 181699269375143936, "content": "{{T3}}", "type": 3, "url": "https://roki-ai-rag.oss-cn-hangzhou.aliyuncs.com/rag/chunk/2025-05-16/1747358972227-8a8c326e.png", "table": "\n\n| 全嵌开孔尺寸（宽×高×深） | 600 | 460 | 565 |\n| --- | --- | --- | --- |\n| 半嵌开孔尺寸（宽×高×深） | 560 | 450 | 550 |\n\n", "orderNo": 91, "createdAt": "2025-05-16T09:30:07", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}]}, {"chunks": [{"id": "160423463982362624_93", "content": "\n\n| 型\u0001\u0001\u0001\u0001号 | ZKWTC-25-DB620DZKWTC-25-DB620 |\n| --- | --- |\n| 额定电压 | 220V〜\u0001 |\n| 额定频率 | 50Hz |\n| 额定输⼊功率 | 2 50W1 |\n| 微波输⼊功率 | 1560W |\n| 微波输出功率 | 1000W |\n| 微波额定频率 | 2450MHz |\n| 总容积 | 27L |\n| 有效容积 | 25L |\n| 噪声(声功率级) | 65dB(A) |\n| 净\u0001\u0001\u0001重 | 27kg |\n| 外形尺⼨(mm)宽×⾼×深 | 470×385×485 |\n\n•\u0001\u0001\u0001附件清单\n\n| 名称 | 数量 |\n| --- | --- |\n| 说明书 | 1份 |\n| 玻璃蒸烤盘 | 1个 |\n| 炸烤⽹架 | 1个 |\n| 脆烤盘 | 1个 |\n| 除垢剂 | 1盒 |\n| 防烫⼿套 | 1个 |\n\n•\u0001\u0001\u0001烹饪⽤具选择不同的烹饪⽅式,选择适当的烹饪容器⼗分必要,请参照下表使⽤。![](https://4)![](https://5)![](https://6)\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001注：在使⽤微波模式时，建议使⽤⼴⼝的微波炉专⽤的玻璃容器或者陶瓷容器。例如:禁⽌使⽤缩⼝容器。例如:![](https://2)\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001在台⾯上，按⽰意图平稳放置多功能蒸烤微⼀体机，注意不得倾斜放置，具体位置如图。（单位：mm）![](https://3)•\u0001\u0001\u0001安装要求\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001放置器具的台⾯必须⽔平。台式机器，不能放到橱柜内。\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001请距离电视机、收⾳机、天线等5⽶，否则会⼲扰图像和产⽣噪⾳；\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001不可将器具放置于⾼温潮湿的地⽅。例如煤⽓炉，带电区域或⽔槽旁；\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001切勿在器具的顶部放置任何物品，并保持与顶部壁柜距离⾄少100厘⽶。•\u0001\u0001\u0001电源要求\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001电路必须装有相应的切断及保护装置，连接电源插头和插座应为同⼀型号并符合当地相关规定。\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001电源线接插必须⽅便，确保器", "orderNo": [93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110]}], "fileId": "160423463982362624", "fileName": "一体机-ZKWTC-25-DB620说明书.pdf", "knowledgeBaseId": 137933822469500928, "productNames": ["ZKWTC-25-DB620"], "productType": "一体机", "miniChunks": [{"id": "160423463982362624_100", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 160423463982362624, "content": "\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001注：在使⽤微波模式时，建议使⽤⼴⼝的微波炉专⽤的玻璃容器或者陶瓷容器。例如:禁⽌使", "type": 1, "url": "", "table": "", "orderNo": 100, "createdAt": "2025-03-18T16:26:33", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "160423463982362624_101", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 160423463982362624, "content": "⽤缩⼝容器。例如:", "type": 1, "url": "", "table": "", "orderNo": 101, "createdAt": "2025-03-18T16:26:33", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "160423463982362624_102", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 160423463982362624, "content": "{{I13}}", "type": 2, "url": "https://roki-ai-rag.oss-cn-hangzhou.aliyuncs.com/rag/chunk/2025-03-18/1742286354039-b96b0bef.png", "table": "", "orderNo": 102, "createdAt": "2025-03-18T16:26:33", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "160423463982362624_103", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 160423463982362624, "content": "\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001在台⾯上，按⽰意图平稳放置多功能蒸烤微⼀体机，注意不得倾斜放置，具体位置如图。（单位", "type": 1, "url": "", "table": "", "orderNo": 103, "createdAt": "2025-03-18T16:26:33", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "160423463982362624_104", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 160423463982362624, "content": "：mm）", "type": 1, "url": "", "table": "", "orderNo": 104, "createdAt": "2025-03-18T16:26:33", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "160423463982362624_105", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 160423463982362624, "content": "{{I14}}", "type": 2, "url": "https://roki-ai-rag.oss-cn-hangzhou.aliyuncs.com/rag/chunk/2025-03-18/1742286353882-c10728da.png", "table": "", "orderNo": 105, "createdAt": "2025-03-18T16:26:33", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "160423463982362624_106", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 160423463982362624, "content": "•\u0001\u0001\u0001安装要求\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001放置器具的台⾯必须⽔平。台式机器，不能放到橱柜内。\u0001\u0001\u0001\u0001\u0001\u0001\u0001", "type": 1, "url": "", "table": "", "orderNo": 106, "createdAt": "2025-03-18T16:26:33", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "160423463982362624_107", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 160423463982362624, "content": "\u0001\u0001\u0001请距离电视机、收⾳机、天线等5⽶，否则会⼲扰图像和产⽣噪⾳；\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001不可将器具放置", "type": 1, "url": "", "table": "", "orderNo": 107, "createdAt": "2025-03-18T16:26:33", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "160423463982362624_108", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 160423463982362624, "content": "于⾼温潮湿的地⽅。例如煤⽓炉，带电区域或⽔槽旁；\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001切勿在器具的顶部放置任何物品，并", "type": 1, "url": "", "table": "", "orderNo": 108, "createdAt": "2025-03-18T16:26:33", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "160423463982362624_109", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 160423463982362624, "content": "保持与顶部壁柜距离⾄少100厘⽶。•\u0001\u0001\u0001电源要求\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001电路必须装有相应的切断及保护装", "type": 1, "url": "", "table": "", "orderNo": 109, "createdAt": "2025-03-18T16:26:33", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "160423463982362624_110", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 160423463982362624, "content": "置，连接电源插头和插座应为同⼀型号并符合当地相关规定。\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001电源线接插必须⽅便，确保器", "type": 1, "url": "", "table": "", "orderNo": 110, "createdAt": "2025-03-18T16:26:33", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "160423463982362624_93", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 160423463982362624, "content": "{{T0}}", "type": 3, "url": "https://roki-ai-rag.oss-cn-hangzhou.aliyuncs.com/rag/chunk/2025-03-18/1742286351009-f6ca8326.png", "table": "\n\n| 型\u0001\u0001\u0001\u0001号 | ZKWTC-25-DB620DZKWTC-25-DB620 |\n| --- | --- |\n| 额定电压 | 220V〜\u0001 |\n| 额定频率 | 50Hz |\n| 额定输⼊功率 | 2 50W1 |\n| 微波输⼊功率 | 1560W |\n| 微波输出功率 | 1000W |\n| 微波额定频率 | 2450MHz |\n| 总容积 | 27L |\n| 有效容积 | 25L |\n| 噪声(声功率级) | 65dB(A) |\n| 净\u0001\u0001\u0001重 | 27kg |\n| 外形尺⼨(mm)宽×⾼×深 | 470×385×485 |\n\n", "orderNo": 93, "createdAt": "2025-03-18T16:26:33", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "160423463982362624_94", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 160423463982362624, "content": "•\u0001\u0001\u0001附件清单", "type": 1, "url": "", "table": "", "orderNo": 94, "createdAt": "2025-03-18T16:26:33", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "160423463982362624_95", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 160423463982362624, "content": "{{T1}}", "type": 3, "url": "https://roki-ai-rag.oss-cn-hangzhou.aliyuncs.com/rag/chunk/2025-03-18/1742286348660-663f3e9f.png", "table": "\n\n| 名称 | 数量 |\n| --- | --- |\n| 说明书 | 1份 |\n| 玻璃蒸烤盘 | 1个 |\n| 炸烤⽹架 | 1个 |\n| 脆烤盘 | 1个 |\n| 除垢剂 | 1盒 |\n| 防烫⼿套 | 1个 |\n\n", "orderNo": 95, "createdAt": "2025-03-18T16:26:33", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "160423463982362624_96", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 160423463982362624, "content": "•\u0001\u0001\u0001烹饪⽤具选择不同的烹饪⽅式,选择适当的烹饪容器⼗分必要,请参照下表使⽤。", "type": 1, "url": "", "table": "", "orderNo": 96, "createdAt": "2025-03-18T16:26:33", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "160423463982362624_97", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 160423463982362624, "content": "{{I10}}", "type": 2, "url": "https://roki-ai-rag.oss-cn-hangzhou.aliyuncs.com/rag/chunk/2025-03-18/1742286354008-58397d69.png", "table": "", "orderNo": 97, "createdAt": "2025-03-18T16:26:33", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "160423463982362624_98", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 160423463982362624, "content": "{{I11}}", "type": 2, "url": "https://roki-ai-rag.oss-cn-hangzhou.aliyuncs.com/rag/chunk/2025-03-18/1742286353936-b8f99c42.png", "table": "", "orderNo": 98, "createdAt": "2025-03-18T16:26:33", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "160423463982362624_99", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 160423463982362624, "content": "{{I12}}", "type": 2, "url": "https://roki-ai-rag.oss-cn-hangzhou.aliyuncs.com/rag/chunk/2025-03-18/1742286353965-784e9eb7.png", "table": "", "orderNo": 99, "createdAt": "2025-03-18T16:26:33", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}]}, {"chunks": [{"id": "137945120435499008_65", "content": "| 额定频率 | 50Hz |\n| 额定功率 | 2650W |\n| 微波输入功率 | 1 00W5 |\n| 微波输出功率 | 800W |\n| 微波额定频率 | 2450MHz |\n| 总容积 | 41L |\n| 有效容积 | 36L |\n| 噪声(声功率级) | 65dB(A) |\n| 净   重 | 38.5kg |\n| 外形尺寸(mm)宽×高×深 | 595×455×535 |\n多功能蒸烤微一体机\n| 序号 | 名称 | 数量 |\n| --- | --- | --- |\n| 1 | 说明书 | 1份 |\n| 2 | 烤盘 | 1个 |\n| 3 | 玻璃烤盘 | 1个 |\n| 4 | 炸烤网架 | 1个 |\n| 5 | ST3.5×30自攻螺钉 | 2个 |\n| 6 | 除垢剂 | 2袋 |\n| 7 | 吸水球 | 1个 |\n烹饪用具选择不同的烹饪方式,选择适当的烹饪容器十分必要,请参照下表使用。![](https://7)![](https://8)注：在使用微波模式时，建议使用广口的微波炉专用的玻璃容器或者陶瓷容器。例如:    禁止使用缩口容器。例如:安装说明在橱柜的设定位置上，按下面安装图示设定方孔，将器具平稳嵌入该方孔，注意不得倾斜安置,具体开孔尺寸（mm）见下表:\n| 序号 | 名称 | W | H | D |\n| --- | --- | --- | --- | --- |\n| 1 | 全嵌开孔尺寸（宽×高×深） | 600 | 460 | 565 |\n| 2 | 半嵌开孔尺寸（宽×高×深） | 560 | 450 | 550 |\n", "orderNo": [65, 66, 67, 68, 69, 70, 71, 72, 73, 74]}], "fileId": "137945120435499008", "fileName": "一体机-WZKQS-36-CQ979说明书.pdf", "knowledgeBaseId": 137933822469500928, "productNames": ["WZKQS-36-CQ979"], "productType": "一体机", "miniChunks": [{"id": "137945120435499008_65", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 137945120435499008, "content": "{{T0}}", "type": 3, "url": "https://roki-ai-rag.oss-cn-hangzhou.aliyuncs.com/rag/chunk/2025-01-15/1736931392335-7ccc165b.png", "table": "\n\n| 型    号 | WZKQS-36-CQ979 |\n| --- | --- |\n| 额定电压 | 220V~  |\n| 额定频率 | 50Hz |\n| 额定功率 | 2650W |\n| 微波输入功率 | 1 00W5 |\n| 微波输出功率 | 800W |\n| 微波额定频率 | 2450MHz |\n| 总容积 | 41L |\n| 有效容积 | 36L |\n| 噪声(声功率级) | 65dB(A) |\n| 净   重 | 38.5kg |\n| 外形尺寸(mm)宽×高×深 | 595×455×535 |\n\n", "orderNo": 65, "createdAt": "2025-01-15T16:58:06", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "137945120435499008_66", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 137945120435499008, "content": "多功能蒸烤微一体机", "type": 1, "url": "", "table": "", "orderNo": 66, "createdAt": "2025-01-15T16:58:06", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "137945120435499008_67", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 137945120435499008, "content": "{{T1}}", "type": 3, "url": "https://roki-ai-rag.oss-cn-hangzhou.aliyuncs.com/rag/chunk/2025-01-15/1736931396871-c995f2e4.png", "table": "\n\n| 序号 | 名称 | 数量 |\n| --- | --- | --- |\n| 1 | 说明书 | 1份 |\n| 2 | 烤盘 | 1个 |\n| 3 | 玻璃烤盘 | 1个 |\n| 4 | 炸烤网架 | 1个 |\n| 5 | ST3.5×30自攻螺钉 | 2个 |\n| 6 | 除垢剂 | 2袋 |\n| 7 | 吸水球 | 1个 |\n\n", "orderNo": 67, "createdAt": "2025-01-15T16:58:06", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "137945120435499008_68", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 137945120435499008, "content": "烹饪用具选择不同的烹饪方式,选择适当的烹饪容器十分必要,请参照下表使用。", "type": 1, "url": "", "table": "", "orderNo": 68, "createdAt": "2025-01-15T16:58:06", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "137945120435499008_69", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 137945120435499008, "content": "{{I7}}", "type": 2, "url": "https://roki-ai-rag.oss-cn-hangzhou.aliyuncs.com/rag/chunk/2025-01-15/1736931394587-f80f7b6a.png", "table": "", "orderNo": 69, "createdAt": "2025-01-15T16:58:06", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "137945120435499008_70", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 137945120435499008, "content": "{{I8}}", "type": 2, "url": "https://roki-ai-rag.oss-cn-hangzhou.aliyuncs.com/rag/chunk/2025-01-15/1736931394638-3a495a8f.png", "table": "", "orderNo": 70, "createdAt": "2025-01-15T16:58:06", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "137945120435499008_71", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 137945120435499008, "content": "注：在使用微波模式时，建议使用广口的微波炉专用的玻璃容器或者陶瓷容器。例如:    禁止使用缩口容器", "type": 1, "url": "", "table": "", "orderNo": 71, "createdAt": "2025-01-15T16:58:06", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "137945120435499008_72", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 137945120435499008, "content": "。例如:安装说明在橱柜的设定位置上，按下面安装图示设定方孔，将器具平稳嵌入该方孔，注意不得倾斜安置,", "type": 1, "url": "", "table": "", "orderNo": 72, "createdAt": "2025-01-15T16:58:06", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "137945120435499008_73", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 137945120435499008, "content": "具体开孔尺寸（mm）见下表:", "type": 1, "url": "", "table": "", "orderNo": 73, "createdAt": "2025-01-15T16:58:06", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "137945120435499008_74", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 137945120435499008, "content": "{{T2}}", "type": 3, "url": "https://roki-ai-rag.oss-cn-hangzhou.aliyuncs.com/rag/chunk/2025-01-15/1736931399689-57ea1a41.png", "table": "\n\n| 序号 | 名称 | W | H | D |\n| --- | --- | --- | --- | --- |\n| 1 | 全嵌开孔尺寸（宽×高×深） | 600 | 460 | 565 |\n| 2 | 半嵌开孔尺寸（宽×高×深） | 560 | 450 | 550 |\n\n", "orderNo": 74, "createdAt": "2025-01-15T16:58:06", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}]}, {"chunks": [{"id": "137944780176781312_65", "content": "| 额定频率 | 50Hz |\n| 额定功率 | 2650W |\n| 微波输入功率 | 1 00W5 |\n| 微波输出功率 | 800W |\n| 微波额定频率 | 2450MHz |\n| 总容积 | 41L |\n| 有效容积 | 36L |\n| 噪声(声功率级) | 65dB(A) |\n| 净   重 | 38.5kg |\n| 外形尺寸(mm)宽×高×深 | 595×455×535 |\n多功能蒸烤微一体机\n| 序号 | 名称 | 数量 |\n| --- | --- | --- |\n| 1 | 说明书 | 1份 |\n| 2 | 烤盘 | 1个 |\n| 3 | 玻璃烤盘 | 1个 |\n| 4 | 炸烤网架 | 1个 |\n| 5 | ST3.5×30自攻螺钉 | 2个 |\n| 6 | 除垢剂 | 2袋 |\n| 7 | 吸水球 | 1个 |\n烹饪用具选择不同的烹饪方式,选择适当的烹饪容器十分必要,请参照下表使用。![](https://9)![](https://10)注：在使用微波模式时，建议使用广口的微波炉专用的玻璃容器或者陶瓷容器。例如:    禁止使用缩口容器。例如:安装说明在橱柜的设定位置上，按下面安装图示设定方孔，将器具平稳嵌入该方孔，注意不得倾斜安置,具体开孔尺寸（mm）见下表:\n| 序号 | 名称 | W | H | D |\n| --- | --- | --- | --- | --- |\n| 1 | 全嵌开孔尺寸（宽×高×深） | 600 | 460 | 565 |\n| 2 | 半嵌开孔尺寸（宽×高×深） | 560 | 450 | 550 |\n", "orderNo": [65, 66, 67, 68, 69, 70, 71, 72, 73, 74]}], "fileId": "137944780176781312", "fileName": "一体机-WZKQS-36-CQ931说明书.pdf", "knowledgeBaseId": 137933822469500928, "productNames": ["WZKQS-36-CQ931"], "productType": "一体机", "miniChunks": [{"id": "137944780176781312_65", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 137944780176781312, "content": "{{T0}}", "type": 3, "url": "https://roki-ai-rag.oss-cn-hangzhou.aliyuncs.com/rag/chunk/2025-01-15/1736930916659-a73cfb4c.png", "table": "\n\n| 型    号 | WZKQS-36-CQ931 |\n| --- | --- |\n| 额定电压 | 220V～  |\n| 额定频率 | 50Hz |\n| 额定功率 | 2650W |\n| 微波输入功率 | 1 00W5 |\n| 微波输出功率 | 800W |\n| 微波额定频率 | 2450MHz |\n| 总容积 | 41L |\n| 有效容积 | 36L |\n| 噪声(声功率级) | 65dB(A) |\n| 净   重 | 38.5kg |\n| 外形尺寸(mm)宽×高×深 | 595×455×535 |\n\n", "orderNo": 65, "createdAt": "2025-01-15T16:49:48", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "137944780176781312_66", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 137944780176781312, "content": "多功能蒸烤微一体机", "type": 1, "url": "", "table": "", "orderNo": 66, "createdAt": "2025-01-15T16:49:48", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "137944780176781312_67", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 137944780176781312, "content": "{{T1}}", "type": 3, "url": "https://roki-ai-rag.oss-cn-hangzhou.aliyuncs.com/rag/chunk/2025-01-15/1736930920890-b38b9a36.png", "table": "\n\n| 序号 | 名称 | 数量 |\n| --- | --- | --- |\n| 1 | 说明书 | 1份 |\n| 2 | 烤盘 | 1个 |\n| 3 | 玻璃烤盘 | 1个 |\n| 4 | 炸烤网架 | 1个 |\n| 5 | ST3.5×30自攻螺钉 | 2个 |\n| 6 | 除垢剂 | 2袋 |\n| 7 | 吸水球 | 1个 |\n\n", "orderNo": 67, "createdAt": "2025-01-15T16:49:48", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "137944780176781312_68", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 137944780176781312, "content": "烹饪用具选择不同的烹饪方式,选择适当的烹饪容器十分必要,请参照下表使用。", "type": 1, "url": "", "table": "", "orderNo": 68, "createdAt": "2025-01-15T16:49:48", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "137944780176781312_69", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 137944780176781312, "content": "{{I6}}", "type": 2, "url": "https://roki-ai-rag.oss-cn-hangzhou.aliyuncs.com/rag/chunk/2025-01-15/1736930918625-f80f7b6a.png", "table": "", "orderNo": 69, "createdAt": "2025-01-15T16:49:48", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "137944780176781312_70", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 137944780176781312, "content": "{{I7}}", "type": 2, "url": "https://roki-ai-rag.oss-cn-hangzhou.aliyuncs.com/rag/chunk/2025-01-15/1736930918669-3a495a8f.png", "table": "", "orderNo": 70, "createdAt": "2025-01-15T16:49:48", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "137944780176781312_71", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 137944780176781312, "content": "注：在使用微波模式时，建议使用广口的微波炉专用的玻璃容器或者陶瓷容器。例如:    禁止使用缩口容器", "type": 1, "url": "", "table": "", "orderNo": 71, "createdAt": "2025-01-15T16:49:48", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "137944780176781312_72", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 137944780176781312, "content": "。例如:安装说明在橱柜的设定位置上，按下面安装图示设定方孔，将器具平稳嵌入该方孔，注意不得倾斜安置,", "type": 1, "url": "", "table": "", "orderNo": 72, "createdAt": "2025-01-15T16:49:48", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "137944780176781312_73", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 137944780176781312, "content": "具体开孔尺寸（mm）见下表:", "type": 1, "url": "", "table": "", "orderNo": 73, "createdAt": "2025-01-15T16:49:48", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}, {"id": "137944780176781312_74", "roId": 104705212141129728, "knowledgeBaseId": 137933822469500928, "fileId": 137944780176781312, "content": "{{T2}}", "type": 3, "url": "https://roki-ai-rag.oss-cn-hangzhou.aliyuncs.com/rag/chunk/2025-01-15/1736930924745-57ea1a41.png", "table": "\n\n| 序号 | 名称 | W | H | D |\n| --- | --- | --- | --- | --- |\n| 1 | 全嵌开孔尺寸（宽×高×深） | 600 | 460 | 565 |\n| 2 | 半嵌开孔尺寸（宽×高×深） | 560 | 450 | 550 |\n\n", "orderNo": 74, "createdAt": "2025-01-15T16:49:48", "sectionId": 0, "videoBeginTime": null, "videoEndTime": null}]}], "cookeryQaAnswer": "", "cookbookExamples": [], "otherFileChunks": [], "regionalDietary": "", "cookbooks": [], "foodstuffsFileChunks": []}}, "position": {"x": 1573.812838311064, "y": 1008.7725467573732}}, {"id": "1728373743598", "data": {"desc": "", "type": "if-else", "cases": [{"id": "true", "case_id": "true", "conditions": [{"id": "0e360f21-34bf-46de-a0b1-71fe3e2710df", "value": "", "varType": "string", "variable_selector": ["1728373706400", "cookeryQaAnswer"], "comparison_operator": "not empty"}], "logical_operator": "and"}, {"id": "d3ae07d0-e265-4b69-9989-d5b3f18b8721", "case_id": "d3ae07d0-e265-4b69-9989-d5b3f18b8721", "conditions": [{"id": "689aace4-dd2d-4d28-b339-8eb3ba669784", "value": "other", "varType": "string", "variable_selector": ["1728375675821", "intent"], "comparison_operator": "contains"}], "logical_operator": "and"}, {"case_id": "be2f4ad1-1923-4291-936c-2f7c9496a221", "conditions": [{"id": "66d32597-3153-42de-a084-21ca0edd9d68", "value": "meal_planning", "varType": "string", "variable_selector": ["1728375675821", "intent"], "comparison_operator": "contains"}], "logical_operator": "and"}], "title": "cookeryGodRetrivalCond", "selected": false, "success": true, "cost_time": 0, "result_inputs": {"conditions": [{"comparison_operator": "contains", "actual_value": "other", "expected_value": "other"}]}, "result_outputs": {"result": false, "selected_case_id": "d3ae07d0-e265-4b69-9989-d5b3f18b8721"}}, "position": {"x": 2006.8965238908645, "y": 991.6930824538274}}, {"id": "1728373955721", "data": {"desc": "", "type": "if-else", "cases": [{"id": "r3ae07d0-e265-4b69-9989-r5b3f18b8725", "case_id": "r3ae07d0-e265-4b69-9989-r5b3f18b8725", "conditions": [{"id": "789aace4-dd2d-4d28-b339-8eb3ba66978r", "value": "product_only", "varType": "string", "variable_selector": ["1728375675821", "intent"], "comparison_operator": "contains"}, {"id": "iuwhc254-9842-3iu1-uhw1-9sjqhfgnwq25", "value": "false", "varType": "string", "variable_selector": ["1739857356836", "imageRelated"], "comparison_operator": "contains"}], "logical_operator": "and"}, {"id": "59ba5acf-59db-439b-aadc-0c7505d5a318", "case_id": "59ba5acf-59db-439b-aadc-0c7505d5a318", "conditions": [{"id": "13575bdf-1858-4798-a143-6ec650e6e687", "value": "true", "varType": "string", "variable_selector": ["1739857618889", "containsDetails"], "comparison_operator": "contains"}, {"id": "1c828202-a30b-45ee-ac01-36aad13a2cb1", "value": "cookery", "varType": "string", "variable_selector": ["1728375675821", "intent"], "comparison_operator": "not contains"}, {"id": "b87175c6-ddbd-4df7-8bdf-6297e6e27abe", "value": "product", "varType": "string", "variable_selector": ["1728375675821", "intent"], "comparison_operator": "not contains"}], "logical_operator": "and"}, {"id": "4f74ed7c-ce66-402f-8c30-786aa7d48013", "case_id": "4f74ed7c-ce66-402f-8c30-786aa7d48013", "conditions": [{"id": "3e1b6cbc-67d4-4364-a3e0-73be0e0fea57", "value": "false", "varType": "string", "variable_selector": ["1739857618889", "containsDetails"], "comparison_operator": "contains"}, {"id": "e4a5bb09-5e6f-49a9-9833-a2fec67e15ee", "value": "cookery", "varType": "string", "variable_selector": ["1728375675821", "intent"], "comparison_operator": "not contains"}, {"id": "505f4f5e-c7ad-471b-a6c5-5b6d034d97ba", "value": "product", "varType": "string", "variable_selector": ["1728375675821", "intent"], "comparison_operator": "not contains"}], "logical_operator": "and"}, {"case_id": "432a2f49-fc2e-4001-a1fa-1ec90f3d10af", "conditions": [{"id": "719b9e0e-c10e-428f-ad15-8555c5c4b581", "value": "subscribe_cookery_daily", "varType": "string", "variable_selector": ["1728375675821", "intent"], "comparison_operator": "contains"}], "logical_operator": "and"}, {"case_id": "230775f6-2c72-450b-bd14-edbe0d1ac7e4", "conditions": [{"id": "89f42976-93fc-444f-b3ac-a7cdedfd8516", "value": "customer", "varType": "string", "variable_selector": ["1728375675821", "intent"], "comparison_operator": "contains"}], "logical_operator": "and"}], "title": "cookeryGodIntentCond", "selected": false, "success": true, "cost_time": 0, "result_inputs": {"conditions": [{"comparison_operator": "contains", "actual_value": "other", "expected_value": "customer"}]}, "result_outputs": {"result": false, "selected_case_id": ""}}, "position": {"x": 388.7072580097463, "y": 450.4764162049301}}, {"id": "1728375675821", "data": {"code": "\ndef main(arg1: int, arg2: int) -> dict:\n    return {\n        \"result\": arg1 + arg2,\n    }\n", "desc": "", "type": "method", "title": "cookeryGodRouter", "outputs": {"intent": {"type": "string", "children": null}, "memories": {"type": "string", "children": null}, "foodstuffs": {"type": "object", "children": null}, "otherIntents": {"type": "object", "children": null}, "productNames": {"type": "object", "children": null}, "productTypes": {"type": "object", "children": null}, "wellnessLabel": {"type": "object", "children": null}, "rewrittenQuery": {"type": "string", "children": null}, "otherIntentsValues": {"type": "object", "children": null}, "recommendedCookbooks": {"type": "object", "children": null}, "cookbookIngredientThemes": {"type": "object", "children": null}}, "selected": false, "variables": [{"variable": "query", "value_selector": ["sys", "query"]}, {"variable": "messages", "value_selector": ["sys", "history"]}, {"variable": "agentId", "value_selector": ["sys", "agent_id"]}], "component_name": "cookeryGodRouter", "success": true, "cost_time": 2735, "result_outputs": {"wellnessLabel": [], "rewrittenQuery": "一体机微波功能可以用玻璃容器吗？", "otherIntents": [], "memories": "【摘要】\n基本信息：身高158厘米，体重52千克，目前居住在杭州临平乔司。希望被称呼为“vpo”。\n\n健康与情绪状态：对坚果过敏，需特别注意饮食中避免坚果类食物。可能有油性肤质（到期时间：2025-10-14）。有肠胃炎（到期时间：2025-10-22）。最近体重下降，需要恢复体重（到期时间：2025-10-22）。\n\n生活习惯与偏好：对奶制品有消费偏好，尤其喜欢奶皮子。不喜欢吃香菜，也不喜欢别人直接称呼她的名字。此外，不喜欢吃酸辣口的菜。\n\n", "productNames": [], "recommendedCookbooks": [], "foodstuffs": [], "productTypes": [], "cookbookIngredientThemes": [], "intent": "other", "otherIntentsValues": {}}}, "position": {"x": -492.1494914519154, "y": 886.717089859674}}, {"id": "1728613209040", "data": {"desc": "", "type": "coze_bot", "title": "DefaultLLM", "memory": {"window": {"size": 20, "enabled": true}, "truncation": {"enabled": true, "turn_configs": {"0": {"max_prefix_length": 384, "max_suffix_length": 128}, "1": {"max_prefix_length": 256, "max_suffix_length": 144}, "2": {"max_prefix_length": 192, "max_suffix_length": 108}}, "default_config": {"max_prefix_length": 70, "max_suffix_length": 80}}, "role_prefix": {"user": "", "assistant": ""}, "query_prompt_template": ""}, "stream": true, "vision": {"enabled": false}, "bot_info": {"bot_id": "7456692805344509986", "bot_name": "食神"}, "selected": false, "variables": [], "prompt_template": [{"id": "85f348d6-46e8-4ed0-8e58-3bfd6aadc873", "role": "user", "text": "{{#1741239358087.output#}}"}], "custom_variables": {"user_info": {"当前用户信息": {"is_variable": true, "value_selector": ["1728373697410", "user_info"]}, "对该用户的重要记忆点": {"is_variable": true, "value_selector": ["1728375675821", "memories"]}}, "current_date": {"is_variable": true, "value_selector": ["sys", "current_date_info"]}, "weather_info": {"is_variable": true, "value_selector": ["1728373697410", "weather"]}, "rewritten_query": {"is_variable": true, "value_selector": ["1741239358087", "output"]}, "knowledge_context": {"常识": {"is_variable": true, "value_selector": ["1728373706400", "qaGroups"]}, "产品知识": {"is_variable": true, "value_selector": ["1728373706400", "productFileChunks"]}, "文档知识": {"is_variable": true, "value_selector": ["1728373706400", "otherFileChunks"]}, "重要信息": [{"is_variable": true, "value_selector": ["1728373706400", "regionalDietary"]}, {"is_variable": true, "value_selector": ["1728373706400", "cookbookExamples"]}, {"is_variable": true, "value_selector": ["1728373706400", "obesityNutritionFileChunks"]}]}}, "auto_save_history": false, "success": true, "cost_time": 157, "result_inputs": {"#sys.recent_user_messages#": "[]", "#1741239358087.output#": "一体机微波功能可以用玻璃容器吗？"}, "result_outputs": {"text": "io.reactivex.internal.operators.flowable.FlowableCreate@d0d7bee"}}, "position": {"x": 3396.8520533275496, "y": 1118.6957472043218}}, {"id": "1728613254355", "data": {"desc": "", "type": "answer", "title": "DefaultLLMAnswer", "answer": "{{#1728613209040.text#}}", "selected": false, "variables": [], "intermediate": true, "success": true, "cost_time": 9361, "result_outputs": {"answer": "根据产品说明书中的信息，一体机的微波功能**可以使用玻璃容器**，但需要满足以下条件：\n\n1. **容器类型**：\n   - 必须使用**广口的微波炉专用玻璃容器**或**陶瓷容器**。\n   - **禁止使用缩口容器**，因为缩口容器可能导致热量分布不均或蒸汽无法释放，存在安全隐患。\n\n2. **注意事项**：\n   - 确保容器是微波炉专用的，普通玻璃容器可能无法承受高温或微波辐射。\n   - 避免使用金属、搪瓷、锡纸等不适合微波加热的材质。\n\n如果需要进一步确认，可以参考您的一体机型号的说明书，或者联系官方客服（95105855）咨询。", "extraData": {}}}, "position": {"x": 3710.361061099707, "y": 1124.0010850120002}}, {"id": "1729149220338", "data": {"desc": "", "type": "if-else", "cases": [{"id": "true", "case_id": "true", "conditions": [{"id": "3299588e-3dd8-439b-867d-25ddc8fc4f96", "value": "", "varType": "string", "variable_selector": ["1728373706400", "cookbooks"], "comparison_operator": "not empty"}], "logical_operator": "and"}], "title": "cookbookRecommendCond", "selected": false, "success": true, "cost_time": 0, "result_inputs": {"conditions": [{"comparison_operator": "not empty", "actual_value": [], "expected_value": ""}]}, "result_outputs": {"result": false, "selected_case_id": ""}}, "position": {"x": 2071.4592440178812, "y": 2108.3858773565617}}, {"id": "1739857356836", "data": {"code": "\ndef main(arg1: str, arg2: str) -> dict:\n    return {\n        \"image_related\": \"是\",\n    }\n", "desc": "", "type": "method", "title": "ImageRelatedJudge", "outputs": {"imageUrl": {"type": "object", "children": null}, "imageRelated": {"type": "string", "children": null}}, "selected": false, "variables": [{"variable": "query", "value_selector": ["sys", "query"]}], "component_name": "imageRelatedJudge", "success": true, "cost_time": 6, "result_outputs": {"imageUrl": [], "imageRelated": "false"}}, "position": {"x": -2320.108293684048, "y": 1036.034031308507}}, {"id": "1739857493593", "data": {"desc": "", "type": "if-else", "cases": [{"id": "true", "case_id": "true", "conditions": [{"id": "baabf4d6-9787-40b1-beed-d3d1c1718b63", "value": "true", "varType": "string", "variable_selector": ["1739857356836", "imageRelated"], "comparison_operator": "contains"}], "logical_operator": "and"}], "title": "ImageRelatedCond", "selected": false, "success": true, "cost_time": 0, "result_inputs": {"conditions": [{"comparison_operator": "contains", "actual_value": "false", "expected_value": "true"}]}, "result_outputs": {"result": false, "selected_case_id": ""}}, "position": {"x": -960.7519454794596, "y": 809.3673330981583}}, {"id": "1740713645824", "data": {"desc": "", "type": "if-else", "cases": [{"id": "true", "case_id": "true", "conditions": [{"id": "a7799df1-a594-41cb-bec1-3e26e7f78216", "value": "deepseek-r1", "varType": "string", "variable_selector": ["user", "model_group"], "comparison_operator": "contains"}], "logical_operator": "and"}, {"case_id": "9570ea90-3fb0-4c22-9f42-b1eef5110341", "conditions": [{"id": "f9f18512-5c55-4bd5-9606-e20b32695e28", "value": "deepseek-v3", "varType": "string", "variable_selector": ["user", "model_group"], "comparison_operator": "contains"}], "logical_operator": "and"}], "title": "ModelGroupOtherIntentCond", "selected": false, "success": true, "cost_time": 0, "result_inputs": {"conditions": [{"comparison_operator": "contains", "actual_value": "", "expected_value": "deepseek-v3"}]}, "result_outputs": {"result": false, "selected_case_id": ""}}, "position": {"x": 2701.0017799844595, "y": 955.3173116161458}}, {"id": "1740721114490", "data": {"desc": "", "type": "if-else", "cases": [{"id": "true", "case_id": "true", "conditions": [{"id": "26aed4c4-6336-4f7f-ae49-1c8c8956615b", "value": "true", "varType": "string", "variable_selector": ["user", "face"], "comparison_operator": "contains"}], "logical_operator": "and"}, {"case_id": "user-input-tongue-true", "conditions": [{"id": "0a931ff4-f8a3-4dba-9e96-29dd00ce04d1", "value": "true", "varType": "string", "variable_selector": ["user", "tongue"], "comparison_operator": "contains"}], "logical_operator": "and"}, {"case_id": "b2b23810-1dc3-467e-92e2-59e058fa1cf6", "conditions": [{"id": "ac8ffba4-4ec7-4f4c-b4c2-606d2954cdb8", "value": "取消订阅", "varType": "string", "variable_selector": ["sys", "query"], "comparison_operator": "contains"}], "logical_operator": "and"}, {"case_id": "0b43c2f2-7f5e-4c88-9514-99737046c332", "conditions": [{"id": "877d72be-b0b7-4e65-80f3-69da745f3de8", "value": "食神日报", "varType": "string", "variable_selector": ["sys", "query"], "comparison_operator": "contains"}, {"id": "9bd1c046-a3cf-4292-bed6-055790a89967", "value": "订阅日报", "varType": "string", "variable_selector": ["sys", "query"], "comparison_operator": "contains"}], "logical_operator": "or"}, {"case_id": "cf9aac8d-c9df-4153-9860-33841dc1c125", "conditions": [{"id": "be68d73c-7cf5-4295-aafd-2fe4590d2d30", "value": "true", "varType": "string", "variable_selector": ["user", "calorie"], "comparison_operator": "contains"}], "logical_operator": "and"}], "title": "Plugin<PERSON><PERSON><PERSON>", "selected": false, "success": true, "cost_time": 2, "result_inputs": {"conditions": [{"comparison_operator": "contains", "actual_value": "", "expected_value": "true"}]}, "result_outputs": {"result": false, "selected_case_id": ""}}, "position": {"x": -2927.0711635017733, "y": 301.7900608373586}}, {"id": "1741239358087", "data": {"desc": "", "type": "variable-aggregator", "title": "routerAgg", "selected": false, "variables": [{"priority": 1, "selector": ["1741239235422", "rewritten<PERSON><PERSON><PERSON>"]}, {"priority": 2, "selector": ["1728375675821", "rewritten<PERSON><PERSON><PERSON>"]}], "output_type": "string", "advanced_settings": {"groups": [{"groupId": "2d1390d1-c191-4295-9296-d996dc06559a", "variables": [], "group_name": "Group1", "output_type": "any"}], "group_enabled": false}, "success": true, "cost_time": 1, "result_inputs": {"rewrittenQuery": "一体机微波功能可以用玻璃容器吗？"}, "result_outputs": {"output": "一体机微波功能可以用玻璃容器吗？"}}, "position": {"x": -34.42827029860971, "y": 723.3667125172843}}, {"id": "1741945361603", "data": {"desc": "", "type": "variable-aggregator", "title": "OtherAnswerAgg", "selected": false, "variables": [{"priority": 1, "selector": ["1740713714150", "text"]}, {"priority": 1, "selector": ["1741154528802", "text"]}, {"priority": 1, "selector": ["1728613209040", "text"]}], "output_type": "string", "success": true, "cost_time": 0, "result_inputs": {"text": "根据产品说明书中的信息，一体机的微波功能**可以使用玻璃容器**，但需要满足以下条件：\n\n1. **容器类型**：\n   - 必须使用**广口的微波炉专用玻璃容器**或**陶瓷容器**。\n   - **禁止使用缩口容器**，因为缩口容器可能导致热量分布不均或蒸汽无法释放，存在安全隐患。\n\n2. **注意事项**：\n   - 确保容器是微波炉专用的，普通玻璃容器可能无法承受高温或微波辐射。\n   - 避免使用金属、搪瓷、锡纸等不适合微波加热的材质。\n\n如果需要进一步确认，可以参考您的一体机型号的说明书，或者联系官方客服（95105855）咨询。"}, "result_outputs": {"output": "根据产品说明书中的信息，一体机的微波功能**可以使用玻璃容器**，但需要满足以下条件：\n\n1. **容器类型**：\n   - 必须使用**广口的微波炉专用玻璃容器**或**陶瓷容器**。\n   - **禁止使用缩口容器**，因为缩口容器可能导致热量分布不均或蒸汽无法释放，存在安全隐患。\n\n2. **注意事项**：\n   - 确保容器是微波炉专用的，普通玻璃容器可能无法承受高温或微波辐射。\n   - 避免使用金属、搪瓷、锡纸等不适合微波加热的材质。\n\n如果需要进一步确认，可以参考您的一体机型号的说明书，或者联系官方客服（95105855）咨询。"}}, "position": {"x": 4381.872445566987, "y": 741.5520040596834}}, {"id": "1741945607974", "data": {"code": "\ndef main(arg1: str, arg2: str) -> dict:\n    return {\n        \"result\": arg1 + arg2,\n    }\n", "desc": "", "type": "method", "title": "OtherPostProcess", "outputs": {"citationFiles": {"type": "object", "children": null}, "postProcessedCookbooks": {"type": "string", "children": null}}, "selected": false, "variables": [{"variable": "cookeryAnswer", "value_selector": ["1728373832821", "answer"]}, {"variable": "cookbooks", "value_selector": ["1728373706400", "cookbooks"]}, {"variable": "cookbookCards", "value_selector": ["1728373706400", "cookbookCards"]}, {"variable": "recommendedCookbooks", "value_selector": ["1729149258024", "recommendedCookbooks"]}, {"variable": "productFileChunks", "value_selector": ["1728373706400", "productFileChunks"]}, {"variable": "cookeryFileChunks", "value_selector": ["1728373706400", "cookeryFileChunks"]}, {"variable": "obesityNutritionFileChunks", "value_selector": ["1728373706400", "obesityNutritionFileChunks"]}, {"variable": "otherFileChunks", "value_selector": ["1728373706400", "otherFileChunks"]}, {"variable": "foodstuffsFileChunks", "value_selector": ["1728373706400", "foodstuffsFileChunks"]}, {"variable": "productNames", "value_selector": ["1728375675821", "productNames"]}, {"variable": "productTypes", "value_selector": ["1728375675821", "productTypes"]}, {"variable": "cookbookIngredientThemes", "value_selector": ["1728375675821", "cookbookIngredientThemes"]}, {"variable": "rewritten<PERSON><PERSON><PERSON>", "value_selector": ["1741239358087", "output"]}], "component_name": "cookeryPostProcess", "success": true, "cost_time": 264, "result_outputs": {"postProcessedCookbooks": [], "citationFiles": []}}, "position": {"x": 4685.872445566987, "y": 741.5520040596834}}, {"id": "1741945612944", "data": {"desc": "", "type": "answer", "title": "OtherPostProcessAnswer", "answer": "", "selected": false, "extra_data": {"data": {"files": "{{#1741945607974.citationFiles#}}", "cookbookCards": "{{#1741945607974.postProcessedCookbooks#}}", "rewrittenQuery": "{{#1741239358087.output#}}"}, "type": "postprocess"}, "prefill_answer": "{{#1741945361603.output#}}", "success": true, "cost_time": 4523, "result_outputs": {"answer": "根据产品说明书中的信息，一体机的微波功能**可以使用玻璃容器**，但需要满足以下条件：\n\n1. **容器类型**：\n   - 必须使用**广口的微波炉专用玻璃容器**或**陶瓷容器**。\n   - **禁止使用缩口容器**，因为缩口容器可能导致热量分布不均或蒸汽无法释放，存在安全隐患。\n\n2. **注意事项**：\n   - 确保容器是微波炉专用的，普通玻璃容器可能无法承受高温或微波辐射。\n   - 避免使用金属、搪瓷、锡纸等不适合微波加热的材质。\n\n如果需要进一步确认，可以参考您的一体机型号的说明书，或者联系官方客服（95105855）咨询。", "extraData": {"type": "postprocess", "data": {"files": "[]", "cookbookCards": "[]", "rewrittenQuery": "一体机微波功能可以用玻璃容器吗？"}}}}, "position": {"x": 4989.872445566987, "y": 741.5520040596834}}, {"id": "291531944468", "data": {"desc": "", "type": "answer", "title": "ImageAnalysisDefaultAnswer", "answer": "", "priority": 999, "variables": [], "intermediate": false, "success": true, "cost_time": 12, "result_outputs": {"answer": "", "extraData": {}}}, "position": {"x": -1637.472784420151, "y": 880.5084943078656}}, {"id": "893509337932", "data": {"desc": "", "type": "if-else", "cases": [{"id": "true", "case_id": "true", "conditions": [{"id": "f963c6e8-3a6c-4395-8bc3-16130c22b5a6", "value": "", "varType": "string", "variable_selector": ["user", "upload_files"], "comparison_operator": "not empty"}], "logical_operator": "and"}], "title": "ImageAnalysisCond", "success": true, "cost_time": 0, "result_inputs": {"conditions": [{"comparison_operator": "not empty", "actual_value": [], "expected_value": ""}]}, "result_outputs": {"result": false, "selected_case_id": ""}}, "position": {"x": -1950.9267935870607, "y": 689.9713168978063}}], "edges": [{"id": "1722935422791-source-1728373955721-target", "source": "1722935422791", "target": "1728373955721", "type": "custom", "sourceHandle": "source", "targetHandle": "target"}, {"id": "1728373697410-source-1740721114490-target", "source": "1728373697410", "target": "1740721114490", "type": "custom", "sourceHandle": "source", "targetHandle": "target"}, {"id": "1728373706400-source-1728373743598-target", "source": "1728373706400", "target": "1728373743598", "type": "custom", "sourceHandle": "source", "targetHandle": "target"}, {"id": "1728373706400-source-1729149220338-target", "source": "1728373706400", "target": "1729149220338", "type": "custom", "sourceHandle": "source", "targetHandle": "target"}, {"id": "1728373743598-d3ae07d0-e265-4b69-9989-d5b3f18b8721-1740713645824-target", "source": "1728373743598", "target": "1740713645824", "type": "custom", "sourceHandle": "d3ae07d0-e265-4b69-9989-d5b3f18b8721", "targetHandle": "target"}, {"id": "1728373955721-false-1728373706400-target", "source": "1728373955721", "target": "1728373706400", "type": "custom", "sourceHandle": "false", "targetHandle": "target"}, {"id": "1728375675821-source-1741239358087-target", "source": "1728375675821", "target": "1741239358087", "type": "custom", "sourceHandle": "source", "targetHandle": "target"}, {"id": "1728613209040-source-1728613254355-target", "source": "1728613209040", "target": "1728613254355", "type": "custom", "sourceHandle": "source", "targetHandle": "target"}, {"id": "1728613254355-source-1741945361603-target", "source": "1728613254355", "target": "1741945361603", "type": "custom", "sourceHandle": "source", "targetHandle": "target"}, {"id": "1739857356836-source-1739857493593-target", "source": "1739857356836", "target": "1739857493593", "type": "custom", "sourceHandle": "source", "targetHandle": "target"}, {"id": "1739857356836-source-893509337932-target", "source": "1739857356836", "target": "893509337932", "type": "custom", "sourceHandle": "source", "targetHandle": "target"}, {"id": "1739857493593-false-1722935422791-target", "source": "1739857493593", "target": "1722935422791", "type": "custom", "sourceHandle": "false", "targetHandle": "target"}, {"id": "1739857493593-false-1728375675821-target", "source": "1739857493593", "target": "1728375675821", "type": "custom", "sourceHandle": "false", "targetHandle": "target"}, {"id": "1739857493593-true-1728375675821-target", "source": "1739857493593", "target": "1728375675821", "type": "custom", "sourceHandle": "true", "targetHandle": "target"}, {"id": "1740713645824-false-1728613209040-target", "source": "1740713645824", "target": "1728613209040", "type": "custom", "sourceHandle": "false", "targetHandle": "target"}, {"id": "1740721114490-false-1739857356836-target", "source": "1740721114490", "target": "1739857356836", "type": "custom", "sourceHandle": "false", "targetHandle": "target"}, {"id": "1741239358087-source-1728373955721-target", "source": "1741239358087", "target": "1728373955721", "type": "custom", "sourceHandle": "source", "targetHandle": "target"}, {"id": "1741945361603-source-1741945607974-target", "source": "1741945361603", "target": "1741945607974", "type": "custom", "sourceHandle": "source", "targetHandle": "target"}, {"id": "1741945607974-source-1741945612944-target", "source": "1741945607974", "target": "1741945612944", "type": "custom", "sourceHandle": "source", "targetHandle": "target"}, {"id": "893509337932-false-291531944468-target", "source": "893509337932", "target": "291531944468", "type": "custom", "sourceHandle": "false", "targetHandle": "target"}]}