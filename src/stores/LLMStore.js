import { defineStore } from 'pinia'

export const useLLMStore = defineStore('llm', {
  state: () => ({
    llmNodes: new Map(), // 使用 Map 存储 LLM 节点数据，key 为节点 id
    currentLlmNode: null // 当前选中的 LLM 节点
  }),
  
  actions: {
    // 初始化/更新 LLM 节点数据
    initLlmNodes(nodes) {
      this.llmNodes.clear()
      nodes.forEach(node => {
        if (node.data?.type === 'llm') {
          this.llmNodes.set(node.id, {
            id: node.id,
            data: node.data,
            position: node.position
          })
        }
      })
    },
    
    // 添加单个 LLM 节点
    addLlmNode(node) {
      if (node.data?.type === 'llm') {
        this.llmNodes.set(node.id, {
          id: node.id,
          data: node.data,
          position: node.position
        })
      }
    },
    
    // 更新单个 LLM 节点
    updateLlmNode(nodeId, nodeData) {
      if (this.llmNodes.has(nodeId)) {
        this.llmNodes.set(nodeId, {
          ...this.llmNodes.get(nodeId),
          data: nodeData
        })
      }
    },
    
    // 删除单个 LLM 节点
    removeLlmNode(nodeId) {
      this.llmNodes.delete(nodeId)
    },
    
    // 设置当前选中的 LLM 节点
    setCurrentLlmNode(nodeId) {
      this.currentLlmNode = this.llmNodes.get(nodeId) || null
    },
    
    // 清空所有数据
    clearAll() {
      this.llmNodes.clear()
      this.currentLlmNode = null
    }
  },
  
  getters: {
    // 获取所有 LLM 节点列表
    llmNodesList: (state) => Array.from(state.llmNodes.values()),
    
    // 获取 LLM 节点数量
    llmNodesCount: (state) => state.llmNodes.size,
    
    // 根据 ID 获取特定 LLM 节点
    getLlmNodeById: (state) => (id) => state.llmNodes.get(id),
    
    // 获取当前选中的 LLM 节点
    getCurrentLlmNode: (state) => state.currentLlmNode
  }
}) 