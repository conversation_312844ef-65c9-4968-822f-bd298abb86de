import request from '@/utils/request'

/**
 * 获取应用列表
 * @returns {Promise} 返回应用列表数据
 */
export function getAppList() {
  return request.get('/llm/consumer/app')
    .then(response => {
      console.log('API 原始响应:', response)
      return response
    })
    .catch(error => {
      console.error('API 请求错误:', error)
      throw error
    })
}

/**
 * 获取Agent配置
 * @param {string} clientKey - Agent的客户端密钥
 * @returns {Promise} 返回Agent配置数据
 */
export function getAgentConfig(clientKey) {
  return request.get(`/llm/consumer/app/${clientKey}/config`)
    .then(response => {
      console.log('获取Agent配置响应:', response)
      return response
    })
    .catch(error => {
      console.error('获取Agent配置错误:', error)
      throw error
    })
}

/**
 * 获取工作流配置
 * @param {string} clientKey - Agent的客户端密钥
 * @returns {Promise} 返回工作流配置数据
 */
export function getWorkflowConfig(clientKey) {
  return request.get(`/llm/workflow/config/feature?clientKey=${clientKey}`)
    .then(response => {
      console.log('获取工作流配置响应:', response)
      return response
    })
    .catch(error => {
      console.error('获取工作流配置错误:', error)
      throw error
    })
}

/**
 * 获取工作流完整配置
 * @param {string} clientKey - Agent的客户端密钥
 * @returns {Promise} 返回工作流完整配置数据
 */
export function getWorkflowFullConfig(clientKey) {
  return request.get(`/llm/workflow/config?clientKey=${clientKey}`)
    .then(response => {
      console.log('获取工作流完整配置响应:', response)
      return response
    })
    .catch(error => {
      console.error('获取工作流完整配置错误:', error)
      throw error
    })
}

/**
 * 更新Agent配置
 * @param {string} clientKey - Agent的客户端密钥
 * @param {Object} configData - 要更新的配置数据
 * @returns {Promise} 返回更新结果
 */
export function updateAgentConfig(clientKey, configData) {
  return request.put(`/llm/consumer/app/${clientKey}/config`, configData)
    .then(response => {
      console.log('更新Agent配置响应:', response)
      return response
    })
    .catch(error => {
      console.error('更新Agent配置错误:', error)
      throw error
    })
}

/**
 * 获取特定配置表的配置
 * @param {string} tableName - 配置表名称 
 * @param {string} configKey - 配置键
 * @returns {Promise} 返回配置数据
 */
export function getTableConfig(tableName, configKey) {
  return request.get(`/llm/config/${tableName}/${configKey}`)
    .then(response => {
      console.log(`获取${tableName}配置响应:`, response)
      return response
    })
    .catch(error => {
      console.error(`获取${tableName}配置错误:`, error)
      throw error
    })
}

/**
 * 更新特定配置表的配置
 * @param {string} tableName - 配置表名称
 * @param {string} configKey - 配置键
 * @param {Object} value - 要更新的配置值
 * @returns {Promise} 返回更新结果
 */
export function updateTableConfig(tableName, configKey, value, comment = '') {
  const data = {
    value,
    comment
  };
  
  return request.put(`/llm/config/${tableName}/${configKey}`, data)
    .then(response => {
      console.log(`更新${tableName}配置响应:`, response)
      return response
    })
    .catch(error => {
      console.error(`更新${tableName}配置错误:`, error)
      throw error
    })
}


