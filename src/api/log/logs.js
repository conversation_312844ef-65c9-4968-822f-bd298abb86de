import axios from 'axios'
import { getAuthTokenFromStorage, saveAuthToken } from '@/utils/storage'

/**
 * 获取API鉴权Token
 * @param {Object} credentials - 登录凭证
 * @param {string} credentials.username - 用户名
 * @param {string} credentials.password - 密码
 * @param {boolean} forceRefresh - 是否强制刷新token（忽略本地缓存）
 * @returns {Promise} - 返回包含token的Promise
 */
export function getAuthToken(credentials = { username: '240703', password: '0000' }, forceRefresh = false) {
  // 如果强制刷新，则跳过本地存储
  if (!forceRefresh) {
    // 先尝试从本地存储获取token
    const cachedToken = getAuthTokenFromStorage()
    if (cachedToken) {
      console.log('从本地存储获取token成功')
      return Promise.resolve(cachedToken)
    }
  } else {
    console.log('强制刷新token，忽略本地缓存')
  }

  // 本地没有有效token，从API获取
  return axios.post(
    '/abp/manager/api/token/',
    credentials,
    {
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      }
    }
  ).then(response => {
    console.log('Token API 响应:', response.data);
    let token = null
    
    if (response.data && response.data.data && response.data.data.access) {
      token = response.data.data.access;
    } else if (response.data && response.data.access) {
      token = response.data.access;
    } else {
      console.error('Token 响应格式异常:', response.data);
      throw new Error('获取 Token 失败: 响应格式异常');
    }
    
    // 将获取到的token保存到本地存储
    if (token) {
      saveAuthToken(token)
    }
    
    return token
  })
}

/**
 * 日志搜索
 * @param {Object} params
 * @param {string} params.index - ES 索引名
 * @param {Object} params.body - ES 查询 body
 * @param {string} [params.env='dev'] - 环境
 * @param {string} params.token - 鉴权 token
 */
export function searchLogs({ index, body, env = 'dev', token }) {
  const makeRequest = (authToken) => {
    return axios.post(
      `/abp/manager/api/logs/es?env=${env}`,
      {
        index,
        body
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        }
      }
    )
  }
  
  // 使用传入的token进行请求
  return makeRequest(token).catch(error => {
    // 检查是否是token失效错误
    const isTokenError = (
      // 检查HTTP状态码
      (error.response && (error.response.status === 401 || error.response.status === 403)) ||
      // 检查特定的错误响应格式
      (error.response && error.response.data && 
       ((error.response.data.code === 401 && error.response.data.data && error.response.data.data.code === 'token_not_valid') ||
        (error.response.data.detail && error.response.data.detail.includes('token not valid'))))
    )
    
    if (isTokenError) {
      console.log('Token已失效，正在重新获取...')
      console.log('错误详情:', error.response?.data)
      
      // 清除本地存储的token
      const { clearAuthToken } = require('@/utils/storage')
      clearAuthToken()
      
      // 重新获取token
      return getAuthToken().then(newToken => {
        console.log('已获取新token，重新发送请求')
        // 使用新token重新发送请求
        return makeRequest(newToken).then(response => {
          // 将新token添加到响应中，以便调用方可以获取
          response.newToken = newToken
          return response
        })
      })
    }
    
    // 其他错误直接抛出
    throw error
  })
} 