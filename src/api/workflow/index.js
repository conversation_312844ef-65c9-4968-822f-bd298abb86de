import request from '@/utils/request'


/**
 * 暂存工作流数据
 * @param {Object} data - 工作流数据
 * @param {string} data.clientKey - 应用ID
 * @param {string} data.description - 工作流描述
 * @param {string} data.updateBy - 修改人
 * @param {string} data.graphString - 工作流图数据（JSON 字符串）
 * @returns {Promise} 返回更新结果
 */
export function cacheUpdateWorkflow(data) {
  return request.post('/llm/workflow/cache/update', data)
}

/**
 * 更新工作流数据
 * @param {Object} data - 工作流数据
 * @param {string} data.clientKey - 应用ID
 * @param {string} data.description - 工作流描述
 * @param {string} data.updateBy - 修改人
 * @param {string} data.graphString - 工作流图数据（JSON 字符串）
 * @returns {Promise} 返回更新结果
 */
export function updateWorkflow(data) {
  return request.post('/llm/workflow/update', data)
}

/**
 * 创建新的工作流
 * @param {Object} data - 工作流数据
 * @param {string} data.clientKey - 应用ID
 * @param {string} data.description - 工作流描述
 * @param {string} data.graphString - 工作流图数据（JSON 字符串）
 * @returns {Promise} 返回创建结果
 */
export function createWorkflow(data) {
  return request.post('/llm/workflow/create', data)
}

/**
 * 保存工作流配置特性和环境变量
 * @param {string} clientKey - 应用ID
 * @param {Object} data - 配置数据
 * @param {Object} data.features - 特性配置对象
 * @param {Object} data.environmentVariables - 环境变量配置对象
 * @returns {Promise} 返回保存结果
 */
export function saveWorkflowConfig(clientKey, data) {
  return request.post(`/llm/workflow/config/feature?clientKey=${clientKey}`, data)
}

/**
 * 保存单个工作流功能特性
 * @param {string} clientKey - 应用ID
 * @param {string} key - 特性键名
 * @param {Object} value - 特性配置值
 * @returns {Promise} 返回保存结果
 */
export function saveWorkflowFeature(clientKey, key, value) {
  const data = {
    features: {
      [key]: value
    },
    environmentVariables: {}
  }
  return saveWorkflowConfig(clientKey, data)
}

/**
 * 保存工作流环境变量
 * @param {string} clientKey - 应用ID
 * @param {Object} envVariables - 环境变量配置对象
 * @returns {Promise} 返回保存结果
 */
export function saveWorkflowEnvVariables(clientKey, envVariables) {
  const data = {
    features: {},
    environmentVariables: envVariables
  }
  return saveWorkflowConfig(clientKey, data)
}

/**
 * 更新工作流完整配置
 * @param {string} clientKey - 应用ID
 * @param {Object} data - 完整配置数据
 * @returns {Promise} 返回更新结果
 */
export function updateWorkflowFullConfig(clientKey, data) {
  return request.post(`/llm/workflow/config/update?clientKey=${clientKey}`, data)
}

/**
 * 删除工作流
 * @param {string} clientKey - 应用ID
 * @returns {Promise} 返回删除结果
 */
export function deleteWorkflow(clientKey) {
  return request.delete('/llm/workflow/delete', {
    clientKey
  })
}

/**
 * 获取工作流缓存数据
 * @param {string} clientKey - 应用ID
 * @param {number} pageSize - 每页条数
 * @param {number} pageNum - 页码
 * @returns {Promise} 返回工作流缓存列表
 */
export function getWorkflowCache(clientKey, pageSize = 10, pageNum = 1) {
  return request.get('/llm/workflow/cache', {
    clientKey,
    pageSize,
    pageNum
  })
}

/**
 * 获取工作流缓存详情数据
 * @param {string} clientKey - 应用ID
 * @param {string} id - 缓存ID
 * @returns {Promise} 返回工作流缓存详情
 */
export function getWorkflowCacheDetail(clientKey, id) {
  return request.get('/llm/workflow/cache/detail', {
    clientKey,
    id
  })
}

/**
 * 获取工作流图数据
 * @param {string} clientKey - 应用ID
 * @returns {Promise} 返回工作流图数据
 */
export function getWorkflowGraph(clientKey) {
  return request.get('/llm/workflow/get/graph', {
    clientKey
  })
}

/**
 * 回滚工作流到指定版本
 * @param {Object} data - 回滚参数
 * @param {string} data.clientKey - 应用ID
 * @param {string} data.id - 缓存ID
 * @returns {Promise} 返回回滚结果
 */
export function rollbackWorkflow(data) {
  return request.post('/llm/workflow/rollback', data)
}

/**
 * 获取 LLM 模型列表
 * @returns {Promise} 返回模型列表数据
 */
export function getLlmModels() {
  return request.get('/llm/workflow/llm')
}

/**
 * LLM 聊天接口（非SSE）
 * @param {Object} data - 聊天参数
 * @param {string} data.provider - 模型提供商
 * @param {string} data.modelName - 模型名称
 * @param {Array} data.messages - 消息列表
 * @param {number} data.temperature - 温度参数
 * @returns {Promise} 返回聊天结果
 */
export function chatWithLLMSync(data) {
  return request.post('/llm/workflow/llm/chat', {
    provider: data.provider || '',
    modelName: data.modelName || '',
    temperature: data.temperature || 1.0,
    messages: data.messages || [],
    stream: false
  })
}

/**
 * LLM 聊天接口（SSE）
 * @param {Object} data - 聊天参数
 * @param {string} data.provider - 模型提供商
 * @param {string} data.modelName - 模型名称
 * @param {Array} data.messages - 消息列表
 * @param {number} data.temperature - 温度参数
 * @param {function} onMessage - 消息回调函数
 * @param {function} onError - 错误回调函数
 * @param {function} onComplete - 完成回调函数
 * @returns {Promise} 返回一个 Promise，resolve 时返回清理函数
 */
export async function chatWithLLM(data, { onMessage, onError, onComplete } = {}) {
  try {
    const baseURL = import.meta.env.VITE_NODE_ENV === 'production'
      ? `${import.meta.env.VITE_BASE_URL}`
      : 'http://cookery-dev.myroki.com';

    const requestUrl = `${baseURL}/api/llm/workflow/llm/chat`
    const requestBody = {
      provider: data.provider || '',
      modelName: data.modelName || '',
      temperature: data.temperature || 1.0,
      messages: data.messages || [],
      stream: true
    }

    console.log('发送 LLM 请求:', {
      url: requestUrl,
      body: requestBody
    })

    // 获取认证token
    const token = localStorage.getItem('token')
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'text/event-stream',
    }

    if (token) {
      headers['Authorization'] = `Bearer ${token}`
    }

    const response = await fetch(requestUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify(requestBody)
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('LLM API 错误:', {
        status: response.status,
        statusText: response.statusText,
        errorText
      })
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`)
    }

    if (!response.body) {
      throw new Error('ReadableStream not supported')
    }

    const reader = response.body.getReader()
    const decoder = new TextDecoder()
    let buffer = ''
    let isThinking = false // 标记是否在思考模式

    const cleanup = () => {
      reader.cancel()
    }

    const readStream = async () => {
      try {
        while (true) {
          const { done, value } = await reader.read()
          
          if (done) {
            if (onComplete) {
              onComplete()
            }
            break
          }

          const chunk = decoder.decode(value, { stream: true })
          buffer += chunk

          const lines = buffer.split('\n')
          buffer = lines.pop() || ''

          for (const line of lines) {
            if (line.trim() === '') continue

            if (line.startsWith('data:')) {
              const data = line.slice(5).trim()
              
              if (data === '[DONE]') {
                if (onComplete) {
                  onComplete()
                }
                continue
              }

              try {
                const jsonData = JSON.parse(data)
                const content = jsonData.choices?.[0]?.delta?.content
                const reasoningContent = jsonData.choices?.[0]?.delta?.reasoning_content

                if (content) {
                  // 如果之前是思考模式，先添加结束标签
                  if (isThinking) {
                    if (onMessage) {
                      onMessage({ content: '</think>\n\n' })
                    }
                    isThinking = false
                  }
                  // 普通内容，直接发送
                  if (onMessage) {
                    onMessage({ content })
                  }
                } else if (reasoningContent) {
                  // 思考内容
                  if (!isThinking) {
                    // 如果是新的思考开始，加上开始标签
                    if (onMessage) {
                      onMessage({ content: '<think>' })
                    }
                    isThinking = true
                  }
                  // 发送思考内容
                  if (onMessage) {
                    onMessage({ content: reasoningContent })
                  }
                }
              } catch (e) {
                console.error('解析消息失败:', e)
              }
            }
          }
        }
      } catch (error) {
        if (onError) {
          onError(error)
        }
      }
    }

    readStream()

    return cleanup
  } catch (error) {
    if (onError) {
      onError(error)
    }
    throw error
  }
}

/**
 * 调试方法组件
 * @param {Object} data - 调试参数
 * @param {string} data.componentName - 组件名称
 * @param {Object} data.systemParams - 系统参数
 * @param {Object} data.params - 组件参数
 * @returns {Promise} 返回调试结果
 */
export function debugMethodNode(data) {
  return request.post('/api/v1/agent-manager/component/execute', {
    componentName: data.componentName,
    systemParams: data.systemParams || {},
    params: data.params || {}
  })
}