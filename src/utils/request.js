import axios from 'axios'
import {Message} from "@arco-design/web-vue";


// 创建 axios 实例
const service = axios.create({
  baseURL: import.meta.env.VITE_NODE_ENV === 'production' 
    ? `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_BASE_API}`  // 完整URL
    : import.meta.env.VITE_BASE_API,
  timeout: 150000
})

// 添加环境信息日志
console.log('当前环境:', import.meta.env.VITE_NODE_ENV)
console.log('API基础路径:', import.meta.env.VITE_BASE_API)
console.log('服务器URL:', import.meta.env.VITE_BASE_URL)
console.log('完整请求地址:', import.meta.env.VITE_NODE_ENV === 'production' 
  ? `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_BASE_API}`
  : import.meta.env.VITE_BASE_API)

// 请求拦截器
service.interceptors.request.use(
  config => {
    console.log('发送请求:', config.url, config)
    // 从本地存储获取 token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    console.log('收到响应:', response.config.url, response)
    const res = response.data
    console.log('响应拦截器收到响应:', res)
    
    // 根据后台返回的统一格式判断请求是否成功
    if (res.code !== 200) {
      console.error('API 请求失败:', res.message || '系统错误')
      
      // 处理特殊状态码
      if (res.code === 401 || res.code === 403) {
        // 清除用户信息并跳转到登录页
        localStorage.removeItem('token')
        setTimeout(() => {
          location.href = '/login'
        }, 1500)
      }
      
      return Promise.reject(res)
    } else {
      // 请求成功，返回完整响应
      console.log('API 请求成功, 返回数据:', res)
      return res
    }
  },
  error => {
    console.error('请求错误:', error.config?.url, error)
    // 如果响应是HTML，打印出来
    if (error.response?.data && typeof error.response.data === 'string' && 
        error.response.data.includes('<!doctype html>')) {
      console.error('收到HTML响应而非API数据，可能是路由配置问题')
    }
    // 处理 HTTP 网络错误
    let message = '网络连接异常'
    if (error.response) {
      // HTTP 错误状态码
      const status = error.response.status
      switch (status) {
        case 401:
          message = '未授权，请重新登录'
          // 清除用户信息并跳转到登录页
          localStorage.removeItem('token')
          setTimeout(() => {
            location.href = '/login'
          }, 1500)
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = `请求错误 (${status})`
      }
    } else if (error.request) {
      // 请求发送成功，但没有收到响应
      message = '服务器无响应'
    } else {
      // 请求配置出错
      message = error.message
    }

    Message.error('请求失败：'+message)
    
    return Promise.reject(error)
  }
)

// 封装 HTTP 方法
export default {
  // GET 请求
  get(url, params = {}) {
    return service.get(url, { params })
  },
  
  // POST 请求
  post(url, data = {}) {
    return service.post(url, data)
  },
  
  // PUT 请求
  put(url, data = {}) {
    return service.put(url, data)
  },
  
  // DELETE 请求
  delete(url, params = {}) {
    return service.delete(url, { params })
  },
  
  // 上传文件
  upload(url, file) {
    const formData = new FormData()
    formData.append('file', file)
    
    return service.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },
  
  // 下载文件
  download(url, params = {}, filename) {
    return service.get(url, { 
      params, 
      responseType: 'blob' 
    }).then(response => {
      // 创建 blob 链接并下载
      const blob = new Blob([response])
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = filename || '下载文件'
      link.click()
      URL.revokeObjectURL(link.href)
    })
  }
} 