/**
 * localStorage 工具类
 */

// 工作流数据存储 key
const getWorkflowDataKey = (clientKey) => `workflowData_${clientKey}`
const getCurrentWorkflowJsonKey = (clientKey) => `current_workflow_json_${clientKey}`
const getCurrentWorkflowFullKey = (clientKey) => `current_workflow_full_${clientKey}`
const getCurrentZoomKey = (clientKey) => `current_zoom_${clientKey}`
const NEW_WORKFLOW_DATA_KEY = 'newWorkflowData'

// LLM 调试数据存储 key
const LLM_DEBUG_DATA_KEY = 'llm_debug_data'

// 添加数据失效时间（1小时）
const DATA_EXPIRY_TIME = 1 * 60 * 60 * 1000

// 日志查询环境存储 key
const LOG_TRACE_ENV_KEY = 'log_trace_env'
// 添加 traceId 存储 key
const LOG_TRACE_ID_KEY = 'log_trace_id'
// 添加 auth token 存储 key
const AUTH_TOKEN_KEY = 'auth_token'
// 添加 auth token 失效时间（10分钟）
const AUTH_TOKEN_EXPIRY_TIME = 10 * 60 * 1000

/**
 * 检查数据是否过期
 * @param {Object} data 存储的数据
 * @returns {boolean} 是否过期
 */
function isDataExpired(data) {
  if (!data || !data.timestamp) return true
  const now = Date.now()
  return now - data.timestamp > DATA_EXPIRY_TIME
}

/**
 * 获取工作流数据
 * @param {string} clientKey 客户端标识
 * @returns {Object|null} 工作流数据
 */
export function getWorkflowData(clientKey) {
  try {
    const data = localStorage.getItem(getWorkflowDataKey(clientKey))
    if (!data) return null
    
    const parsedData = JSON.parse(data)
    if (isDataExpired(parsedData)) {
      clearWorkflowData(clientKey)
      return null
    }
    return parsedData
  } catch (err) {
    console.error('获取工作流数据失败:', err)
    return null
  }
}

/**
 * 保存工作流数据
 * @param {Object} data 工作流数据
 * @param {string} clientKey 客户端标识
 * @returns {boolean} 是否保存成功
 */
export function saveWorkflowData(data, clientKey) {
  try {
    const dataWithTimestamp = {
      ...data,
      timestamp: Date.now()
    }
    localStorage.setItem(getWorkflowDataKey(clientKey), JSON.stringify(dataWithTimestamp))
    return true
  } catch (err) {
    console.error('保存工作流数据失败:', err)
    return false
  }
}

/**
 * 清除工作流数据
 * @param {string} clientKey 客户端标识
 */
export function clearWorkflowData(clientKey) {
  try {
    localStorage.removeItem(getWorkflowDataKey(clientKey))
  } catch (err) {
    console.error('清除工作流数据失败:', err)
  }
}

/**
 * 获取新建工作流数据
 * @returns {Object|null} 新建工作流数据
 */
export function getNewWorkflowData() {
  try {
    const data = localStorage.getItem(NEW_WORKFLOW_DATA_KEY)
    if (!data) return null
    
    const parsedData = JSON.parse(data)
    if (isDataExpired(parsedData)) {
      clearNewWorkflowData()
      return null
    }
    return parsedData
  } catch (err) {
    console.error('获取新建工作流数据失败:', err)
    return null
  }
}

/**
 * 保存新建工作流数据
 * @param {Object} data 新建工作流数据
 * @returns {boolean} 是否保存成功
 */
export function saveNewWorkflowData(data) {
  try {
    const dataWithTimestamp = {
      ...data,
      timestamp: Date.now()
    }
    localStorage.setItem(NEW_WORKFLOW_DATA_KEY, JSON.stringify(dataWithTimestamp))
    return true
  } catch (err) {
    console.error('保存新建工作流数据失败:', err)
    return false
  }
}

/**
 * 清除新建工作流数据
 */
export function clearNewWorkflowData() {
  try {
    localStorage.removeItem(NEW_WORKFLOW_DATA_KEY)
  } catch (err) {
    console.error('清除新建工作流数据失败:', err)
  }
}

/**
 * 获取当前工作流 JSON
 * @param {string} clientKey 客户端标识
 * @returns {Object|null} 当前工作流 JSON
 */
export function getCurrentWorkflowJson(clientKey) {
  try {
    const data = localStorage.getItem(getCurrentWorkflowJsonKey(clientKey))
    return data ? JSON.parse(data) : null
  } catch (err) {
    console.error('获取当前工作流 JSON 失败:', err)
    return null
  }
}

/**
 * 保存当前工作流 JSON
 * @param {Object} data 当前工作流 JSON
 * @param {string} clientKey 客户端标识
 * @returns {boolean} 是否保存成功
 */
export function saveCurrentWorkflowJson(data, clientKey) {
  try {
    localStorage.setItem(getCurrentWorkflowJsonKey(clientKey), JSON.stringify(data))
    return true
  } catch (err) {
    console.error('保存当前工作流 JSON 失败:', err)
    return false
  }
}

/**
 * 清除当前工作流 JSON
 * @param {string} clientKey 客户端标识
 */
export function clearCurrentWorkflowJson(clientKey) {
  try {
    localStorage.removeItem(getCurrentWorkflowJsonKey(clientKey))
  } catch (err) {
    console.error('清除当前工作流 JSON 失败:', err)
  }
}

/**
 * 获取当前工作流完整数据
 * @param {string} clientKey 客户端标识
 * @returns {Object|null} 当前工作流完整数据
 */
export function getCurrentWorkflowFull(clientKey) {
  try {
    const data = localStorage.getItem(getCurrentWorkflowFullKey(clientKey))
    return data ? JSON.parse(data) : null
  } catch (err) {
    console.error('获取当前工作流完整数据失败:', err)
    return null
  }
}

/**
 * 保存当前工作流完整数据
 * @param {Object} data 当前工作流完整数据
 * @param {string} clientKey 客户端标识
 * @returns {boolean} 是否保存成功
 */
export function saveCurrentWorkflowFull(data, clientKey) {
  try {
    localStorage.setItem(getCurrentWorkflowFullKey(clientKey), JSON.stringify(data))
    return true
  } catch (err) {
    console.error('保存当前工作流完整数据失败:', err)
    return false
  }
}

/**
 * 清除当前工作流完整数据
 * @param {string} clientKey 客户端标识
 */
export function clearCurrentWorkflowFull(clientKey) {
  try {
    localStorage.removeItem(getCurrentWorkflowFullKey(clientKey))
  } catch (err) {
    console.error('清除当前工作流完整数据失败:', err)
  }
}

/**
 * 获取当前缩放比例
 * @param {string} clientKey 客户端标识
 * @returns {number|null} 当前缩放比例
 */
export function getCurrentZoom(clientKey) {
  try {
    const zoom = localStorage.getItem(getCurrentZoomKey(clientKey))
    return zoom ? parseFloat(zoom) : null
  } catch (err) {
    console.error('获取当前缩放比例失败:', err)
    return null
  }
}

/**
 * 保存当前缩放比例
 * @param {number} zoom 当前缩放比例
 * @param {string} clientKey 客户端标识
 * @returns {boolean} 是否保存成功
 */
export function saveCurrentZoom(zoom, clientKey) {
  try {
    localStorage.setItem(getCurrentZoomKey(clientKey), zoom.toString())
    return true
  } catch (err) {
    console.error('保存当前缩放比例失败:', err)
    return false
  }
}

/**
 * 清除当前缩放比例
 * @param {string} clientKey 客户端标识
 */
export function clearCurrentZoom(clientKey) {
  try {
    localStorage.removeItem(getCurrentZoomKey(clientKey))
  } catch (err) {
    console.error('清除当前缩放比例失败:', err)
  }
}

/**
 * 清除所有工作流相关数据
 * @param {string} clientKey 客户端标识
 */
export function clearAllWorkflowData(clientKey) {
  clearWorkflowData(clientKey)
  clearNewWorkflowData()
  clearCurrentWorkflowJson(clientKey)
  clearCurrentWorkflowFull(clientKey)
  clearCurrentZoom(clientKey)
}

/**
 * 获取 LLM 调试数据
 * @returns {Object|null} LLM 调试数据
 */
export function getLlmDebugData() {
  try {
    const data = localStorage.getItem(LLM_DEBUG_DATA_KEY)
    return data ? JSON.parse(data) : null
  } catch (err) {
    console.error('获取 LLM 调试数据失败:', err)
    return null
  }
}

/**
 * 保存 LLM 调试数据
 * @param {Object} data LLM 调试数据
 * @returns {boolean} 是否保存成功
 */
export function saveLlmDebugData(data) {
  try {
    localStorage.setItem(LLM_DEBUG_DATA_KEY, JSON.stringify(data))
    return true
  } catch (err) {
    console.error('保存 LLM 调试数据失败:', err)
    return false
  }
}

/**
 * 清除 LLM 调试数据
 */
export function clearLlmDebugData() {
  try {
    localStorage.removeItem(LLM_DEBUG_DATA_KEY)
  } catch (err) {
    console.error('清除 LLM 调试数据失败:', err)
  }
}

/**
 * 获取日志查询环境
 * @returns {string|null} 日志查询环境
 */
export function getLogTraceEnv() {
  try {
    return localStorage.getItem(LOG_TRACE_ENV_KEY)
  } catch (err) {
    console.error('获取日志查询环境失败:', err)
    return null
  }
}

/**
 * 保存日志查询环境
 * @param {string} env 日志查询环境
 * @returns {boolean} 是否保存成功
 */
export function saveLogTraceEnv(env) {
  try {
    localStorage.setItem(LOG_TRACE_ENV_KEY, env)
    return true
  } catch (err) {
    console.error('保存日志查询环境失败:', err)
    return false
  }
}

/**
 * 清除日志查询环境
 */
export function clearLogTraceEnv() {
  try {
    localStorage.removeItem(LOG_TRACE_ENV_KEY)
  } catch (err) {
    console.error('清除日志查询环境失败:', err)
  }
}

/**
 * 获取日志 traceId
 * @returns {string|null} 日志 traceId
 */
export function getLogTraceId() {
  try {
    return localStorage.getItem(LOG_TRACE_ID_KEY)
  } catch (err) {
    console.error('获取日志 traceId 失败:', err)
    return null
  }
}

/**
 * 保存日志 traceId
 * @param {string} traceId 日志 traceId
 * @returns {boolean} 是否保存成功
 */
export function saveLogTraceId(traceId) {
  try {
    localStorage.setItem(LOG_TRACE_ID_KEY, traceId)
    return true
  } catch (err) {
    console.error('保存日志 traceId 失败:', err)
    return false
  }
}

/**
 * 清除日志 traceId
 */
export function clearLogTraceId() {
  try {
    localStorage.removeItem(LOG_TRACE_ID_KEY)
  } catch (err) {
    console.error('清除日志 traceId 失败:', err)
  }
} 

/**
 * 获取授权 token
 * @returns {Object|null} 授权 token 数据，包含 token 和 timestamp
 */
export function getAuthTokenFromStorage() {
  try {
    const data = localStorage.getItem(AUTH_TOKEN_KEY)
    if (!data) return null
    
    const parsedData = JSON.parse(data)
    const now = Date.now()
    
    // 检查 token 是否过期（10分钟）
    if (now - parsedData.timestamp > AUTH_TOKEN_EXPIRY_TIME) {
      console.log('本地存储的token已过期')
      clearAuthToken()
      return null
    }
    
    // 验证token格式
    const token = parsedData.token
    if (!token || typeof token !== 'string' || token.length < 10) {
      console.log('本地存储的token格式无效')
      clearAuthToken()
      return null
    }
    
    // 尝试解析JWT token，检查是否是有效的JWT格式
    try {
      const parts = token.split('.')
      if (parts.length !== 3) {
        console.log('本地存储的token不是有效的JWT格式')
        clearAuthToken()
        return null
      }
      
      // 检查是否可以解码
      const payload = JSON.parse(atob(parts[1]))
      
      // 检查token是否过期
      if (payload.exp && payload.exp * 1000 < Date.now()) {
        console.log('本地存储的JWT token已过期')
        clearAuthToken()
        return null
      }
    } catch (e) {
      console.log('本地存储的token解析失败:', e)
      clearAuthToken()
      return null
    }
    
    return parsedData.token
  } catch (err) {
    console.error('获取授权 token 失败:', err)
    clearAuthToken()
    return null
  }
}

/**
 * 保存授权 token
 * @param {string} token 授权 token
 * @returns {boolean} 是否保存成功
 */
export function saveAuthToken(token) {
  try {
    const dataWithTimestamp = {
      token,
      timestamp: Date.now()
    }
    localStorage.setItem(AUTH_TOKEN_KEY, JSON.stringify(dataWithTimestamp))
    return true
  } catch (err) {
    console.error('保存授权 token 失败:', err)
    return false
  }
}

/**
 * 清除授权 token
 */
export function clearAuthToken() {
  try {
    localStorage.removeItem(AUTH_TOKEN_KEY)
  } catch (err) {
    console.error('清除授权 token 失败:', err)
  }
} 