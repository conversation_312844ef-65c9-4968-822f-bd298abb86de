<template>
  <BaseNodeEditor 
    title="编辑 COZE 机器人节点" 
    :tabs="tabs" 
    v-model:currentTab="currentTab"
    @save="saveChanges" 
    @close="closeEditor"
  >
    <!-- 基本信息 Tab -->
    <template #basic>
      <div class="form-group">
        <label>节点标题</label>
        <a-input v-model="nodeData.title" placeholder="输入节点标题" allow-clear />
      </div>
      <div class="form-group">
        <label>描述</label>
        <textarea v-model="nodeData.desc" class="form-control" rows="2"></textarea>
      </div>
      <div class="form-group">
        <label>机器人信息</label>
        <div class="bot-info-inputs">
          <a-input 
            v-model="botInfo.bot_id" 
            placeholder="机器人ID" 
            allow-clear 
          />
          <a-input 
            v-model="botInfo.bot_name" 
            placeholder="机器人名称" 
            allow-clear 
          />
        </div>
      </div>
      <div class="form-group">
        <div class="checkbox-group">
          <a-checkbox id="stream" v-model="nodeData.stream">启用流式输出</a-checkbox>
        </div>
      </div>
      <div class="form-group">
        <div class="checkbox-group">
          <a-checkbox id="auto-save-history" v-model="nodeData.auto_save_history">自动保存历史记录</a-checkbox>
        </div>
      </div>
    </template>
    
    <!-- 提示词模板 Tab -->
    <template #prompt>
      <div class="prompt-templates">
        <div v-for="(prompt, index) in nodeData.prompt_template" :key="prompt.id" class="prompt-item">
          <div class="prompt-header">
            <div class="prompt-role">
              <select v-model="prompt.role" class="form-control role-select">
                <option value="system">系统</option>
                <option value="user">用户</option>
                <option value="assistant">助手</option>
              </select>
            </div>
            <div class="prompt-actions">
              <button class="move-btn" @click="movePromptUp(index)" :disabled="index === 0" title="上移">↑</button>
              <button class="move-btn" @click="movePromptDown(index)" :disabled="index === nodeData.prompt_template.length - 1" title="下移">↓</button>
              <button class="remove-btn" @click="removePrompt(index)" :disabled="nodeData.prompt_template.length <= 1" title="删除">×</button>
            </div>
          </div>
          <div class="prompt-body">
            <textarea v-model="prompt.text" class="form-control prompt-text" rows="5" placeholder="输入提示词内容..."></textarea>
          </div>
        </div>
        <button class="add-prompt-btn" @click="addPrompt">
          <span class="add-icon">+</span> 添加提示词
        </button>
      </div>
    </template>
    
    <!-- 数据预览 Tab -->
    <template #preview>
      <JsonEditor v-model="nodeData" @change="onJsonEditorChange" />
    </template>
  </BaseNodeEditor>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import BaseNodeEditor from './BaseNodeEditor.vue'
import JsonEditor from './common/JsonEditor.vue'
import { v4 as uuidv4 } from 'uuid'
import { Message } from '@arco-design/web-vue'
import { IconCheck, IconRefresh, IconCodeBlock, IconCopy } from '@arco-design/web-vue/es/icon'

const props = defineProps({
  node: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['save', 'close'])

// 节点数据
const nodeData = reactive(JSON.parse(JSON.stringify(props.node.data)))

// 使用计算属性来安全访问 bot_info
const botInfo = computed({
  get: () => {
    if (!nodeData.bot_info) {
      nodeData.bot_info = { bot_id: '', bot_name: '' }
    }
    return nodeData.bot_info
  },
  set: (value) => {
    nodeData.bot_info = value
  }
})

// 选项卡
const tabs = [
  { id: 'basic', label: '基本信息' },
  { id: 'prompt', label: '提示词' },
  { id: 'preview', label: 'JSON 编辑' }
]

const currentTab = ref('basic')

// 初始化数据
onMounted(() => {
  initializeNodeData()
})

// 初始化默认值
const initializeNodeData = () => {
  console.log('初始化 COZE 节点数据:', nodeData)
  
  // 确保基本字段存在
  if (!nodeData.title) nodeData.title = ''
  if (!nodeData.desc) nodeData.desc = ''
  if (!nodeData.type) nodeData.type = 'coze_bot'
  
  // 确保 bot_info 字段存在
  if (!nodeData.bot_info) {
    nodeData.bot_info = { bot_id: '', bot_name: '' }
  }
  
  // 确保 stream 字段存在
  if (nodeData.stream === undefined) nodeData.stream = false
  
  // 确保 auto_save_history 字段存在
  if (nodeData.auto_save_history === undefined) nodeData.auto_save_history = true
  
  // 确保 prompt_template 数组存在
  if (!nodeData.prompt_template || !Array.isArray(nodeData.prompt_template) || nodeData.prompt_template.length === 0) {
    nodeData.prompt_template = [
      { id: uuidv4(), role: 'user', text: '' }
    ]
  }
}

// 提示词相关方法
const addPrompt = () => {
  nodeData.prompt_template.push({
    id: uuidv4(),
    role: 'user',
    text: ''
  })
}

const removePrompt = (index) => {
  if (nodeData.prompt_template.length > 1) {
    nodeData.prompt_template.splice(index, 1)
  }
}

const movePromptUp = (index) => {
  if (index > 0) {
    const temp = nodeData.prompt_template[index]
    nodeData.prompt_template[index] = nodeData.prompt_template[index - 1]
    nodeData.prompt_template[index - 1] = temp
  }
}

const movePromptDown = (index) => {
  if (index < nodeData.prompt_template.length - 1) {
    const temp = nodeData.prompt_template[index]
    nodeData.prompt_template[index] = nodeData.prompt_template[index + 1]
    nodeData.prompt_template[index + 1] = temp
  }
}

// 监听 props.node 变化，重新初始化数据
watch(() => props.node, () => {
  Object.assign(nodeData, JSON.parse(JSON.stringify(props.node.data)))
  initializeNodeData()
}, { deep: true })

// 监听 nodeData 变化
watch(nodeData, () => {
  // JSON编辑器内容更新由JsonEditor组件处理
}, { deep: true })

// 保存更改
const saveChanges = () => {
  emit('save', {
    ...props.node,
    data: { ...nodeData }
  })
}

// 关闭编辑器
const closeEditor = () => {
  emit('close')
}

// 处理 JSON 编辑器变化
const onJsonEditorChange = (newData) => {
  // 将新的JSON数据更新到nodeData
  Object.assign(nodeData, newData)
}
</script>

<style scoped>
/* 表单样式 */
.form-group {
  margin-bottom: 15px;
}

/* 覆盖 Arco Input 样式 */
:deep(.arco-input-wrapper) {
  background-color: #fff !important;
}

:deep(.arco-input) {
  background-color: #fff !important;
}

:deep(.arco-input-number) {
  background-color: #fff !important;
}

:deep(.arco-input-number-input) {
  background-color: #fff !important;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-control:focus {
  border-color: #2196f3;
  outline: none;
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.bot-info-inputs {
  display: flex;
  gap: 10px;
}

/* 提示词模板样式 */
.prompt-templates {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.prompt-item {
  border: 1px solid #eee;
  border-radius: 6px;
  overflow: hidden;
}

.prompt-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #eee;
}

.prompt-role {
  flex: 1;
}

.role-select {
  width: 120px;
}

.prompt-actions {
  display: flex;
  gap: 5px;
}

.move-btn, .remove-btn {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  font-size: 14px;
  padding: 5px;
}

.move-btn:hover, .remove-btn:hover {
  color: #333;
}

.move-btn:disabled {
  color: #ccc;
  cursor: not-allowed;
}

.remove-btn {
  color: #f44336;
}

.remove-btn:hover {
  color: #d32f2f;
}

.prompt-body {
  padding: 15px;
}

.prompt-text {
  width: 100%;
  min-height: 100px;
  resize: vertical;
}

.add-prompt-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px;
  background-color: #f5f5f5;
  border: 1px dashed #ccc;
  border-radius: 6px;
  cursor: pointer;
  color: #666;
  transition: all 0.2s;
}

.add-prompt-btn:hover {
  background-color: #e0e0e0;
  color: #333;
}

.add-icon {
  font-size: 16px;
  font-weight: bold;
}
</style> 