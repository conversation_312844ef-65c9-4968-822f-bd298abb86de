<template>
  <BaseNodeEditor 
    title="编辑 Method 节点"
    :tabs="tabs" 
    v-model:currentTab="currentTab"
    @save="saveChanges" 
    @close="closeEditor"
  >
    <!-- 基本信息 Tab -->
    <template #basic>
      <div class="form-group">
        <label for="title">节点标题</label>
        <a-input type="text" id="title" v-model="nodeData.title" placeholder="输入节点标题" allow-clear />
      </div>
      
      <div class="form-group">
        <label for="desc">描述</label>
        <textarea id="desc" v-model="nodeData.desc" placeholder="输入节点描述"></textarea>
      </div>
      
      <div class="form-group">
        <label for="component-name">组件名称</label>
        <a-input type="text" id="component-name" v-model="nodeData.component_name" placeholder="输入组件名称" allow-clear />
      </div>
    </template>
    
    <!-- 输出定义 Tab -->
    <template #outputs>
      <div class="outputs-section">
        <div class="section-header">
          <h4>输出字段定义</h4>
          <div class="output-actions">
            <a-input v-model="newOutputName" placeholder="字段名称" allow-clear />
            <select v-model="newOutputType" class="form-control">
              <option value="string">字符串</option>
              <option value="number">数字</option>
              <option value="boolean">布尔值</option>
              <option value="object">对象</option>
              <option value="array">数组</option>
            </select>
            <button class="add-btn" @click="addOutput" :disabled="!newOutputName">添加字段</button>
          </div>
        </div>
        
        <div v-if="Object.keys(nodeData.outputs || {}).length === 0" class="no-outputs">
          暂无输出字段，点击上方按钮添加
        </div>
        
        <div v-else class="output-list">
          <div v-for="(output, name) in nodeData.outputs" :key="name" class="output-item">
            <div class="output-info">
              <div class="output-name">{{ name }}</div>
              <div class="output-type">{{ getTypeName(output.type) }}</div>
            </div>
            <button class="remove-btn" @click="removeOutput(name)">删除</button>
          </div>
        </div>
      </div>
    </template>
    
    <!-- 变量配置 Tab -->
    <template #variables>
      <div class="variables-section">
        <div class="section-header">
          <h4>输入变量配置</h4>
          <div class="variable-actions">
            <a-input v-model="newVariableName" placeholder="变量名称" allow-clear />
            <button class="add-btn" @click="addVariable" :disabled="!newVariableName">添加变量</button>
            <a-dropdown trigger="click">
              <button class="add-btn preset-btn">添加预设变量</button>
              <template #content>
                <a-doption @click="addPresetVariable('sys')">系统变量(sys)</a-doption>
                <a-doption @click="addPresetVariable('user')">用户变量(user)</a-doption>
              </template>
            </a-dropdown>
          </div>
        </div>
        
        <div v-if="!nodeData.variables || nodeData.variables.length === 0" class="no-variables">
          暂无变量，点击上方按钮添加
        </div>
        
        <div v-else class="variable-list">
          <div v-for="(variable, index) in nodeData.variables" :key="index" class="variable-item">
            <div class="variable-content">
              <!-- 变量类型显示行 -->
              <div class="variable-row variable-type-row">
                <label>变量类型</label>
                <div class="variable-type-selector">
                  <div 
                    :class="['type-option', !isPresetVariable(variable.value_selector[0]) ? 'active' : '']"
                    @click="setVariableType(variable, 'normal')"
                  >普通变量</div>
                  <div 
                    :class="['type-option', variable.value_selector[0] === 'sys' ? 'active sys-active' : '']"
                    @click="setVariableType(variable, 'sys')"
                  >系统变量</div>
                  <div 
                    :class="['type-option', variable.value_selector[0] === 'user' ? 'active user-active' : '']"
                    @click="setVariableType(variable, 'user')"
                  >用户变量</div>
                </div>
              </div>
              
              <div class="variable-row">
                <label>变量名称</label>
                <a-input v-model="variable.variable" placeholder="变量名" allow-clear />
              </div>

              <!-- 预设变量的输入框 -->
              <div class="variable-row" v-if="isPresetVariable(variable.value_selector[0])">
                <label>系统字段</label>
                <a-input
                  v-model="variable.value_selector[1]"
                  :placeholder="`请输入${variable.value_selector[0] === 'sys' ? '系统' : '用户'}变量字段`"
                  allow-clear
                />
              </div>

              <!-- 普通变量的节点选择器 -->
              <div class="variable-row" v-else>
                <label>节点选择</label>
                <NodeSelector
                  v-model="variable.value_selector[0]"
                  :exclude-node-id="props.node.id"
                />
              </div>

              <!-- 普通变量的输出选择器 -->
              <div class="variable-row" v-if="!isPresetVariable(variable.value_selector[0]) && variable.value_selector[0]">
                <label>变量选择</label>
                <NodeOutputSelector
                  v-model="variable.value_selector[1]"
                  :node-id="variable.value_selector[0]"
                  :current-node-id="props.node.id"
                />
              </div>
            </div>
            <div class="variable-actions">
              <button class="move-btn" @click="moveVariableUp(index)" :disabled="index === 0">↑</button>
              <button class="move-btn" @click="moveVariableDown(index)" :disabled="index === nodeData.variables.length - 1">↓</button>
              <button class="remove-btn" @click="removeVariable(index)">删除</button>
            </div>
          </div>
        </div>
      </div>
    </template>
    
    <!-- 数据预览 Tab -->
    <template #preview>
      <JsonEditor v-model="nodeData" @change="onJsonEditorChange" />
    </template>
  </BaseNodeEditor>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import BaseNodeEditor from './BaseNodeEditor.vue'
import JsonEditor from './common/JsonEditor.vue'
import { Message } from '@arco-design/web-vue'
import { IconCheck, IconRefresh, IconCodeBlock, IconCopy } from '@arco-design/web-vue/es/icon'
import { getWorkflowData, getCurrentWorkflowFull } from '../../utils/storage'
import { useRoute } from 'vue-router'
import NodeSelector from '../selectors/NodeSelector.vue'
import NodeOutputSelector from '../selectors/NodeOutputSelector.vue'
import { Dropdown as ADropdown, Doption as ADoption } from '@arco-design/web-vue'

const props = defineProps({
  node: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['save', 'close'])
const route = useRoute()

// 节点数据
const nodeData = reactive(JSON.parse(JSON.stringify(props.node.data)))

// 选项卡 - 移除代码标签页
const tabs = [
  { id: 'basic', label: '基本信息' },
  { id: 'outputs', label: '输出定义' },
  { id: 'variables', label: '变量配置' },
  { id: 'preview', label: 'JSON 编辑' }
]

const currentTab = ref('basic')
const newOutputName = ref('')
const newOutputType = ref('string')
const newVariableName = ref('')

// 监听 props.node 变化，重新初始化数据
watch(() => props.node, () => {
  Object.assign(nodeData, JSON.parse(JSON.stringify(props.node.data)))
  initializeNodeData()
}, { deep: true })

// 监听 nodeData 变化
watch(nodeData, () => {
  // JSON编辑器内容更新由JsonEditor组件处理
}, { deep: true })

// 初始化节点数据
onMounted(() => {
  initializeNodeData()
})

// 初始化节点数据
const initializeNodeData = () => {
  console.log('初始化 Method 节点数据:', nodeData)
  
  // 确保基本字段存在
  if (!nodeData.title) nodeData.title = ''
  if (!nodeData.desc) nodeData.desc = ''
  if (!nodeData.type) nodeData.type = 'method'
  if (!nodeData.component_name) nodeData.component_name = ''
  
  // 确保 code 字段存在，但我们不会在界面上显示它
  if (!nodeData.code) {
    nodeData.code = '\ndef main() -> dict:\n    # 处理逻辑\n    \n    # 返回结果\n    return {\n        "result": "",\n    }\n'
  }
  
  // 确保 outputs 对象存在
  if (!nodeData.outputs) {
    nodeData.outputs = {}
  }
  
  // 确保 variables 数组存在
  if (!Array.isArray(nodeData.variables)) {
    nodeData.variables = []
  }
  
  // 确保每个变量都有正确的结构
  nodeData.variables.forEach(variable => {
    if (!variable.value_selector) {
      variable.value_selector = ['', '']
    } else if (!Array.isArray(variable.value_selector)) {
      variable.value_selector = ['', '']
    } else if (variable.value_selector.length < 2) {
      while (variable.value_selector.length < 2) {
        variable.value_selector.push('')
      }
    }

    // 处理旧版数据迁移：如果有preset_value，将其转移到value_selector[1]
    if (isPresetVariable(variable.variable) && variable.preset_value) {
      variable.value_selector = [variable.variable, variable.preset_value]
      delete variable.preset_value
    }
  })
}

// 获取类型名称
const getTypeName = (type) => {
  const typeMap = {
    'string': '字符串',
    'number': '数字',
    'boolean': '布尔值',
    'object': '对象',
    'array': '数组'
  }
  return typeMap[type] || type
}

// 添加输出字段
const addOutput = () => {
  if (newOutputName.value && !nodeData.outputs[newOutputName.value]) {
    nodeData.outputs[newOutputName.value] = {
      type: newOutputType.value,
      children: null
    }
    newOutputName.value = ''
  }
}

// 移除输出字段
const removeOutput = (name) => {
  if (nodeData.outputs[name]) {
    const { [name]: _, ...rest } = nodeData.outputs
    nodeData.outputs = rest
  }
}

// 添加变量
const addVariable = () => {
  if (newVariableName.value) {
    // 使用unshift代替push，将新变量添加到数组开头
    nodeData.variables.unshift({
      variable: newVariableName.value,
      value_selector: ['', '']
    })
    newVariableName.value = ''
  }
}

// 移除变量
const removeVariable = (index) => {
  nodeData.variables.splice(index, 1)
}

// 上移变量
const moveVariableUp = (index) => {
  if (index > 0) {
    const temp = nodeData.variables[index]
    nodeData.variables[index] = nodeData.variables[index - 1]
    nodeData.variables[index - 1] = temp
  }
}

// 下移变量
const moveVariableDown = (index) => {
  if (index < nodeData.variables.length - 1) {
    const temp = nodeData.variables[index]
    nodeData.variables[index] = nodeData.variables[index + 1]
    nodeData.variables[index + 1] = temp
  }
}

// 保存更改
const saveChanges = () => {
  emit('save', {
    id: props.node.id,
    data: nodeData
  })
}

// 关闭编辑器
const closeEditor = () => {
  emit('close')
}

// 处理 JSON 编辑器变化
const onJsonEditorChange = (newData) => {
  // 将新的JSON数据更新到nodeData
  Object.assign(nodeData, newData)
}

// 添加预设变量
const addPresetVariable = (type) => {
  // 使用unshift代替push，将新变量添加到数组开头
  nodeData.variables.unshift({
    variable: "",  // 变量名留空，由用户填写
    value_selector: [type, ''] // 第一个值为预设类型(sys/user)，第二个值为用户需要选择的字段
  })
}

// 检查是否为预设变量
const isPresetVariable = (selectorValue) => {
  const presetVariables = ['sys', 'user']
  return presetVariables.includes(selectorValue)
}

// 设置变量类型
const setVariableType = (variable, type) => {
  if (type === 'normal') {
    // 如果之前是预设变量，重置选择器
    if (isPresetVariable(variable.value_selector[0])) {
      variable.value_selector = ['', '']
    }
  } else if (type === 'sys' || type === 'user') {
    // 设置为预设变量
    variable.value_selector = [type, '']
  }
}
</script>

<style scoped>
/* 表单样式 */
.form-group {
  margin-bottom: 20px;
}

/* 覆盖 Arco Input 样式 */
:deep(.arco-input-wrapper) {
  background-color: #fff !important;
}

:deep(.arco-input) {
  background-color: #fff !important;
}

:deep(.arco-input-number) {
  background-color: #fff !important;
}

:deep(.arco-input-number-input) {
  background-color: #fff !important;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

input[type="text"],
textarea,
select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
}

input[type="text"]:focus,
textarea:focus,
select:focus {
  border-color: #2196f3;
  outline: none;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

/* 输出定义样式 */
.outputs-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h4 {
  margin: 0;
  color: #333;
}

.output-actions {
  display: flex;
  gap: 10px;
}

.output-actions .form-control {
  width: auto;
}

.add-btn {
  padding: 8px 12px;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-btn:hover {
  background-color: #0b7dda;
}

.add-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.no-outputs, .no-variables {
  padding: 20px;
  text-align: center;
  color: #666;
  background-color: #f9f9f9;
  border-radius: 6px;
}

.output-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.output-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.output-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.output-name {
  font-family: 'Courier New', monospace;
  color: #333;
  font-weight: 500;
}

.output-type {
  color: #666;
  font-size: 12px;
  background-color: #e3f2fd;
  padding: 2px 6px;
  border-radius: 3px;
}

.remove-btn {
  background: none;
  border: none;
  color: #f44336;
  cursor: pointer;
  font-size: 14px;
  padding: 5px;
}

.remove-btn:hover {
  color: #d32f2f;
}

/* 变量配置样式 */
.variables-section {
  margin-bottom: 20px;
}

.section-header h4 {
  margin-bottom: 15px;
}

.variable-actions {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-left: 10px;
}

/* 输入框样式 */
.variable-actions .arco-input-wrapper {
  width: 100%; /* 输入框宽度100% */
  margin-bottom: 5px;
}

/* 按钮通用样式 */
.add-btn {
  width: 120px; /* 固定宽度 */
  padding: 8px 0;
  text-align: center;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.add-btn:hover {
  background-color: #0d8aee;
  transform: translateY(-2px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.add-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 预设变量按钮样式 */
.preset-btn {
  background-color: #673ab7;
  width: 120px; /* 确保宽度相同 */
}

.preset-btn:hover {
  background-color: #5e35b1;
}

.no-variables {
  padding: 20px;
  text-align: center;
  color: #666;
  background-color: #f9f9f9;
  border-radius: 6px;
}

.variable-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 15px;
}

.variable-item {
  display: flex;
  justify-content: space-between;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.variable-content {
  flex-grow: 1;
  width: calc(100% - 50px);
}

.variable-row {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.variable-row:last-child {
  margin-bottom: 0;
}

.variable-row label {
  width: 80px;
  margin-bottom: 0;
  flex-shrink: 0;
}

.move-btn {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.move-btn:hover {
  background-color: #e0e0e0;
}

.move-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 修复 a-select 样式 */
:deep(.arco-select) {
  width: 100%;
  flex-grow: 1;
}

:deep(.arco-select-view) {
  background-color: #fff;
}

:deep(.arco-select-view:hover) {
  border-color: #2196f3;
}

:deep(.arco-select-view-focused) {
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

/* JSON 编辑器样式 */
.json-editor-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 10px;
}

.json-editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.json-status {
  font-size: 12px;
  color: var(--color-text-3);
}

.json-error {
  color: var(--color-danger-6);
  font-weight: 500;
}

.json-editor-wrapper {
  flex: 1;
  overflow: hidden;
  border-radius: 4px;
  border: 1px solid var(--color-border-2);
}

:deep(.arco-textarea) {
  font-family: 'Fira Code', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  resize: none;
  border: none;
  border-radius: 0;
  padding: 12px;
}

:deep(.arco-textarea:focus) {
  box-shadow: none;
}

/* 修复滚动条样式 */
:deep(.arco-textarea)::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

:deep(.arco-textarea)::-webkit-scrollbar-track {
  background: var(--color-fill-1);
  border-radius: 4px;
}

:deep(.arco-textarea)::-webkit-scrollbar-thumb {
  background-color: var(--color-fill-3);
  border-radius: 4px;
}

:deep(.arco-textarea)::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-fill-4);
}

/* 变量类型行样式 */
.variable-type-row {
  margin-bottom: 15px;
  border-bottom: 1px dashed #e8e8e8;
  padding-bottom: 12px;
}

.variable-type-selector {
  display: flex;
  gap: 10px;
  flex-grow: 1;
}

.type-option {
  padding: 5px 12px;
  border-radius: 4px;
  background-color: #f5f5f5;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.2s;
  text-align: center;
  flex: 1;
  border: 1px solid transparent;
}

.type-option:hover {
  background-color: #e0e0e0;
}

.type-option.active {
  background-color: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
  font-weight: 500;
}

.type-option.sys-active {
  background-color: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

.type-option.user-active {
  background-color: #f0e6ff;
  border-color: #d3adf7;
  color: #722ed1;
}
</style>