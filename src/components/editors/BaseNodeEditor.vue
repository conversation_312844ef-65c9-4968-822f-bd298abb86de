<template>
  <div class="node-editor-overlay">
    <div class="node-editor">
      <div class="editor-header">
        <h2>{{ title }}</h2>
        <button class="close-btn" @click="close">×</button>
      </div>
      
      <div class="editor-tabs">
        <button 
          v-for="tab in tabs" 
          :key="tab.id" 
          class="tab-btn" 
          :class="{ active: currentTab === tab.id }"
          @click="$emit('update:currentTab', tab.id)"
        >
          {{ tab.label }}
        </button>
      </div>
      
      <div class="editor-body">
        <slot :name="currentTab"></slot>
      </div>
      
      <div class="editor-footer">
        <button class="cancel-btn" @click="close">取消</button>
        <button class="save-btn" @click="save">保存</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted } from 'vue'

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  tabs: {
    type: Array,
    required: true
  },
  currentTab: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['save', 'close', 'update:currentTab'])

// 处理键盘事件
const handleKeyDown = (event) => {
  if (event.key === 'Escape') {
    close()
  }
}

// 添加键盘事件监听
onMounted(() => {
  document.addEventListener('keydown', handleKeyDown)
})

// 移除键盘事件监听
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
})

const save = () => {
  emit('save')
}

const close = () => {
  emit('close')
}
</script>

<style scoped>
/* 全局输入框样式 */
:deep(.arco-input-wrapper) {
  border: 1px solid #ddd !important;
}

:deep(.arco-input-wrapper:hover) {
  border-color: #2196f3 !important;
}

:deep(.arco-input-wrapper:focus-within) {
  border-color: #2196f3 !important;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1) !important;
}

:deep(.arco-textarea-wrapper) {
  border: 1px solid #ddd !important;
}

:deep(.arco-textarea-wrapper:hover) {
  border-color: #2196f3 !important;
}

:deep(.arco-textarea-wrapper:focus-within) {
  border-color: #2196f3 !important;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1) !important;
}

.node-editor-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.node-editor {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 1200px;
  height: 90vh;
  max-height: 900px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #eee;
  background-color: #f9f9f9;
}

.editor-header h2 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #999;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.close-btn:hover {
  color: #333;
}

.editor-tabs {
  display: flex;
  border-bottom: 1px solid #eee;
  padding: 0 24px;
  background-color: #fff;
  overflow-x: auto;
}

.tab-btn {
  padding: 12px 20px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  position: relative;
  white-space: nowrap;
}

.tab-btn.active {
  color: #2196f3;
  font-weight: 500;
}

.tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #2196f3;
}

.editor-body {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  min-height: 0;
  max-height: calc(90vh - 200px);
}

.editor-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  border-top: 1px solid #eee;
  background-color: #f9f9f9;
}

.cancel-btn, .save-btn {
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-btn {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  color: #666;
}

.cancel-btn:hover {
  background-color: #e0e0e0;
}

.save-btn {
  background-color: #2196f3;
  border: 1px solid #2196f3;
  color: white;
}

.save-btn:hover {
  background-color: #0b7dda;
}

@media (max-width: 768px) {
  .node-editor {
    width: 95%;
    height: 95vh;
  }
  
  .editor-header {
    padding: 12px 16px;
  }
  
  .editor-body {
    padding: 16px;
  }
  
  .editor-footer {
    padding: 12px 16px;
  }
}
</style>