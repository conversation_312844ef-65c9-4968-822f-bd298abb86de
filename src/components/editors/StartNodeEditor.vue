<template>
  <div class="start-node-editor">
    <a-form :model="formData" layout="vertical">
      <a-form-item field="title" label="节点标题">
        <a-input v-model="formData.title" placeholder="请输入节点标题" />
      </a-form-item>
      <a-form-item field="desc" label="节点描述">
        <a-textarea v-model="formData.desc" placeholder="请输入节点描述" />
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  nodeData: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:nodeData'])

const formData = ref({
  title: props.nodeData.title || '开始',
  desc: props.nodeData.desc || '',
  type: 'start',
  variables: []
})

// 监听表单数据变化，更新节点数据
watch(formData, (newVal) => {
  emit('update:nodeData', {
    ...props.nodeData,
    ...newVal
  })
}, { deep: true })
</script>

<style scoped>
.start-node-editor {
  padding: 16px;
}
</style> 