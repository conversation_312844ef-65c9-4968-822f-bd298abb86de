<template>
  <div class="json-editor-container">
    <div class="editor-toolbar">
      <a-space>
        <a-button size="small" type="primary" @click="applyChanges" :disabled="!isValidJson">
          <template #icon><icon-check /></template>
          应用更改
        </a-button>
        <a-button size="small" @click="resetEditor">
          <template #icon><icon-refresh /></template>
          重置
        </a-button>
        <a-button size="small" @click="formatJson">
          <template #icon><icon-code-block /></template>
          格式化
        </a-button>
        <a-button size="small" @click="copyToClipboard">
          <template #icon><icon-copy /></template>
          复制
        </a-button>
      </a-space>
      
      <div class="json-status" :class="{ 'json-error': !isValidJson, 'json-valid': isValidJson }">
        <span class="status-dot" :class="{ 'error-dot': !isValidJson, 'valid-dot': isValid<PERSON><PERSON> }"></span>
        {{ jsonStatusMessage }}
      </div>
    </div>
    
    <div class="json-editor-wrapper">
      <MonacoEditor
        ref="monacoEditor"
        v-model:value="editorContent"
        language="json"
        theme="vs"
        :options="editorOptions"
        @change="onEditorChange"
        @mount="onEditorMounted"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import { IconCheck, IconRefresh, IconCodeBlock, IconCopy } from '@arco-design/web-vue/es/icon'
import MonacoEditor from '@guolao/vue-monaco-editor'

const props = defineProps({
  // 初始数据
  modelValue: {
    type: [Object, Array],
    required: true
  },
  // 是否保留内部属性（以 _ 开头的属性）
  preserveInternalProps: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

// Monaco 编辑器相关
const monacoEditor = ref(null)
const editorInstance = ref(null)

// 编辑器配置
const editorOptions = {
  automaticLayout: true,
  scrollBeyondLastLine: false,
  minimap: { enabled: true, showSlider: "mouseover" },
  lineNumbers: 'on',
  tabSize: 2,
  fontSize: 14,
  fontFamily: 'Fira Code, Monaco, Menlo, Consolas, monospace',
  renderLineHighlight: 'all',
  formatOnPaste: true,
  folding: false,
  matchBrackets: 'always',
  wordWrap: 'on',
  scrollbar: {
    useShadows: true,
    verticalScrollbarSize: 8,
    horizontalScrollbarSize: 8
  }
}

// 编辑器状态
const editorContent = ref('')
const isValidJson = ref(true)
const jsonStatusMessage = ref('JSON 有效')

// Monaco 模型和验证相关
let jsonModelUri = null
let jsonModel = null
let jsonSeverity = null

// 监听输入变化并应用验证
const onEditorChange = (value) => {
  validateJson(value)
}

// Monaco 编辑器挂载完成
const onEditorMounted = (editor) => {
  editorInstance.value = editor
  
  // 设置JSON验证
  editor.onDidFocusEditorWidget(() => {
    const monaco = window.monaco
    if (monaco) {
      monaco.languages.json.jsonDefaults.setDiagnosticsOptions({
        validate: true,
        allowComments: false,
        schemas: []
      })
    }
  })
  
  updateEditorContent()
}

// 监听 props 变化
watch(() => props.modelValue, (newVal) => {
  if (newVal && !isEditing.value) {
    updateEditorContent()
  }
}, { deep: true })

// 跟踪编辑状态，避免循环更新
const isEditing = ref(false)

// 更新编辑器内容
const updateEditorContent = () => {
  try {
    if (props.modelValue) {
      isEditing.value = true
      
      // 将对象转换为格式化的JSON字符串
      const cleanData = JSON.parse(JSON.stringify(props.modelValue))
      editorContent.value = JSON.stringify(cleanData, null, 2)
      
      // 延迟状态重置，避免与用户输入冲突
      setTimeout(() => {
        isEditing.value = false
        validateJson(editorContent.value)
      }, 100)
    } else {
      editorContent.value = '{}'
      isValidJson.value = true
      jsonStatusMessage.value = 'JSON 有效'
    }
  } catch (error) {
    console.error('JSON序列化错误:', error)
    isValidJson.value = false
    jsonStatusMessage.value = `序列化错误: ${error.message}`
  }
}

// 验证JSON
const validateJson = (jsonString) => {
  try {
    if (!jsonString || jsonString.trim() === '') {
      isValidJson.value = false
      jsonStatusMessage.value = 'JSON不能为空'
      return
    }
    
    JSON.parse(jsonString)
    isValidJson.value = true
    jsonStatusMessage.value = 'JSON 有效'
  } catch (error) {
    isValidJson.value = false
    jsonStatusMessage.value = `JSON 错误: ${error.message}`
  }
}

// 应用更改
const applyChanges = () => {
  if (!isValidJson.value) {
    Message.error(jsonStatusMessage.value)
    return
  }
  
  try {
    const updatedData = JSON.parse(editorContent.value)
    
    // 保存内部属性
    if (props.preserveInternalProps && typeof props.modelValue === 'object' && !Array.isArray(updatedData)) {
      const internalProps = {}
      Object.entries(props.modelValue).forEach(([key, value]) => {
        if (key.startsWith('_')) {
          internalProps[key] = value
        }
      })
      
      // 合并更新的数据和内部属性
      Object.assign(updatedData, internalProps)
    }
    
    emit('update:modelValue', updatedData)
    emit('change', updatedData)
    Message.success('JSON 更改已应用')
  } catch (error) {
    Message.error(`应用更改失败: ${error.message}`)
  }
}

// 重置编辑器
const resetEditor = () => {
  updateEditorContent()
  Message.info('JSON 已重置')
}

// 格式化JSON
const formatJson = () => {
  if (!isValidJson.value) {
    Message.warning('无法格式化无效的 JSON')
    return
  }
  
  try {
    const parsed = JSON.parse(editorContent.value)
    editorContent.value = JSON.stringify(parsed, null, 2)
    
    // 使用Monaco编辑器的格式化功能
    if (editorInstance.value) {
      setTimeout(() => {
        editorInstance.value.getAction('editor.action.formatDocument').run()
      }, 100)
    }
    
    Message.success('JSON 已格式化')
  } catch (error) {
    Message.error(`格式化失败: ${error.message}`)
  }
}

// 复制到剪贴板
const copyToClipboard = async () => {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(editorContent.value)
      Message.success('JSON 已复制到剪贴板')
    } else {
      const textArea = document.createElement('textarea')
      textArea.value = editorContent.value
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()

      try {
        const successful = document.execCommand('copy')
        if (successful) {
          Message.success('JSON 已复制到剪贴板')
        } else {
          throw new Error('execCommand failed')
        }
      } catch (fallbackErr) {
        Message.info('请手动复制 JSON 内容')
        console.error('复制失败，请手动复制:', fallbackErr)
      } finally {
        document.body.removeChild(textArea)
      }
    }
  } catch (err) {
    Message.error('复制失败: ' + err.message)
    console.error('复制失败:', err)
  }
}

// 暴露方法给父组件
defineExpose({
  applyChanges
})

// 初始化
onMounted(() => {
  console.log('Simple JSON Editor mounted, modelValue:', props.modelValue)
})
</script>

<style scoped>
.json-editor-container {
  display: flex;
  flex-direction: column;
  min-height: 500px;
  height: 100%;
  gap: 12px;
  padding: 8px;
  background-color: #fafafa;
  border-radius: 6px;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  flex-wrap: wrap;
  gap: 8px;
}

.json-status {
  font-size: 13px;
  color: #666;
  display: flex;
  align-items: center;
  padding: 0 8px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.error-dot {
  background-color: #f5222d;
}

.valid-dot {
  background-color: #52c41a;
}

.json-error {
  color: #f5222d;
}

.json-valid {
  color: #52c41a;
}

.json-editor-wrapper {
  flex: 1;
  min-height: 400px;
  overflow: visible;
  border-radius: 4px;
  border: 1px solid #e5e5e5;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

/* 编辑器高度 */
:deep(.monaco-editor) {
  height: 100% !important;
}

:deep(.monaco-editor .margin) {
  background-color: #f5f5f5;
}
</style>