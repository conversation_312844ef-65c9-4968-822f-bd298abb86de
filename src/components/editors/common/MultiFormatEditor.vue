<template>
  <div class="multi-format-editor-container">
    <div class="editor-toolbar">
      <a-space>
        <!-- 格式选择器 -->
        <a-select v-model="currentFormat" @change="onFormatChange" @click="() => console.log('[DEBUG] 格式选择器被点击')" style="width: 120px;">
          <a-option value="json">JSON</a-option>
          <a-option value="yaml">YAML</a-option>
          <a-option value="markdown">Markdown</a-option>
        </a-select>
        
        <a-divider direction="vertical" />
        

        <a-button size="small" @click="resetEditor">
          <template #icon><icon-refresh /></template>
          重置
        </a-button>
        <a-button size="small" @click="formatContent" v-if="currentFormat !== 'markdown'">
          <template #icon><icon-code-block /></template>
          格式化
        </a-button>
        <a-button size="small" @click="toggleMarkdownPreview" v-if="currentFormat === 'markdown'">
          <template #icon><icon-eye v-if="!isMarkdownPreview" /><icon-edit v-else /></template>
          {{ isMarkdownPreview ? '编辑' : '预览' }}
        </a-button>

        <a-button size="small" @click="copyToClipboard">
          <template #icon><icon-copy /></template>
          复制
        </a-button>
      </a-space>
      
      <div class="content-status" :class="{ 'content-error': !isValidContent, 'content-valid': isValidContent }">
        <span class="status-dot" :class="{ 'error-dot': !isValidContent, 'valid-dot': isValidContent }"></span>
        {{ statusMessage }}
      </div>
    </div>
    
    <div class="editor-wrapper">
      <!-- Monaco 编辑器 -->
      <MonacoEditor
        v-show="!isMarkdownPreview || currentFormat !== 'markdown'"
        ref="monacoEditor"
        v-model:value="editorContent"
        :language="currentFormat"
        theme="vs"
        :options="editorOptions"
        @change="onEditorChange"
        @mount="onEditorMounted"
      />
      
      <!-- Markdown 预览 -->
      <div 
        v-show="isMarkdownPreview && currentFormat === 'markdown'"
        class="markdown-preview"
        v-html="renderedMarkdown"
      ></div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, computed } from 'vue'
import { Message } from '@arco-design/web-vue'
import { IconRefresh, IconCodeBlock, IconCopy, IconEye, IconEdit } from '@arco-design/web-vue/es/icon'
import MonacoEditor from '@guolao/vue-monaco-editor'
import * as yaml from 'js-yaml'

const props = defineProps({
  // 初始数据
  modelValue: {
    type: [Object, Array, String],
    required: true
  },
  // 初始格式
  initialFormat: {
    type: String,
    default: 'json',
    validator: (value) => ['json', 'yaml', 'markdown'].includes(value)
  },
  // 是否保留内部属性（以 _ 开头的属性）
  preserveInternalProps: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:modelValue', 'change', 'format-change'])

// Monaco 编辑器相关
const monacoEditor = ref(null)
const editorInstance = ref(null)

// 当前格式
const currentFormat = ref(props.initialFormat)

// 编辑器配置
const editorOptions = computed(() => ({
  automaticLayout: true,
  scrollBeyondLastLine: false,
  minimap: { enabled: true, showSlider: "mouseover" },
  lineNumbers: 'on',
  tabSize: 2,
  fontSize: 14,
  fontFamily: 'Fira Code, Monaco, Menlo, Consolas, monospace',
  renderLineHighlight: 'all',
  formatOnPaste: true,
  folding: currentFormat.value === 'markdown', // Markdown支持折叠
  foldingStrategy: currentFormat.value === 'markdown' ? 'auto' : 'never',
  matchBrackets: 'always',
  wordWrap: 'on',
  scrollbar: {
    useShadows: true,
    verticalScrollbarSize: 8,
    horizontalScrollbarSize: 8
  }
}))

// 编辑器状态
const editorContent = ref('')
const isValidContent = ref(true)
const statusMessage = ref('内容有效')

// 原始数据存储
const originalData = ref(null)

// Markdown 预览状态
const isMarkdownPreview = ref(false)

// Monaco 模型和验证相关
let jsonModelUri = null
let jsonModel = null
let jsonSeverity = null

// 监听输入变化并应用验证
const onEditorChange = (value) => {
  validateContent(value)
}

// Monaco 编辑器挂载完成
const onEditorMounted = (editor) => {
  console.log('[DEBUG] Monaco编辑器挂载完成，当前内容长度:', editorContent.value?.length || 0)
  editorInstance.value = editor
  
  // 设置语言特定配置
  editor.onDidFocusEditorWidget(() => {
    const monaco = window.monaco
    if (monaco) {
      if (currentFormat.value === 'json') {
        monaco.languages.json.jsonDefaults.setDiagnosticsOptions({
          validate: true,
          allowComments: false,
          schemas: []
        })
      }
    }
  })
  
  // 只有在编辑器完全为空且有初始数据时才初始化内容
  const hasUserContent = editorContent.value && editorContent.value.trim() !== '' && editorContent.value !== getDefaultContent()
  
  if (!hasUserContent && props.modelValue) {
    console.log('[DEBUG] 编辑器挂载时初始化内容')
    updateEditorContent()
  } else if (hasUserContent) {
    console.log('[DEBUG] 编辑器挂载时检测到用户内容，保留现有内容')
  }
}

// Markdown 渲染
const renderedMarkdown = computed(() => {
  if (currentFormat.value === 'markdown' && editorContent.value) {
    return renderMarkdown(editorContent.value)
  }
  return ''
})

// 简单的 Markdown 渲染器
const renderMarkdown = (markdown) => {
  // 先处理\n转义字符为实际换行
  let html = markdown.replace(/\\n/g, '\n')
  
  // 按行处理，确保标题标记在行首才生效
  const lines = html.split('\n')
  const processedLines = lines.map(line => {
    // 标题处理 - 必须在行首
    if (/^### /.test(line)) {
      return line.replace(/^### (.*)$/, '<h3>$1</h3>')
    } else if (/^## /.test(line)) {
      return line.replace(/^## (.*)$/, '<h2>$1</h2>')
    } else if (/^# /.test(line)) {
      return line.replace(/^# (.*)$/, '<h1>$1</h1>')
    }
    
    // 无序列表
    if (/^\s*[-*+]\s+/.test(line)) {
      return line.replace(/^\s*[-*+]\s+(.*)$/, '<li>$1</li>')
    }
    
    // 有序列表
    if (/^\s*\d+\.\s+/.test(line)) {
      return line.replace(/^\s*\d+\.\s+(.*)$/, '<li>$1</li>')
    }
    
    return line
  })
  
  html = processedLines.join('\n')
  
  // 其他格式处理
  html = html
    // 粗体
    .replace(/\*\*(.*?)\*\*/gim, '<strong>$1</strong>')
    // 斜体
    .replace(/\*(.*?)\*/gim, '<em>$1</em>')
    // 代码块
    .replace(/```([\s\S]*?)```/gim, '<pre><code>$1</code></pre>')
    // 行内代码
    .replace(/`(.*?)`/gim, '<code>$1</code>')
    // 链接
    .replace(/\[([^\]]+)\]\(([^\)]+)\)/gim, '<a href="$2" target="_blank">$1</a>')
    // 换行
    .replace(/\n/gim, '<br>')
  
  // 包装列表项
  html = html.replace(/(<li>.*<\/li>)/gims, '<ul>$1</ul>')
  
  return html
}

// 切换 Markdown 预览
const toggleMarkdownPreview = () => {
  isMarkdownPreview.value = !isMarkdownPreview.value
}

// 格式变化处理
const onFormatChange = (newFormat) => {
  console.log(`[DEBUG] onFormatChange 被调用 - 当前格式: ${currentFormat.value}, 新格式: ${newFormat}`)
  try {
    if (currentFormat.value === newFormat) {
      console.log(`[DEBUG] 格式相同，但仍然发射事件确保同步`)
      emit('format-change', newFormat)
      return
    }
    
    // 重置 Markdown 预览状态
    if (newFormat !== 'markdown') {
      isMarkdownPreview.value = false
    }
    
    // 只更新格式，保留当前编辑器内容
    currentFormat.value = newFormat
    
    // 重新验证当前内容是否符合新格式
    validateContent(editorContent.value)
    
    // 发射格式变化事件
    console.log(`[DEBUG] MultiFormatEditor 发射格式变化事件: ${newFormat}`)
    emit('format-change', newFormat)
    
    Message.success(`已切换到 ${newFormat.toUpperCase()} 格式，内容已保留`)
  } catch (error) {
    Message.error(`格式切换失败: ${error.message}`)
  }
}

// 监听 props 变化
watch(() => props.modelValue, (newVal, oldVal) => {
  console.log('[DEBUG] props.modelValue 变化，isEditing:', isEditing.value, '编辑器内容长度:', editorContent.value?.length || 0)
  
  // 只有在非编辑状态且编辑器完全为空时才更新内容
  const isEmpty = !editorContent.value || editorContent.value.trim() === ''
  const isDefaultContent = editorContent.value === getDefaultContent()
  
  if (newVal && !isEditing.value && (isEmpty || isDefaultContent)) {
    console.log('[DEBUG] props变化触发内容更新')
    updateEditorContent()
  } else if (newVal && !isEditing.value) {
    console.log('[DEBUG] props变化但编辑器有用户内容，跳过更新')
  }
}, { deep: true })

// 监听初始格式变化
watch(() => props.initialFormat, (newFormat, oldFormat) => {
  if (newFormat && newFormat !== currentFormat.value && oldFormat !== undefined) {
    console.log(`[DEBUG] props.initialFormat 变化: ${oldFormat} -> ${newFormat}`)
    currentFormat.value = newFormat
    // 不需要发射格式变化事件，避免循环调用
    // emit('format-change', newFormat)
  }
})

// 监听当前格式变化
watch(currentFormat, (newFormat, oldFormat) => {
  console.log(`[DEBUG] currentFormat 变化: ${oldFormat} -> ${newFormat}`)
}, { immediate: true })

// 跟踪编辑状态，避免循环更新
const isEditing = ref(false)

// 更新编辑器内容
const updateEditorContent = () => {
  try {
    if (props.modelValue) {
      isEditing.value = true
      originalData.value = props.modelValue
      
      // 只有在编辑器完全为空或者是默认内容时才初始化内容
      const isEmpty = !editorContent.value || editorContent.value.trim() === ''
      const isDefaultContent = editorContent.value === getDefaultContent()
      
      if (isEmpty || isDefaultContent) {
        console.log('[DEBUG] 编辑器内容为空或默认内容，初始化数据')
        convertDataToFormat(props.modelValue, currentFormat.value)
      } else {
        console.log('[DEBUG] 编辑器有用户内容，跳过数据转换，保留用户输入')
      }
      
      // 延迟状态重置，避免与用户输入冲突
      setTimeout(() => {
        isEditing.value = false
        validateContent(editorContent.value)
      }, 100)
    } else {
      // 只有在没有用户内容时才设置默认内容
      if (!editorContent.value || editorContent.value.trim() === '') {
        editorContent.value = getDefaultContent()
      }
      isValidContent.value = true
      statusMessage.value = '内容有效'
    }
  } catch (error) {
    console.error('内容序列化错误:', error)
    isValidContent.value = false
    statusMessage.value = `序列化错误: ${error.message}`
  }
}

// 获取默认内容
const getDefaultContent = () => {
  switch (currentFormat.value) {
    case 'json':
      return '{}'
    case 'yaml':
      return '# YAML content\n'
    case 'markdown':
      return '# Markdown\n\n内容...'
    default:
      return ''
  }
}

// 将数据转换为指定格式
const convertDataToFormat = (data, format) => {
  console.log('[DEBUG] convertDataToFormat 被调用，当前编辑器内容长度:', editorContent.value?.length || 0)
  
  // 检查是否有用户内容，如果有则不覆盖
  const hasUserContent = editorContent.value && 
                         editorContent.value.trim() !== '' && 
                         editorContent.value !== getDefaultContent()
  
  if (hasUserContent) {
    console.log('[DEBUG] 检测到用户内容，跳过数据转换以保留用户输入')
    return
  }
  
  console.log('[DEBUG] 编辑器为空或默认内容，执行数据转换')
  
  try {
    switch (format) {
      case 'json':
        if (typeof data === 'string') {
          // 如果是字符串，尝试解析后重新格式化
          try {
            const parsed = JSON.parse(data)
            editorContent.value = JSON.stringify(parsed, null, 2)
          } catch {
            editorContent.value = data
          }
        } else {
          const cleanData = JSON.parse(JSON.stringify(data))
          editorContent.value = JSON.stringify(cleanData, null, 2)
        }
        break
      case 'yaml':
        if (typeof data === 'string') {
          editorContent.value = data
        } else {
          editorContent.value = yaml.dump(data, { indent: 2 })
        }
        break
      case 'markdown':
        if (typeof data === 'string') {
          editorContent.value = data
        } else {
          // 将对象转换为Markdown格式
          editorContent.value = objectToMarkdown(data)
        }
        break
    }
    console.log('[DEBUG] 数据转换完成，新内容长度:', editorContent.value?.length || 0)
  } catch (error) {
    console.error('格式转换错误:', error)
    editorContent.value = String(data)
  }
}

// 对象转Markdown
const objectToMarkdown = (obj) => {
  let markdown = '# 数据\n\n'
  
  const processValue = (value, level = 0) => {
    const indent = '  '.repeat(level)
    
    if (typeof value === 'object' && value !== null) {
      if (Array.isArray(value)) {
        return value.map(item => `${indent}- ${processValue(item, level + 1)}`).join('\n')
      } else {
        return Object.entries(value)
          .map(([key, val]) => `${indent}**${key}**: ${processValue(val, level + 1)}`)
          .join('\n')
      }
    }
    return String(value)
  }
  
  markdown += processValue(obj)
  return markdown
}

// 验证内容
const validateContent = (content) => {
  try {
    if (!content || content.trim() === '') {
      isValidContent.value = false
      statusMessage.value = '内容不能为空'
      return
    }
    
    switch (currentFormat.value) {
      case 'json':
        JSON.parse(content)
        isValidContent.value = true
        statusMessage.value = 'JSON 有效'
        break
      case 'yaml':
        yaml.load(content)
        isValidContent.value = true
        statusMessage.value = 'YAML 有效'
        break
      case 'markdown':
        // Markdown 通常不需要严格验证
        isValidContent.value = true
        statusMessage.value = 'Markdown 有效'
        break
      default:
        isValidContent.value = true
        statusMessage.value = '内容有效'
    }
  } catch (error) {
    isValidContent.value = false
    statusMessage.value = `${currentFormat.value.toUpperCase()} 错误: ${error.message}`
  }
}

// 解析当前内容
const parseCurrentContent = () => {
  try {
    if (!editorContent.value || editorContent.value.trim() === '') {
      return null
    }
    
    switch (currentFormat.value) {
      case 'json':
        return JSON.parse(editorContent.value)
      case 'yaml':
        return yaml.load(editorContent.value)
      case 'markdown':
        return editorContent.value
      default:
        return editorContent.value
    }
  } catch (error) {
    console.error('解析内容失败:', error)
    return null
  }
}



// 重置编辑器
const resetEditor = () => {
  updateEditorContent()
  Message.info('内容已重置')
}

// 格式化内容（包含去转义功能）
const formatContent = () => {
  if (!editorContent.value || editorContent.value.trim() === '') {
    Message.warning('内容不能为空')
    return
  }
  
  try {
    // 先尝试去除转义符
    let processedContent = editorContent.value
      .replace(/\\n/g, '\n')     // 将 \n 转换为实际换行符
      .replace(/\\t/g, '\t')     // 将 \t 转换为实际制表符
      .replace(/\\r/g, '\r')     // 将 \r 转换为实际回车符
      .replace(/\\"/g, '"')     // 将 \" 转换为实际双引号
      .replace(/\\'/g, "'")     // 将 \' 转换为实际单引号
      .replace(/\\\\/g, '\\')   // 将 \\\\ 转换为实际反斜杠
    
    // 更新编辑器内容
    editorContent.value = processedContent
    
    // 验证处理后的内容
    validateContent(processedContent)
    
    // 如果内容有效，则进行格式化
    if (isValidContent.value) {
      switch (currentFormat.value) {
        case 'json':
          const parsed = JSON.parse(processedContent)
          editorContent.value = JSON.stringify(parsed, null, 2)
          break
        case 'yaml':
          const yamlData = yaml.load(processedContent)
          editorContent.value = yaml.dump(yamlData, { indent: 2 })
          break
      }
      
      // 使用Monaco编辑器的格式化功能
      if (editorInstance.value) {
        setTimeout(() => {
          editorInstance.value.getAction('editor.action.formatDocument').run()
        }, 100)
      }
      
      Message.success(`${currentFormat.value.toUpperCase()} 已格式化`)
    } else {
      Message.success('已去除转义符，但内容格式无效，无法进一步格式化')
    }
  } catch (error) {
    Message.error(`格式化失败: ${error.message}`)
  }
}







// 复制到剪贴板 - 兼容HTTP环境
const copyToClipboard = async () => {
  try {
    // 优先使用现代 Clipboard API（HTTPS/localhost）
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(editorContent.value)
      Message.success('JSON 已复制到剪贴板')
    } else {
      // 降级方案：使用传统方法（HTTP环境）
      const textArea = document.createElement('textarea')
      textArea.value = editorContent.value
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()

      try {
        const successful = document.execCommand('copy')
        if (successful) {
          Message.success('JSON 已复制到剪贴板')
        } else {
          throw new Error('execCommand failed')
        }
      } catch (fallbackErr) {
        Message.info('请手动复制 JSON 内容')
        console.error('复制失败，请手动复制:', fallbackErr)
      } finally {
        document.body.removeChild(textArea)
      }
    }
  } catch (err) {
    Message.error('复制失败: ' + err.message)
    console.error('复制失败:', err)
  }
}

// 初始化
onMounted(() => {
  console.log('Monaco JSON Editor mounted, modelValue:', props.modelValue)
  // 发射初始格式变化事件，确保父组件同步
  console.log(`[DEBUG] MultiFormatEditor 挂载时发射初始格式事件: ${currentFormat.value}`)
  emit('format-change', currentFormat.value)
})
</script>

<style scoped>
.multi-format-editor-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 12px;
  padding: 8px;
  background-color: #fafafa;
  border-radius: 6px;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  flex-wrap: wrap;
  gap: 8px;
}

.content-status {
  font-size: 13px;
  color: #666;
  display: flex;
  align-items: center;
  padding: 0 8px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.error-dot {
  background-color: #f5222d;
}

.valid-dot {
  background-color: #52c41a;
}

.content-error {
  color: #f5222d;
}

.content-valid {
  color: #52c41a;
}

.editor-wrapper {
  flex: 1;
  overflow: visible;
  border-radius: 4px;
  border: 1px solid #e5e5e5;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  min-height: 400px;
}

/* 编辑器高度 */
:deep(.monaco-editor) {
  height: 100% !important;
}

:deep(.monaco-editor .margin) {
  background-color: #f5f5f5;
}

/* Markdown 预览样式 */
.markdown-preview {
  height: 100%;
  min-height: 400px;
  padding: 16px;
  background-color: #ffffff;
  overflow-y: auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  box-sizing: border-box;
}

.markdown-preview h1 {
  font-size: 2em;
  font-weight: 600;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #eee;
}

.markdown-preview h2 {
  font-size: 1.5em;
  font-weight: 600;
  margin: 24px 0 16px 0;
  padding-bottom: 6px;
  border-bottom: 1px solid #eee;
}

.markdown-preview h3 {
  font-size: 1.25em;
  font-weight: 600;
  margin: 20px 0 12px 0;
}

.markdown-preview p {
  margin: 0 0 16px 0;
}

.markdown-preview code {
  background-color: #f6f8fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Fira Code', Monaco, Menlo, Consolas, monospace;
  font-size: 0.9em;
}

.markdown-preview pre {
  background-color: #f6f8fa;
  padding: 16px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 0 0 16px 0;
}

.markdown-preview pre code {
  background: none;
  padding: 0;
  font-size: 0.85em;
}

.markdown-preview ul {
  margin: 0 0 16px 0;
  padding-left: 24px;
}

.markdown-preview li {
  margin: 4px 0;
}

.markdown-preview a {
  color: #0366d6;
  text-decoration: none;
}

.markdown-preview a:hover {
  text-decoration: underline;
}

.markdown-preview strong {
  font-weight: 600;
}

.markdown-preview em {
  font-style: italic;
}
</style>