<template>
  <BaseNodeEditor 
    title="编辑 Answer 节点" 
    :tabs="tabs" 
    v-model:currentTab="currentTab"
    @save="saveChanges" 
    @close="closeEditor"
  >
    <!-- 基本信息 Tab -->
    <template #basic>
      <div class="form-group">
        <label for="title">标题</label>
        <a-input type="text" id="title" v-model="nodeData.title" placeholder="输入节点标题" allow-clear />
      </div>
      
      <div class="form-group">
        <label for="desc">描述</label>
        <textarea id="desc" v-model="nodeData.desc" placeholder="输入节点描述"></textarea>
      </div>
    </template>
    
    <!-- 答案内容 Tab -->
    <template #answer>
      <div class="form-card">
        <div class="form-group">
          <label for="answer">答案内容</label>
          <div class="answer-input-wrapper">
            <textarea 
              id="answer" 
              v-model="nodeData.answer" 
              placeholder="输入答案内容" 
              rows="8"
              class="answer-input"
            ></textarea>
          </div>
        </div>
        
        <div class="form-group checkbox-group">
          <a-checkbox id="intermediate" v-model="nodeData.intermediate">中间节点（不是最终答案）</a-checkbox>
        </div>
        
        <!-- 变量语法帮助 -->
        <div class="variable-helper">
          <div class="variable-helper-header">
            <h4>变量语法帮助</h4>
            <button class="toggle-btn" @click="showVariableHelper = !showVariableHelper">
              {{ showVariableHelper ? '收起' : '展开' }}
            </button>
          </div>
          
          <div v-if="showVariableHelper" class="variable-helper-content">
            <p>在答案内容中，您可以使用以下语法引用其他节点的变量：</p>
            <code v-html="variableSyntaxHtml"></code>
            
            <div class="variable-examples">
              <h5>示例：</h5>
              <ul>
                <li>引用节点ID的文本: <code v-html="example1Html"></code></li>
                <li>引用LLM节点的选择: <code v-html="example2Html"></code></li>
                <li>引用Method节点的结果: <code v-html="example3Html"></code></li>
                <li>引用系统变量: <code v-html="example4Html"></code></li>
                <li>引用日期信息: <code v-html="example5Html"></code></li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </template>
    
    <!-- 变量配置 Tab -->
    <template #variables>
      <div class="variables-section">
        <div class="section-header">
          <h4>输出变量</h4>
          <div class="variable-actions">
            <a-input v-model="newVariableName" placeholder="变量名称" allow-clear />
            <button class="add-btn" @click="addVariable" :disabled="!newVariableName">添加变量</button>
          </div>
        </div>
        
        <div v-if="nodeData.variables.length === 0" class="no-variables">
          暂无变量，点击上方按钮添加
        </div>
        
        <div v-else class="variable-list">
          <div v-for="(variable, index) in nodeData.variables" :key="index" class="variable-item">
            <div class="variable-name">{{ variable }}</div>
            <button class="remove-btn" @click="removeVariable(index)">删除</button>
          </div>
        </div>
      </div>
    </template>
    
    <!-- 数据预览 Tab -->
    <template #preview>
      <JsonEditor ref="jsonEditorRef" v-model="nodeData" @change="onJsonEditorChange" />
    </template>
  </BaseNodeEditor>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import BaseNodeEditor from './BaseNodeEditor.vue'
import JsonEditor from './common/JsonEditor.vue'
import { Message } from '@arco-design/web-vue'
import { IconCheck, IconRefresh, IconCodeBlock, IconCopy } from '@arco-design/web-vue/es/icon'

const props = defineProps({
  node: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['save', 'close'])

// 节点数据
const nodeData = reactive(JSON.parse(JSON.stringify(props.node.data)))

// 变量语法示例（使用计算属性避免模板解析错误）
const variableSyntax = "{{#nodeId.variableName#}}"
const variableSyntaxHtml = "&#123;&#123;#nodeId.variableName#&#125;&#125;"
const exampleSyntaxHtml = "&#123;&#123;#1234567890.text#&#125;&#125;"
const example1Html = "&#123;&#123;#nodeId.text#&#125;&#125;"
const example2Html = "&#123;&#123;#nodeId.choices[0].message.content#&#125;&#125;"
const example3Html = "&#123;&#123;#nodeId.result#&#125;&#125;"
const example4Html = "&#123;&#123;#sys.query#&#125;&#125;"
const example5Html = "&#123;&#123;#sys.current_date_info#&#125;&#125;"

// 选项卡
const tabs = [
  { id: 'basic', label: '基本信息' },
  { id: 'answer', label: '答案内容' },
  { id: 'variables', label: '变量配置' },
  { id: 'preview', label: 'JSON 编辑' }
]

const currentTab = ref('basic')
const showVariableHelper = ref(false)
const newVariableName = ref('')
const jsonEditorRef = ref(null)

// 监听 props.node 变化，重新初始化数据
watch(() => props.node, () => {
  Object.assign(nodeData, JSON.parse(JSON.stringify(props.node.data)))
  initializeNodeData()
}, { deep: true })

// 监听 nodeData 变化，更新 JSON 编辑器内容
watch(nodeData, () => {
  // JSON编辑器内容更新已由JsonEditor组件处理
}, { deep: true })

// 初始化节点数据
onMounted(() => {
  initializeNodeData()
})

// 初始化节点数据
const initializeNodeData = () => {
  console.log('初始化 Answer 节点数据:', nodeData)
  
  // 确保基本字段存在
  if (!nodeData.title) nodeData.title = ''
  if (!nodeData.desc) nodeData.desc = ''
  if (!nodeData.type) nodeData.type = 'answer'
  
  // 确保 answer 字段存在
  if (!nodeData.answer) nodeData.answer = ''
  
  // 确保 intermediate 字段存在
  if (nodeData.intermediate === undefined) nodeData.intermediate = false
  
  // 确保 variables 数组存在
  if (!Array.isArray(nodeData.variables)) {
    nodeData.variables = []
  }
}

// 变量相关方法
const addVariable = () => {
  if (newVariableName.value && !nodeData.variables.includes(newVariableName.value)) {
    nodeData.variables.push(newVariableName.value)
    newVariableName.value = ''
  }
}

const removeVariable = (index) => {
  nodeData.variables.splice(index, 1)
}

// 处理 JsonEditor 的 change 事件
const onJsonEditorChange = (updatedData) => {
  // 深拷贝更新的数据
  const newData = JSON.parse(JSON.stringify(updatedData))

  // 保存内部属性
  const internalProps = {}
  Object.keys(nodeData).forEach(key => {
    if (key.startsWith('_')) {
      internalProps[key] = nodeData[key]
    }
  })

  // 清空当前数据（除了内部属性）
  Object.keys(nodeData).forEach(key => {
    if (!key.startsWith('_')) {
      delete nodeData[key]
    }
  })

  // 合并新数据
  Object.assign(nodeData, newData)

  // 恢复内部属性
  Object.assign(nodeData, internalProps)

  // 重新初始化节点数据以确保所有必要的字段都存在
  initializeNodeData()

  console.log('JsonEditor 更改已应用:', nodeData)
}

// 保存更改
const saveChanges = () => {
  // 如果当前在JSON编辑标签页，先自动应用JSON编辑器的更改
  if (currentTab.value === 'preview') {
    try {
      if (jsonEditorRef.value && jsonEditorRef.value.applyChanges) {
        jsonEditorRef.value.applyChanges()
        console.log('自动应用JSON编辑器更改成功')
      }
    } catch (error) {
      console.warn('自动应用JSON更改失败:', error)
    }
  }
  
  // 确保数据完整性
  initializeNodeData()

  emit('save', {
    id: props.node.id,
    data: { ...nodeData }
  })
}

// 关闭编辑器
const closeEditor = () => {
  emit('close')
}
</script>

<style scoped>
/* 表单样式 */
.form-group {
  margin-bottom: 20px;
}

/* 覆盖 Arco Input 样式 */
:deep(.arco-input-wrapper) {
  background-color: #fff !important;
}

:deep(.arco-input) {
  background-color: #fff !important;
}

:deep(.arco-input-number) {
  background-color: #fff !important;
}

:deep(.arco-input-number-input) {
  background-color: #fff !important;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

input[type="text"],
textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
}

input[type="text"]:focus,
textarea:focus {
  border-color: #2196f3;
  outline: none;
}

.form-card {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkbox-group input[type="checkbox"] {
  margin: 0;
}

.answer-input-wrapper {
  position: relative;
}

.answer-input {
  min-height: 150px;
  resize: vertical;
  font-family: 'Courier New', monospace;
  line-height: 1.5;
}

.variable-helper {
  margin-top: 15px;
  border: 1px solid #eee;
  border-radius: 6px;
  overflow: hidden;
}

.variable-helper-header {
  background-color: #f5f5f5;
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.variable-helper-header h4 {
  margin: 0;
  font-size: 14px;
  color: #333;
}

.toggle-btn {
  background: none;
  border: none;
  color: #2196f3;
  cursor: pointer;
  font-size: 14px;
}

.variable-helper-content {
  padding: 15px;
  background-color: #fcfcfc;
  font-size: 14px;
}

.variable-helper-content p {
  margin: 0 0 10px;
}

.variable-helper-content code {
  display: inline-block;
  background-color: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  margin: 5px 0;
}

.variable-examples {
  margin-top: 15px;
  border-top: 1px solid #eee;
  padding-top: 15px;
}

.variable-examples h5 {
  margin: 0 0 10px;
  font-size: 14px;
}

.variable-examples ul {
  margin: 0;
  padding-left: 20px;
}

.variable-examples li {
  margin-bottom: 5px;
}

/* 变量配置样式 */
.variables-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h4 {
  margin: 0;
  color: #333;
}

.variable-actions {
  display: flex;
  gap: 10px;
}

.add-btn {
  padding: 8px 12px;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-btn:hover {
  background-color: #0b7dda;
}

.add-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.no-variables {
  padding: 20px;
  text-align: center;
  color: #666;
  background-color: #f9f9f9;
  border-radius: 6px;
}

.variable-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.variable-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.variable-name {
  font-family: 'Courier New', monospace;
  color: #333;
}

.remove-btn {
  background: none;
  border: none;
  color: #f44336;
  cursor: pointer;
  font-size: 14px;
  padding: 5px;
}

.remove-btn:hover {
  color: #d32f2f;
}
</style>