<template>
  <BaseNodeEditor 
    title="编辑结束节点" 
    :tabs="tabs" 
    v-model:currentTab="currentTab"
    @save="saveChanges" 
    @close="closeEditor"
  >
    <!-- 基本信息 Tab -->
    <template #basic>
      <div class="form-group">
        <label for="title">标题</label>
        <a-input type="text" id="title" v-model="nodeData.title" placeholder="输入节点标题" allow-clear />
      </div>
      
      <div class="form-group">
        <label for="desc">描述</label>
        <textarea id="desc" v-model="nodeData.desc" placeholder="输入节点描述"></textarea>
      </div>
    </template>
    
    <!-- 数据预览 Tab -->
    <template #preview>
      <JsonEditor v-model="fullNodeData" @change="onJsonEditorChange" />
    </template>
  </BaseNodeEditor>
</template>

<script setup>
import { ref, reactive, watch, onMounted, computed } from 'vue'
import BaseNodeEditor from './BaseNodeEditor.vue'
import JsonEditor from './common/JsonEditor.vue'

const props = defineProps({
  node: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['save', 'close'])

// 节点数据
const nodeData = reactive(JSON.parse(JSON.stringify(props.node.data)))

// 选项卡
const tabs = [
  { id: 'basic', label: '基本信息' },
  { id: 'preview', label: 'JSON 编辑' }
]

const currentTab = ref('basic')

// 添加计算属性，创建包含ID的完整节点对象
const fullNodeData = computed({
  get: () => {
    return {
      id: props.node.id,
      data: nodeData
    }
  },
  set: (newValue) => {
    // 如果更新了 ID 或 data，分别进行处理
    if (newValue.id) {
      // 更新节点ID将在保存时处理
      console.log('节点ID已更改:', newValue.id)
    }
    if (newValue.data) {
      // 更新节点数据
      Object.assign(nodeData, newValue.data)
    }
  }
})

// 监听 props.node 变化，重新初始化数据
watch(() => props.node, () => {
  Object.assign(nodeData, JSON.parse(JSON.stringify(props.node.data)))
  initializeNodeData()
}, { deep: true })

// 初始化节点数据
onMounted(() => {
  initializeNodeData()
})

// 初始化节点数据
const initializeNodeData = () => {
  console.log('初始化结束节点数据:', nodeData)
  
  // 确保基本字段存在
  if (!nodeData.title) nodeData.title = '结束'
  if (!nodeData.desc) nodeData.desc = ''
  if (!nodeData.type) nodeData.type = 'end'
}

// 处理 JsonEditor 的 change 事件
const onJsonEditorChange = (newData) => {
  // 这里可以根据需要处理 JsonEditor 的 change 事件
  console.log('JsonEditor 更改:', newData)
}

// 保存更改
const saveChanges = () => {
  emit('save', {
    id: fullNodeData.value.id, // 使用可能已更改的ID
    data: { ...nodeData }
  })
}

// 关闭编辑器
const closeEditor = () => {
  emit('close')
}
</script>

<style scoped>
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-weight: 500;
  margin-bottom: 8px;
  color: #333;
}

textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.5;
  min-height: 100px;
  resize: vertical;
}

textarea:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}
</style> 