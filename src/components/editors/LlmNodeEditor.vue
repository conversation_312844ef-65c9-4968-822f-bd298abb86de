<template>
  <BaseNodeEditor 
    title="编辑 LLM 节点" 
    :tabs="tabs" 
    v-model:currentTab="currentTab"
    @save="saveChanges" 
    @close="closeEditor"
  >
    <!-- 基本信息 Tab -->
    <template #basic>
      <div class="form-group">
        <label for="title">节点标题</label>
        <a-input type="text" id="title" v-model="nodeData.title" placeholder="输入节点标题" allow-clear />
      </div>
      
      <div class="form-group">
        <label for="desc">描述</label>
        <textarea id="desc" v-model="nodeData.desc" placeholder="输入节点描述"></textarea>
      </div>
    </template>
    
    <!-- 模型配置 Tab -->
    <template #model>
      <div class="form-card">
        <div class="form-group">
          <label>模型提供商</label>
          <a-select
            v-model="nodeData.model.provider"
            placeholder="选择模型提供商"
            allow-clear
            :loading="loadingModels"
          >
            <a-option v-for="provider in llmModels" :key="provider.name" :value="provider.name">
              {{ provider.name }}
            </a-option>
          </a-select>
        </div>
        
        <div class="form-group">
          <label>模型名称</label>
          <a-select
            v-model="nodeData.model.name"
            placeholder="选择模型名称"
            allow-clear
            :disabled="!nodeData.model.provider"
          >
            <a-option v-for="model in availableModels" :key="model" :value="model">
              {{ model }}
            </a-option>
          </a-select>
        </div>
        
        <div class="form-group">
          <label for="model-mode">模型模式</label>
          <select id="model-mode" v-model="nodeData.model.mode">
            <option value="chat">对话模式</option>
            <option value="completion">补全模式</option>
          </select>
        </div>
        
        <div class="form-group">
          <label>温度（{{ nodeData.model.completion_params.temperature }}）</label>
          <div class="temperature-slider">
            <div class="slider-with-value">
              <input 
                type="range" 
                v-model.number="nodeData.model.completion_params.temperature" 
                step="0.1" 
                min="0" 
                max="1"
              >
              <span class="temperature-value">{{ nodeData.model.completion_params.temperature }}</span>
            </div>
          </div>
        </div>
      </div>
    </template>
    
    <!-- 提示词模板 Tab -->
    <template #prompt>
      <div class="prompt-templates">
        <div v-for="(prompt, index) in nodeData.prompt_template" :key="prompt.id" class="prompt-item">
          <div class="prompt-header">
            <div class="prompt-role">
              <select v-model="prompt.role" class="form-control role-select">
                <option value="system">系统</option>
                <option value="user">用户</option>
                <option value="assistant">助手</option>
              </select>
            </div>
            <div class="prompt-actions">
              <button class="move-btn" @click="movePromptUp(index)" :disabled="index === 0" title="上移">↑</button>
              <button class="move-btn" @click="movePromptDown(index)" :disabled="index === nodeData.prompt_template.length - 1" title="下移">↓</button>
              <button class="remove-btn" @click="removePrompt(index)" :disabled="nodeData.prompt_template.length <= 1" title="删除">×</button>
            </div>
          </div>
          <div class="prompt-body">
            <div class="prompt-input-wrapper">
              <textarea
                v-model="prompt.text"
                class="form-control prompt-text"
                rows="5"
                placeholder="输入提示词内容..."
                :data-content="prompt.text"
              ></textarea>
              <button class="expand-btn" @click="openPromptModal(index, prompt.text)" title="放大编辑">
                <icon-fullscreen />
              </button>
            </div>
          </div>
        </div>
        <button class="add-prompt-btn" @click="addPrompt">
          <span class="add-icon">+</span> 添加提示词
        </button>
      </div>
    </template>
    
    <!-- 模型组 Tab -->
    <template #modelGroup>
      <div class="model-groups">
        <div class="section-header">
          <h4>模型组配置</h4>
          <div class="group-actions">
            <a-input v-model="newGroupName" placeholder="输入组名" allow-clear />
            <button class="add-btn" @click="addModelGroup" :disabled="!newGroupName">添加组</button>
          </div>
        </div>
        
        <div v-for="(group, groupName) in nodeData.model_group" :key="groupName" class="model-group">
          <div class="group-header">
            <h5>{{ groupName }}</h5>
            <button class="remove-btn" @click="removeModelGroup(groupName)">删除</button>
          </div>
          
          <div class="group-content">
            <div class="accordion-item">
              <div class="accordion-header" @click="toggleGroupSection(groupName, 'model')">
                <span>模型配置</span>
                <span class="toggle-icon">{{ expandedSections[`${groupName}-model`] ? '▼' : '▶' }}</span>
              </div>
              <div v-if="expandedSections[`${groupName}-model`]" class="accordion-body">
                <div class="form-group">
                  <label>模型提供商</label>
                  <a-select
                    v-model="group.model.provider"
                    placeholder="选择模型提供商"
                    allow-clear
                    :loading="loadingModels"
                  >
                    <a-option v-for="provider in llmModels" :key="provider.name" :value="provider.name">
                      {{ provider.name }}
                    </a-option>
                  </a-select>
                </div>
                <div class="form-group">
                  <label>模型名称</label>
                  <a-select
                    v-model="group.model.name"
                    placeholder="选择模型名称"
                    allow-clear
                    :disabled="!group.model.provider"
                  >
                    <a-option v-for="model in getAvailableModels(group.model.provider)" :key="model" :value="model">
                      {{ model }}
                    </a-option>
                  </a-select>
                </div>
                <div class="form-group">
                  <label>模型模式</label>
                  <select v-model="group.model.mode" class="form-control">
                    <option value="chat">对话模式</option>
                    <option value="completion">补全模式</option>
                  </select>
                </div>
                <div class="form-group">
                  <label>温度 ({{ group.model.completion_params.temperature }})</label>
                  <input 
                    v-model.number="group.model.completion_params.temperature" 
                    type="range" 
                    step="0.1" 
                    min="0" 
                    max="1" 
                    class="form-range"
                  >
                </div>
              </div>
            </div>
            
            <div class="accordion-item">
              <div class="accordion-header" @click="toggleGroupSection(groupName, 'prompt')">
                <span>提示词模板</span>
                <span class="toggle-icon">{{ expandedSections[`${groupName}-prompt`] ? '▼' : '▶' }}</span>
              </div>
              <div v-if="expandedSections[`${groupName}-prompt`]" class="accordion-body">
                <div class="prompt-templates">
                  <div v-for="(prompt, index) in group.prompt_template" :key="prompt.id" class="prompt-item">
                    <div class="prompt-header">
                      <div class="prompt-role">
                        <select v-model="prompt.role" class="form-control role-select">
                          <option value="system">系统</option>
                          <option value="user">用户</option>
                          <option value="assistant">助手</option>
                        </select>
                      </div>
                      <div class="prompt-actions">
                        <button class="move-btn" @click="moveGroupPromptUp(groupName, index)" :disabled="index === 0" title="上移">↑</button>
                        <button class="move-btn" @click="moveGroupPromptDown(groupName, index)" :disabled="index === group.prompt_template.length - 1" title="下移">↓</button>
                        <button class="remove-btn" @click="removeGroupPrompt(groupName, index)" :disabled="group.prompt_template.length <= 1" title="删除">×</button>
                      </div>
                    </div>
                    <div class="prompt-body">
                      <div class="prompt-input-wrapper">
                        <textarea
                          v-model="prompt.text"
                          class="form-control prompt-text"
                          rows="5"
                          placeholder="输入提示词内容..."
                          :data-content="prompt.text"
                        ></textarea>
                        <button class="expand-btn" @click="openPromptModal(index, prompt.text)" title="放大编辑">
                          <icon-fullscreen />
                        </button>
                      </div>
                    </div>
                  </div>
                  <button class="add-prompt-btn" @click="addGroupPrompt(groupName)">
                    <span class="add-icon">+</span> 添加提示词
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    
    <!-- 数据预览 Tab -->
    <template #preview>
      <JsonEditor
        v-model="nodeData"
        @change="onJsonChange"
      />
    </template>
  </BaseNodeEditor>
  <a-modal
    v-model:visible="showPromptModal"
    title="编辑提示词"
    :footer="false"
    :closable="true"
    :mask-closable="false"
    :keyboard="false"
    width="900px"
    style="top: 5vh;"
    @cancel="cancelPromptEdit"
  >
    <div class="prompt-editor">
      <div class="editor-toolbar">
        <a-tooltip content="插入变量">
          <a-button type="text" size="small">
            <icon-code />
          </a-button>
        </a-tooltip>
        <a-tooltip content="预览">
          <a-button type="text" size="small">
            <icon-eye />
          </a-button>
        </a-tooltip>
        <div class="toolbar-right">
          <span class="keyboard-tips">
            Ctrl + Enter 保存
          </span>
        </div>
      </div>
      
      <div class="editor-main">
        <a-textarea
          v-model="editingPromptText"
          :auto-size="false"
          style="height: 60vh; font-size: 16px; line-height: 1.6;"
          placeholder="请输入提示词内容..."
          @keydown="handleKeydown"
        />
      </div>
      
      <div class="editor-footer">
        <div class="draft-status" v-if="isDirty">
          <icon-exclamation-circle-fill /> 有未保存的更改
        </div>
        <div class="footer-buttons">
          <a-button @click="cancelPromptEdit" style="margin-right: 12px;">
            取消
          </a-button>
          <a-button type="primary" @click="confirmPromptEdit">
            保存
          </a-button>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { v4 as uuidv4 } from 'uuid'
import BaseNodeEditor from './BaseNodeEditor.vue'
import JsonEditor from './common/JsonEditor.vue'
import { Message, Modal } from '@arco-design/web-vue'
import { IconCheck, IconRefresh, IconCodeBlock, IconCopy, IconFullscreen, IconEye, IconExclamationCircleFill } from '@arco-design/web-vue/es/icon'
import { getLlmModels } from '@/api/workflow'

const props = defineProps({
  node: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['save', 'close'])

// 创建节点数据的深拷贝
const nodeData = reactive(JSON.parse(JSON.stringify(props.node.data)))

// 选项卡配置
const tabs = [
  { id: 'basic', label: '基本信息' },
  { id: 'model', label: '模型配置' },
  { id: 'prompt', label: '提示词模板' },
  { id: 'modelGroup', label: '模型组' },
  { id: 'preview', label: 'JSON 编辑' }
]
const currentTab = ref('basic')

// 模型组相关状态
const newGroupName = ref('')
const expandedSections = reactive({})

// JSON 编辑器相关
const jsonEditorContent = ref('')
const isValidJson = ref(true)
const jsonStatusMessage = ref('JSON 有效')

// 模型列表相关状态
const llmModels = ref([])
const loadingModels = ref(false)

// 弹窗相关响应式变量
const showPromptModal = ref(false)
const editingPromptIndex = ref(null)
const editingPromptText = ref('')
const isDirty = ref(false)

// 获取模型列表
const fetchLlmModels = async () => {
  loadingModels.value = true
  try {
    const response = await getLlmModels()
    if (response.code === 200) {
      llmModels.value = response.data
    } else {
      Message.error('获取模型列表失败: ' + response.message)
    }
  } catch (error) {
    console.error('获取模型列表失败:', error)
    Message.error('获取模型列表失败: ' + error.message)
  } finally {
    loadingModels.value = false
  }
}

// 监听模型提供商变化，更新可选的模型列表
const availableModels = computed(() => {
  if (!nodeData.model.provider) return []
  const provider = llmModels.value.find(p => p.name === nodeData.model.provider)
  return provider ? provider.supportModels : []
})

// 监听 props.node 变化，重新初始化数据
watch(() => props.node, () => {
  Object.assign(nodeData, JSON.parse(JSON.stringify(props.node.data)))
  initializeNodeData()
}, { deep: true })

// 监听 nodeData 变化，更新 JSON 编辑器内容
watch(nodeData, () => {
  updateJsonEditorContent()
}, { deep: true })

// 初始化必要的对象结构
onMounted(() => {
  initializeNodeData()
  updateJsonEditorContent()
  fetchLlmModels()
})

// 初始化节点数据
const initializeNodeData = () => {
  console.log('初始化 LLM 节点数据:', nodeData)
  
  // 确保基本字段存在
  if (!nodeData.title) nodeData.title = ''
  if (!nodeData.desc) nodeData.desc = ''
  if (!nodeData.type) nodeData.type = 'llm'
  
  // 确保模型对象存在并包含所有必要字段
  if (!nodeData.model) {
    nodeData.model = {
      mode: 'chat',
      name: '',
      provider: '',
      completion_params: {
        temperature: 0.7
      }
    }
  } else {
    // 确保 completion_params 存在
    if (!nodeData.model.completion_params) {
      nodeData.model.completion_params = { temperature: 0.7 }
    }
    // 确保 mode 存在
    if (!nodeData.model.mode) {
      nodeData.model.mode = 'chat'
    }
  }
  
  // 确保提示词模板数组存在
  if (!Array.isArray(nodeData.prompt_template) || nodeData.prompt_template.length === 0) {
    nodeData.prompt_template = [{
      id: uuidv4(),
      role: 'system',
      text: ''
    }]
  } else {
    // 确保每个提示词都有 id
    nodeData.prompt_template.forEach(prompt => {
      if (!prompt.id) {
        prompt.id = uuidv4()
      }
    })
  }
  
  // 确保模型组对象存在
  if (!nodeData.model_group) {
    nodeData.model_group = {}
  } else {
    // 初始化每个模型组的折叠状态
    Object.keys(nodeData.model_group).forEach(groupName => {
      expandedSections[`${groupName}-model`] = true
      expandedSections[`${groupName}-prompt`] = true
      
      const group = nodeData.model_group[groupName]
      
      // 确保每个组的模型配置完整
      if (!group.model) {
        group.model = {
          mode: 'chat',
          name: '',
          provider: '',
          completion_params: {
            temperature: 0.7
          }
        }
      } else {
        // 确保 completion_params 存在
        if (!group.model.completion_params) {
          group.model.completion_params = { temperature: 0.7 }
        }
        // 确保 mode 存在
        if (!group.model.mode) {
          group.model.mode = 'chat'
        }
      }
      
      // 确保提示词模板数组存在
      if (!Array.isArray(group.prompt_template) || group.prompt_template.length === 0) {
        group.prompt_template = [{
          id: uuidv4(),
          role: 'system',
          text: ''
        }]
      } else {
        // 确保每个提示词都有 id
        group.prompt_template.forEach(prompt => {
          if (!prompt.id) {
            prompt.id = uuidv4()
          }
        })
      }
    })
  }
  
  // 初始化其他可能的字段（memory, vision, context 等）
  if (!nodeData.memory) {
    nodeData.memory = {
      window: { size: 40, enabled: true },
      truncation: {
        enabled: true,
        turn_configs: {},
        default_config: { max_prefix_length: 70, max_suffix_length: 80 }
      },
      role_prefix: { user: '', assistant: '' },
      query_prompt_template: ''
    }
  }
  
  if (!nodeData.vision) {
    nodeData.vision = { enabled: false }
  }
  
  if (!nodeData.context) {
    nodeData.context = { enabled: false, variable_selector: [] }
  }
}

// 提示词模板相关方法
const addPrompt = () => {
  nodeData.prompt_template.push({
    id: uuidv4(),
    role: 'user',
    text: ''
  })
}

const removePrompt = (index) => {
  if (nodeData.prompt_template.length > 1) {
    nodeData.prompt_template.splice(index, 1)
  }
}

const movePromptUp = (index) => {
  if (index > 0) {
    const temp = nodeData.prompt_template[index]
    nodeData.prompt_template[index] = nodeData.prompt_template[index - 1]
    nodeData.prompt_template[index - 1] = temp
  }
}

const movePromptDown = (index) => {
  if (index < nodeData.prompt_template.length - 1) {
    const temp = nodeData.prompt_template[index]
    nodeData.prompt_template[index] = nodeData.prompt_template[index + 1]
    nodeData.prompt_template[index + 1] = temp
  }
}

// 模型组相关方法
const addModelGroup = () => {
  if (newGroupName.value && !nodeData.model_group[newGroupName.value]) {
    nodeData.model_group[newGroupName.value] = {
      model: {
        mode: 'chat',
        name: '',
        provider: '',
        completion_params: {
          temperature: 0.7
        }
      },
      prompt_template: [{
        id: uuidv4(),
        role: 'system',
        text: ''
      }]
    }
    
    // 默认展开新添加的组
    expandedSections[`${newGroupName.value}-model`] = true
    expandedSections[`${newGroupName.value}-prompt`] = true
    
    // 清空输入
    newGroupName.value = ''
  }
}

const removeModelGroup = (groupName) => {
  delete nodeData.model_group[groupName]
  delete expandedSections[`${groupName}-model`]
  delete expandedSections[`${groupName}-prompt`]
}

// 折叠面板相关方法
const toggleGroupSection = (groupName, section) => {
  const key = `${groupName}-${section}`
  expandedSections[key] = !expandedSections[key]
}

// 模型组提示词相关方法
const addGroupPrompt = (groupName) => {
  nodeData.model_group[groupName].prompt_template.push({
    id: uuidv4(),
    role: 'user',
    text: ''
  })
}

const removeGroupPrompt = (groupName, index) => {
  const prompts = nodeData.model_group[groupName].prompt_template
  if (prompts.length > 1) {
    prompts.splice(index, 1)
  }
}

const moveGroupPromptUp = (groupName, index) => {
  const prompts = nodeData.model_group[groupName].prompt_template
  if (index > 0) {
    const temp = prompts[index]
    prompts[index] = prompts[index - 1]
    prompts[index - 1] = temp
  }
}

const moveGroupPromptDown = (groupName, index) => {
  const prompts = nodeData.model_group[groupName].prompt_template
  if (index < prompts.length - 1) {
    const temp = prompts[index]
    prompts[index] = prompts[index + 1]
    prompts[index + 1] = temp
  }
}

// 更新 JSON 编辑器内容
const updateJsonEditorContent = () => {
  try {
    // 创建一个干净的对象副本，避免循环引用和特殊对象
    const cleanData = cleanObjectForJson(nodeData)
    jsonEditorContent.value = JSON.stringify(cleanData, null, 2)
    validateJson()
  } catch (error) {
    console.error('JSON 序列化错误:', error)
    jsonEditorContent.value = '// 错误: 无法序列化数据\n// ' + error.message
    isValidJson.value = false
    jsonStatusMessage.value = `序列化错误: ${error.message}`
  }
}

// 清理对象，使其可以安全地转换为 JSON
const cleanObjectForJson = (obj) => {
  // 如果不是对象或为 null，直接返回
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  // 处理数组
  if (Array.isArray(obj)) {
    return obj.map(item => cleanObjectForJson(item))
  }
  
  // 创建一个新对象来存储清理后的属性
  const cleanObj = {}
  
  // 遍历对象的所有可枚举属性
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      try {
        const value = obj[key]
        
        // 跳过函数、Symbol 和其他不能序列化的类型
        if (typeof value === 'function' || typeof value === 'symbol') {
          continue
        }
        
        // 跳过以下划线开头的内部属性
        if (key.startsWith('_')) {
          continue
        }
        
        // 递归清理嵌套对象
        cleanObj[key] = cleanObjectForJson(value)
      } catch (error) {
        // 如果处理某个属性时出错，跳过该属性
        console.warn(`无法处理属性 "${key}":`, error)
        cleanObj[key] = `[无法序列化: ${error.message}]`
      }
    }
  }
  
  return cleanObj
}

// 验证 JSON
const validateJson = () => {
  try {
    if (jsonEditorContent.value.trim() === '') {
      isValidJson.value = false
      jsonStatusMessage.value = 'JSON 不能为空'
      return
    }
    
    JSON.parse(jsonEditorContent.value)
    isValidJson.value = true
    jsonStatusMessage.value = 'JSON 有效'
  } catch (error) {
    isValidJson.value = false
    jsonStatusMessage.value = `JSON 无效: ${error.message}`
  }
}

// 应用 JSON 更改
const applyJsonChanges = () => {
  if (!isValidJson.value) {
    Message.error('无法应用无效的 JSON')
    return
  }
  
  try {
    const updatedData = JSON.parse(jsonEditorContent.value)
    
    // 更新节点数据，保留内部属性
    const internalProps = {}
    
    // 保存所有以下划线开头的内部属性
    Object.keys(nodeData).forEach(key => {
      if (key.startsWith('_')) {
        internalProps[key] = nodeData[key]
      }
    })
    
    // 清空当前数据
    Object.keys(nodeData).forEach(key => {
      if (!key.startsWith('_')) { // 不删除内部属性
        delete nodeData[key]
      }
    })
    
    // 应用新数据
    Object.keys(updatedData).forEach(key => {
      if (!key.startsWith('_')) { // 不覆盖内部属性
        nodeData[key] = updatedData[key]
      }
    })
    
    // 恢复内部属性
    Object.keys(internalProps).forEach(key => {
      nodeData[key] = internalProps[key]
    })
    
    Message.success('JSON 更改已应用')
  } catch (error) {
    Message.error(`应用 JSON 失败: ${error.message}`)
  }
}

// 重置 JSON 编辑器
const resetJsonEditor = () => {
  updateJsonEditorContent()
  Message.info('JSON 已重置')
}

// 格式化 JSON
const formatJson = () => {
  if (!isValidJson.value) {
    Message.warning('无法格式化无效的 JSON')
    return
  }
  
  try {
    const parsed = JSON.parse(jsonEditorContent.value)
    jsonEditorContent.value = JSON.stringify(parsed, null, 2)
    Message.success('JSON 已格式化')
  } catch (error) {
    Message.error(`格式化失败: ${error.message}`)
  }
}

// 复制 JSON 到剪贴板 - 兼容HTTP环境
const copyJsonToClipboard = async () => {
  try {
    // 优先使用现代 Clipboard API（HTTPS/localhost）
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(jsonEditorContent.value)
      Message.success('JSON 已复制到剪贴板')
    } else {
      // 降级方案：使用传统方法（HTTP环境）
      const textArea = document.createElement('textarea')
      textArea.value = jsonEditorContent.value
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()

      try {
        const successful = document.execCommand('copy')
        if (successful) {
          Message.success('JSON 已复制到剪贴板')
        } else {
          throw new Error('execCommand failed')
        }
      } catch (fallbackErr) {
        Message.info('请手动复制 JSON 内容')
        console.error('复制失败，请手动复制:', fallbackErr)
      } finally {
        document.body.removeChild(textArea)
      }
    }
  } catch (err) {
    Message.error('复制失败: ' + err.message)
    console.error('复制失败:', err)
  }
}

// 保存更改
const saveChanges = () => {
  // 如果当前在 JSON 编辑标签页，先尝试应用更改
  if (currentTab.value === 'preview' && isValidJson.value) {
    applyJsonChanges()
  }
  
  emit('save', {
    id: props.node.id,
    data: nodeData
  })
}

// 关闭编辑器
const closeEditor = () => {
  emit('close')
}

// 添加处理换行符的方法
const processNewlines = (text) => {
  return text.replace(/\\n/g, '\n').replace(/\\n\\n/g, '\n\n')
}

// 监听提示词文本变化，处理换行符
watch(() => nodeData.prompt_template, (newPrompts) => {
  newPrompts.forEach(prompt => {
    if (prompt.text) {
      prompt.text = processNewlines(prompt.text)
    }
  })
}, { deep: true })

// 添加获取可用模型的方法
const getAvailableModels = (providerName) => {
  if (!providerName) return []
  const provider = llmModels.value.find(p => p.name === providerName)
  return provider ? provider.supportModels : []
}

// 打开弹窗
const openPromptModal = (index, text) => {
  editingPromptIndex.value = index
  editingPromptText.value = text || ''  // 确保 text 不是 undefined
  showPromptModal.value = true
  isDirty.value = false
}

// 监听内容变化，自动保存草稿
watch(editingPromptText, (newText) => {
  if (editingPromptIndex.value !== null) {
    const draftKey = `prompt_draft_${editingPromptIndex.value}`
    localStorage.setItem(draftKey, newText)
    isDirty.value = true
  }
})

// 修改 cancelPromptEdit 方法
const cancelPromptEdit = () => {
  editingPromptIndex.value = null  // 清空索引
  editingPromptText.value = ''     // 清空编辑内容
  showPromptModal.value = false    // 关闭弹窗
}

// 修改 confirmPromptEdit 方法
const confirmPromptEdit = () => {
  if (editingPromptIndex.value !== null && editingPromptText.value !== undefined) {
    // 直接更新对应索引的提示词内容
    nodeData.prompt_template[editingPromptIndex.value].text = editingPromptText.value
  }
  // 清理状态
  editingPromptIndex.value = null
  editingPromptText.value = ''
  showPromptModal.value = false
}

// 修改 handleKeydown 方法
const handleKeydown = (e) => {
  // 只处理 Ctrl+Enter 保存
  if (e.ctrlKey && e.key === 'Enter') {
    e.preventDefault()
    confirmPromptEdit()
  }
  // 阻止 ESC 键的所有行为
  if (e.key === 'Escape') {
    e.stopPropagation()
    e.preventDefault()
    return false
  }
}

// 高亮变量
const highlightVariables = (text) => {
  if (!text) return text
  // 将 {{xxx}} 格式的变量用 span 包裹并添加样式
  return text.replace(/\{\{([^}]+)\}\}/g, '<span class="variable-highlight">{{$1}}</span>')
}

// 处理 JSON 更改
const onJsonChange = (updatedData) => {
  // 深拷贝更新的数据
  const newData = JSON.parse(JSON.stringify(updatedData))
  
  // 保存内部属性
  const internalProps = {}
  Object.keys(nodeData).forEach(key => {
    if (key.startsWith('_')) {
      internalProps[key] = nodeData[key]
    }
  })
  
  // 清空当前数据（除了内部属性）
  Object.keys(nodeData).forEach(key => {
    if (!key.startsWith('_')) {
      delete nodeData[key]
    }
  })
  
  // 合并新数据
  Object.assign(nodeData, newData)
  
  // 恢复内部属性
  Object.assign(nodeData, internalProps)
  
  // 重新初始化节点数据以确保所有必要的字段都存在
  initializeNodeData()
}
</script>

<style scoped>
/* 表单样式 */
.form-group {
  margin-bottom: 20px;
}

/* 覆盖 Arco Input 样式 */
:deep(.arco-input-wrapper) {
  background-color: #fff !important;
}

:deep(.arco-input) {
  background-color: #fff !important;
}

:deep(.arco-input-number) {
  background-color: #fff !important;
}

:deep(.arco-input-number-input) {
  background-color: #fff !important;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

input[type="text"],
textarea,
select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
}

input[type="text"]:focus,
textarea:focus,
select:focus {
  border-color: #2196f3;
  outline: none;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

.form-card {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

/* 温度滑块样式 */
.temperature-slider {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.slider-with-value {
  display: flex;
  align-items: center;
  gap: 8px;
}

input[type="range"] {
  width: 100%;
  height: 8px;
  -webkit-appearance: none;
  appearance: none;
  background: #f0f0f0;
  border-radius: 4px;
  outline: none;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  background: #2196f3;
  border-radius: 50%;
  cursor: pointer;
}

input[type="range"]::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #2196f3;
  border-radius: 50%;
  cursor: pointer;
}

.temperature-value {
  width: 50px;
  text-align: center;
}

/* 提示词模板样式 */
.prompt-templates {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.prompt-item {
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.2s;
  margin-bottom: 20px;
}

.prompt-item:hover {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.prompt-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #eee;
}

.prompt-role {
  width: 120px;
}

.role-select {
  width: 120px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
  font-size: 14px;
  transition: border-color 0.2s;
}

.role-select:focus {
  border-color: #2196f3;
  outline: none;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

.prompt-actions {
  display: flex;
  gap: 8px;
}

.move-btn, .remove-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s;
}

.move-btn:hover, .remove-btn:hover {
  background-color: #e0e0e0;
  border-color: #999;
}

.move-btn:disabled, .remove-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #f5f5f5;
  border-color: #eee;
}

.prompt-body {
  padding: 15px;
}

.prompt-text {
  min-height: 200px;
  max-height: 400px;
  resize: vertical;
  font-family: 'Courier New', monospace;
  line-height: 1.6;
  font-size: 14px;
  white-space: pre-wrap !important;
  text-decoration: none !important;
  font-style: normal !important;
  word-break: break-word;
  overflow-wrap: break-word;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
  transition: border-color 0.2s;
}

.prompt-text:focus {
  border-color: #2196f3;
  outline: none;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

/* 添加换行符显示样式 */
.prompt-text::before {
  content: attr(data-content);
  white-space: pre-wrap;
}

/* 确保文本区域中的换行符正确显示 */
:deep(.arco-textarea) {
  white-space: pre-wrap !important;
  word-break: break-word;
  overflow-wrap: break-word;
}

.add-prompt-btn {
  width: 100%;
  padding: 15px;
  background-color: #f0f0f0;
  border: 1px dashed #ccc;
  border-radius: 4px;
  cursor: pointer;
  text-align: center;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 14px;
  margin-top: 10px;
}

.add-prompt-btn:hover {
  background-color: #e0e0e0;
  border-color: #999;
}

.add-icon {
  font-size: 18px;
  font-weight: bold;
}

/* 模型组样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h4 {
  margin: 0;
  color: #333;
}

.group-actions {
  display: flex;
  gap: 10px;
}

.add-btn {
  padding: 8px 12px;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-btn:hover {
  background-color: #0b7dda;
}

.add-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.model-group {
  border: 1px solid #eee;
  border-radius: 8px;
  margin-bottom: 20px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.group-header {
  background-color: #f5f5f5;
  padding: 12px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
}

.group-header h5 {
  margin: 0;
  color: #333;
}

.group-content {
  padding: 15px;
}

/* 折叠面板样式 */
.accordion-item {
  border: 1px solid #eee;
  border-radius: 6px;
  margin-bottom: 10px;
  overflow: hidden;
}

.accordion-header {
  background-color: #f5f5f5;
  padding: 12px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.accordion-header:hover {
  background-color: #efefef;
}

.toggle-icon {
  font-size: 12px;
  color: #666;
}

.accordion-body {
  padding: 15px;
  background-color: #fcfcfc;
}

/* 数据预览样式 */
.json-editor-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 10px;
}

.json-editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.json-status {
  font-size: 12px;
  color: var(--color-text-3);
}

.status-invalid {
  color: var(--color-danger-6);
  font-weight: 500;
}

.json-editor-wrapper {
  flex: 1;
  overflow: hidden;
  border-radius: 4px;
  border: 1px solid var(--color-border-2);
}

:deep(.arco-textarea) {
  font-family: 'Fira Code', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  resize: none;
  border: none;
  border-radius: 0;
  padding: 12px;
  text-decoration: none !important;
  text-underline-position: unset !important;
  text-decoration-style: none !important;
  font-style: normal !important;
  white-space: pre-wrap !important;
}

:deep(.arco-textarea:focus) {
  box-shadow: none;
}

/* 修复滚动条样式 */
:deep(.arco-textarea)::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

:deep(.arco-textarea)::-webkit-scrollbar-track {
  background: var(--color-fill-1);
  border-radius: 4px;
}

:deep(.arco-textarea)::-webkit-scrollbar-thumb {
  background-color: var(--color-fill-3);
  border-radius: 4px;
}

:deep(.arco-textarea)::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-fill-4);
}

/* 确保所有输入框中的下划线能正确显示 */
input,
textarea,
select {
  text-decoration: none !important;
  text-underline-position: unset !important;
  text-decoration-style: none !important;
  font-style: normal !important;
}

/* 添加选择器样式 */
:deep(.arco-select) {
  width: 100%;
}

:deep(.arco-select-view) {
  background-color: #fff !important;
}

:deep(.arco-select-option) {
  padding: 8px 12px;
}

/* 添加新样式 */
.prompt-input-wrapper {
  position: relative;
  display: flex;
}

.expand-btn {
  position: absolute;
  right: 8px;
  top: 8px;
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.expand-btn:hover {
  background: rgba(0, 0, 0, 0.05);
  color: #333;
}

.prompt-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.editor-toolbar {
  display: flex;
  align-items: center;
  padding: 8px;
  border-bottom: 1px solid #eee;
}

.toolbar-right {
  margin-left: auto;
}

.keyboard-tips {
  color: #999;
  font-size: 12px;
}

.editor-main {
  flex: 1;
  padding: 16px;
}

.editor-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-top: 1px solid #eee;
}

.draft-status {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #ff9800;
  font-size: 14px;
}

.footer-buttons {
  display: flex;
  align-items: center;
}

/* 变量高亮样式 */
:deep(.variable-highlight) {
  color: #2196f3;
  background: rgba(33, 150, 243, 0.1);
  padding: 0 2px;
  border-radius: 2px;
}

/* 编辑器内容样式 */
:deep(.arco-textarea) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
  line-height: 1.6;
  padding: 12px;
}
</style> 