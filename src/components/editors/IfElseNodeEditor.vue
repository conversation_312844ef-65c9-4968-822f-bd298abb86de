<template>
  <BaseNodeEditor 
    title="编辑条件判断节点" 
    :tabs="tabs" 
    v-model:currentTab="currentTab"
    @save="saveChanges" 
    @close="closeEditor"
    @removeEdges="removeEdges"
  >
    <!-- 基本信息 Tab -->
    <template #basic>
      <div class="form-group">
        <label>节点标题</label>
        <a-input v-model="nodeData.title" placeholder="输入节点标题" allow-clear />
      </div>
      <div class="form-group">
        <label>描述</label>
        <textarea v-model="nodeData.desc" class="form-control" rows="2"></textarea>
      </div>
    </template>
    
    <!-- 条件配置 Tab -->
    <template #conditions>
      <div class="conditions-section">
        <div class="section-header">
          <h4>条件分支配置</h4>
          <div class="case-actions">
            <button class="add-btn" @click="addCase">添加分支</button>
          </div>
        </div>
        
        <div v-if="!nodeData.cases || nodeData.cases.length === 0" class="no-cases">
          暂无条件分支，点击上方按钮添加
        </div>
        
        <div v-else class="case-list">
          <div v-for="(caseItem, caseIndex) in nodeData.cases" :key="caseItem.case_id" class="case-item">
            <div class="case-header">
              <div class="case-title">
                <span v-if="caseItem.case_id === 'true'" class="case-label true-case">默认分支</span>
                <span v-else class="case-label">分支 {{ caseIndex + 1 }}</span>
                <div class="case-id">ID: {{ caseItem.case_id }}</div>
              </div>
              <div class="case-actions">
                <button class="move-btn" @click="moveCaseUp(caseIndex)" :disabled="caseIndex === 0">↑</button>
                <button class="move-btn" @click="moveCaseDown(caseIndex)" :disabled="caseIndex === nodeData.cases.length - 1">↓</button>
                <button class="remove-btn" @click="removeCase(caseIndex)" :disabled="caseItem.case_id === 'true'">删除</button>
              </div>
            </div>
            
            <div class="logical-operator">
              <label>逻辑运算符:</label>
              <select v-model="caseItem.logical_operator" class="form-control">
                <option value="and">AND (所有条件都满足)</option>
                <option value="or">OR (任一条件满足)</option>
              </select>
            </div>
            
            <div class="conditions">
              <div class="conditions-header">
                <h5>条件列表</h5>
                <button class="add-btn" @click="addCondition(caseItem)">添加条件</button>
              </div>
              
              <div v-if="!caseItem.conditions || caseItem.conditions.length === 0" class="no-conditions">
                暂无条件，点击上方按钮添加
              </div>
              
              <div v-else class="condition-list">
                <div v-for="(condition, condIndex) in caseItem.conditions" :key="condition.id" class="condition-item">
                  <div class="condition-row">
                    <div class="condition-field">
                      <label>变量选择器</label>
                      <div class="selector-inputs">
                        <a-input v-model="condition.variable_selector[0]" placeholder="节点ID" allow-clear />
                        <a-input v-model="condition.variable_selector[1]" placeholder="变量名" allow-clear />
                      </div>
                    </div>
                    
                    <div class="condition-field">
                      <label>变量类型</label>
                      <select v-model="condition.varType" class="form-control">
                        <option value="string">字符串</option>
                        <option value="number">数字</option>
                        <option value="boolean">布尔值</option>
                      </select>
                    </div>
                  </div>
                  
                  <div class="condition-row">
                    <div class="condition-field">
                      <label>比较运算符</label>
                      <select v-model="condition.comparison_operator" class="form-control">
                        <option v-if="condition.varType === 'string'" value="equals">等于</option>
                        <option v-if="condition.varType === 'string'" value="not equals">不等于</option>
                        <option v-if="condition.varType === 'string'" value="contains">包含</option>
                        <option v-if="condition.varType === 'string'" value="not contains">不包含</option>
                        <option v-if="condition.varType === 'string'" value="empty">为空</option>
                        <option v-if="condition.varType === 'string'" value="not empty">不为空</option>
                        
                        <option v-if="condition.varType === 'number'" value="equals">等于</option>
                        <option v-if="condition.varType === 'number'" value="not equals">不等于</option>
                        <option v-if="condition.varType === 'number'" value="greater than">大于</option>
                        <option v-if="condition.varType === 'number'" value="less than">小于</option>
                        <option v-if="condition.varType === 'number'" value="greater or equal">大于等于</option>
                        <option v-if="condition.varType === 'number'" value="less or equal">小于等于</option>
                        
                        <option v-if="condition.varType === 'boolean'" value="is true">为真</option>
                        <option v-if="condition.varType === 'boolean'" value="is false">为假</option>
                      </select>
                    </div>
                    
                    <div v-if="!['empty', 'not empty', 'is true', 'is false'].includes(condition.comparison_operator)" class="condition-field">
                      <label>比较值</label>
                      <a-input v-model="condition.value" placeholder="输入比较值" allow-clear />
                    </div>
                  </div>
                  
                  <div class="condition-actions">
                    <button class="remove-btn" @click="removeCondition(caseItem, condIndex)">删除条件</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    
    <!-- 数据预览 Tab -->
    <template #preview>
      <JsonEditor v-model="nodeData" @change="onJsonEditorChange" />
    </template>
  </BaseNodeEditor>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import BaseNodeEditor from './BaseNodeEditor.vue'
import JsonEditor from './common/JsonEditor.vue'
import { v4 as uuidv4 } from 'uuid'
import { Message } from '@arco-design/web-vue'
import { IconCheck, IconRefresh, IconCodeBlock, IconCopy } from '@arco-design/web-vue/es/icon'

const props = defineProps({
  node: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['save', 'close', 'removeEdges'])

// 节点数据
const nodeData = reactive(JSON.parse(JSON.stringify(props.node.data)))

// 选项卡
const tabs = [
  { id: 'basic', label: '基本信息' },
  { id: 'conditions', label: '条件配置' },
  { id: 'preview', label: 'JSON 编辑' }
]

const currentTab = ref('basic')

// 监听 props.node 变化，重新初始化数据
watch(() => props.node, () => {
  Object.assign(nodeData, JSON.parse(JSON.stringify(props.node.data)))
  initializeNodeData()
}, { deep: true })

// 监听 nodeData 变化
watch(nodeData, () => {
  // JSON编辑器内容更新由JsonEditor组件处理
}, { deep: true })

// 初始化节点数据
onMounted(() => {
  initializeNodeData()
})

// 初始化节点数据
const initializeNodeData = () => {
  console.log('初始化条件判断节点数据:', nodeData)
  
  // 确保基本字段存在
  if (!nodeData.title) nodeData.title = ''
  if (!nodeData.desc) nodeData.desc = ''
  if (!nodeData.type) nodeData.type = 'if_else'
  
  // 确保 cases 字段存在
  if (!nodeData.cases || !Array.isArray(nodeData.cases) || nodeData.cases.length === 0) {
    nodeData.cases = []
    
    // 添加默认的 true 分支
    addDefaultCase()
  }
}

// 添加默认的 true 分支
const addDefaultCase = () => {
  const defaultCase = {
    id: 'true',
    case_id: 'true',
    conditions: [],
    logical_operator: 'and'
  }
  
  nodeData.cases.push(defaultCase)
}

// 添加新分支
const addCase = () => {
  const newCase = {
    case_id: uuidv4(),
    conditions: [],
    logical_operator: 'and'
  }
  
  nodeData.cases.push(newCase)
}

// 移动分支上移
const moveCaseUp = (index) => {
  if (index > 0) {
    const temp = nodeData.cases[index]
    nodeData.cases[index] = nodeData.cases[index - 1]
    nodeData.cases[index - 1] = temp
  }
}

// 移动分支下移
const moveCaseDown = (index) => {
  if (index < nodeData.cases.length - 1) {
    const temp = nodeData.cases[index]
    nodeData.cases[index] = nodeData.cases[index + 1]
    nodeData.cases[index + 1] = temp
  }
}

// 删除分支
const removeCase = (index) => {
  // 不允许删除 true 分支
  if (nodeData.cases[index].case_id === 'true') return
  
  // 获取要删除的分支的 case_id
  const caseId = nodeData.cases[index].case_id
  
  // 删除分支
  nodeData.cases.splice(index, 1)
  
  // 通知父组件删除相关的边
  emit('removeEdges', {
    nodeId: props.node.id,
    caseId: caseId
  })
}

// 添加条件
const addCondition = (caseItem) => {
  if (!caseItem.conditions) {
    caseItem.conditions = []
  }
  
  const newCondition = {
    id: uuidv4(),
    variable_selector: ['', ''],
    varType: 'string',
    comparison_operator: 'equals',
    value: ''
  }
  
  caseItem.conditions.push(newCondition)
}

// 删除条件
const removeCondition = (caseItem, condIndex) => {
  caseItem.conditions.splice(condIndex, 1)
}

// 保存更改
const saveChanges = () => {
  emit('save', {
    id: props.node.id,
    data: nodeData
  })
}

// 关闭编辑器
const closeEditor = () => {
  emit('close')
}

// 处理删除边的逻辑
const removeEdges = (event) => {
  // 实现删除边的逻辑
  console.log('删除边:', event)
}

// 处理 JSON 编辑器变化
const onJsonEditorChange = (newData) => {
  // 更新节点数据
  Object.assign(nodeData, newData)
}
</script>

<style scoped>
/* 保留原有的样式 */
.form-group {
  margin-bottom: 15px;
}

/* 覆盖 Arco Input 样式 */
:deep(.arco-input-wrapper) {
  background-color: #fff !important;
}

:deep(.arco-input) {
  background-color: #fff !important;
}

:deep(.arco-input-number) {
  background-color: #fff !important;
}

:deep(.arco-input-number-input) {
  background-color: #fff !important;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-control:focus {
  border-color: #2196f3;
  outline: none;
}

/* 条件分支样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h4 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.add-btn {
  padding: 6px 12px;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.add-btn:hover {
  background-color: #0b7dda;
}

.add-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.no-cases, .no-conditions {
  padding: 20px;
  text-align: center;
  color: #666;
  background-color: #f9f9f9;
  border-radius: 6px;
}

.case-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.case-item {
  background-color: #f5f5f5;
  border-radius: 6px;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.case-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.case-title {
  display: flex;
  flex-direction: column;
}

.case-label {
  font-weight: 500;
  font-size: 14px;
  color: #333;
}

.true-case {
  color: #4caf50;
}

.case-id {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.case-actions {
  display: flex;
  gap: 5px;
}

.move-btn, .remove-btn {
  padding: 4px 8px;
  background-color: #f0f0f0;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.move-btn:hover {
  background-color: #e0e0e0;
}

.remove-btn {
  color: #f44336;
}

.remove-btn:hover {
  background-color: #ffebee;
}

.move-btn:disabled, .remove-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.logical-operator {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.logical-operator label {
  font-weight: 500;
  color: #333;
  white-space: nowrap;
}

.conditions {
  background-color: white;
  border-radius: 4px;
  padding: 15px;
}

.conditions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.conditions-header h5 {
  margin: 0;
  font-size: 14px;
  color: #333;
}

.condition-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.condition-item {
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 15px;
  position: relative;
}

.condition-row {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.condition-field {
  flex: 1;
}

.condition-field label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
  font-size: 12px;
}

.selector-inputs {
  display: flex;
  gap: 10px;
}

.condition-actions {
  display: flex;
  justify-content: flex-end;
}
</style> 