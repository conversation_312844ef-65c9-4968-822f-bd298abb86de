<template>
  <BaseNodeEditor 
    title="编辑变量聚合节点" 
    :tabs="tabs" 
    v-model:currentTab="currentTab"
    @save="saveChanges" 
    @close="closeEditor"
  >
    <!-- 基本信息 Tab -->
    <template #basic>
      <div class="form-group">
        <label for="title">节点标题</label>
        <a-input type="text" id="title" v-model="nodeData.title" placeholder="输入节点标题" allow-clear />
      </div>
      
      <div class="form-group">
        <label for="desc">描述</label>
        <textarea id="desc" v-model="nodeData.desc" placeholder="输入节点描述"></textarea>
      </div>
      
      <div class="form-group">
        <label for="output-type">输出类型</label>
        <select id="output-type" v-model="nodeData.output_type">
          <option value="string">字符串</option>
          <option value="number">数字</option>
          <option value="boolean">布尔值</option>
          <option value="object">对象</option>
          <option value="array">数组</option>
          <option value="any">任意类型</option>
        </select>
      </div>
    </template>
    
    <!-- 变量配置 Tab -->
    <template #variables>
      <div class="variables-section">
        <div class="section-header">
          <h4>变量优先级配置</h4>
          <div class="variable-actions">
            <button class="add-btn" @click="addVariable">添加变量</button>
          </div>
        </div>
        
        <div v-if="!nodeData.variables || nodeData.variables.length === 0" class="no-variables">
          暂无变量，点击上方按钮添加
        </div>
        
        <div v-else class="variable-list">
          <div v-for="(variable, index) in nodeData.variables" :key="index" class="variable-item">
            <div class="variable-content">
              <div class="variable-row">
                <label>优先级</label>
                <a-input-number v-model="variable.priority" :min="1" class="priority-input" />
              </div>
              <div class="variable-row">
                <label>选择器</label>
                <div class="selector-inputs">
                  <a-input v-model="variable.selector[0]" placeholder="节点ID" allow-clear />
                  <a-input v-model="variable.selector[1]" placeholder="变量名" allow-clear />
                </div>
              </div>
            </div>
            <div class="variable-actions">
              <button class="move-btn" @click="moveVariableUp(index)" :disabled="index === 0">↑</button>
              <button class="move-btn" @click="moveVariableDown(index)" :disabled="index === nodeData.variables.length - 1">↓</button>
              <button class="remove-btn" @click="removeVariable(index)">删除</button>
            </div>
          </div>
        </div>
      </div>
    </template>
    
    <!-- 高级设置 Tab -->
    <template #advanced>
      <div class="form-group">
        <div class="checkbox-group">
          <a-checkbox id="group-enabled" v-model="nodeData.advanced_settings.group_enabled">启用分组</a-checkbox>
        </div>
      </div>
      
      <div v-if="nodeData.advanced_settings.group_enabled" class="groups-section">
        <div class="section-header">
          <h4>变量分组</h4>
          <div class="group-actions">
            <button class="add-btn" @click="addGroup">添加分组</button>
          </div>
        </div>
        
        <div v-if="!nodeData.advanced_settings.groups || nodeData.advanced_settings.groups.length === 0" class="no-groups">
          暂无分组，点击上方按钮添加
        </div>
        
        <div v-else class="group-list">
          <div v-for="(group, groupIndex) in nodeData.advanced_settings.groups" :key="group.groupId" class="group-item">
            <div class="group-header">
              <div class="group-title-row">
                <a-input v-model="group.group_name" placeholder="分组名称" allow-clear />
                <select v-model="group.output_type" class="form-control">
                  <option value="string">字符串</option>
                  <option value="number">数字</option>
                  <option value="boolean">布尔值</option>
                  <option value="object">对象</option>
                  <option value="array">数组</option>
                  <option value="any">任意类型</option>
                </select>
                <button class="remove-btn" @click="removeGroup(groupIndex)">删除</button>
              </div>
            </div>
            
            <div class="group-variables">
              <div class="group-variables-header">
                <h5>分组变量</h5>
                <button class="add-btn" @click="addGroupVariable(group)">添加变量</button>
              </div>
              
              <div v-if="!group.variables || group.variables.length === 0" class="no-group-variables">
                暂无变量，点击上方按钮添加
              </div>
              
              <div v-else class="group-variable-list">
                <div v-for="(groupVar, varIndex) in group.variables" :key="varIndex" class="group-variable-item">
                  <div class="variable-row">
                    <label>优先级</label>
                    <a-input-number v-model="groupVar.priority" :min="1" class="priority-input" />
                  </div>
                  <div class="variable-row">
                    <label>选择器</label>
                    <div class="selector-inputs">
                      <a-input v-model="groupVar.selector[0]" placeholder="节点ID" allow-clear />
                      <a-input v-model="groupVar.selector[1]" placeholder="变量名" allow-clear />
                    </div>
                  </div>
                  <div class="group-variable-actions">
                    <button class="remove-btn" @click="removeGroupVariable(group, varIndex)">删除</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    
    <!-- 数据预览 Tab -->
    <template #preview>
      <JsonEditor v-model="nodeData" @change="onJsonEditorChange" />
    </template>
  </BaseNodeEditor>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import BaseNodeEditor from './BaseNodeEditor.vue'
import JsonEditor from './common/JsonEditor.vue'
import { Message } from '@arco-design/web-vue'
import { IconCheck, IconRefresh, IconCodeBlock, IconCopy } from '@arco-design/web-vue/es/icon'
import { v4 as uuidv4 } from 'uuid'

const props = defineProps({
  node: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['save', 'close'])

// 节点数据
const nodeData = reactive(JSON.parse(JSON.stringify(props.node.data)))

// 选项卡
const tabs = [
  { id: 'basic', label: '基本信息' },
  { id: 'variables', label: '变量配置' },
  { id: 'advanced', label: '高级设置' },
  { id: 'preview', label: 'JSON 编辑' }
]

const currentTab = ref('basic')

// 监听 props.node 变化，重新初始化数据
watch(() => props.node, () => {
  Object.assign(nodeData, JSON.parse(JSON.stringify(props.node.data)))
  initializeNodeData()
}, { deep: true })

// 监听 nodeData 变化
watch(nodeData, () => {
  // JSON编辑器内容更新由JsonEditor组件处理
}, { deep: true })

// 初始化节点数据
onMounted(() => {
  initializeNodeData()
})

// 初始化节点数据
const initializeNodeData = () => {
  // 确保基本字段存在
  if (!nodeData.title) nodeData.title = '变量聚合器'
  if (!nodeData.desc) nodeData.desc = ''
  if (!nodeData.output_type) nodeData.output_type = 'string'
  
  // 确保变量数组存在
  if (!nodeData.variables) nodeData.variables = []
  
  // 确保每个变量都有正确的结构
  nodeData.variables.forEach(variable => {
    if (!variable.priority) variable.priority = 1
    if (!variable.selector) variable.selector = ['', '']
    else if (!Array.isArray(variable.selector)) variable.selector = ['', '']
    else if (variable.selector.length < 2) {
      while (variable.selector.length < 2) variable.selector.push('')
    }
  })
  
  // 确保高级设置存在
  if (!nodeData.advanced_settings) {
    nodeData.advanced_settings = {
      groups: [],
      group_enabled: false
    }
  }
  
  // 确保分组数组存在
  if (!nodeData.advanced_settings.groups) nodeData.advanced_settings.groups = []
  
  // 确保分组启用标志存在
  if (nodeData.advanced_settings.group_enabled === undefined) {
    nodeData.advanced_settings.group_enabled = false
  }
  
  // 确保每个分组都有正确的结构
  nodeData.advanced_settings.groups.forEach(group => {
    if (!group.groupId) group.groupId = uuidv4()
    if (!group.group_name) group.group_name = '新分组'
    if (!group.output_type) group.output_type = 'any'
    if (!group.variables) group.variables = []
    
    // 确保分组中的每个变量都有正确的结构
    group.variables.forEach(variable => {
      if (!variable.priority) variable.priority = 1
      if (!variable.selector) variable.selector = ['', '']
      else if (!Array.isArray(variable.selector)) variable.selector = ['', '']
      else if (variable.selector.length < 2) {
        while (variable.selector.length < 2) variable.selector.push('')
      }
    })
  })
}

// 添加变量
const addVariable = () => {
  nodeData.variables.push({
    priority: nodeData.variables.length + 1,
    selector: ['', '']
  })
}

// 移除变量
const removeVariable = (index) => {
  nodeData.variables.splice(index, 1)
  
  // 重新排序优先级
  nodeData.variables.forEach((variable, idx) => {
    variable.priority = idx + 1
  })
}

// 上移变量
const moveVariableUp = (index) => {
  if (index > 0) {
    // 交换变量位置
    const temp = nodeData.variables[index]
    nodeData.variables[index] = nodeData.variables[index - 1]
    nodeData.variables[index - 1] = temp
    
    // 交换优先级
    const tempPriority = nodeData.variables[index].priority
    nodeData.variables[index].priority = nodeData.variables[index - 1].priority
    nodeData.variables[index - 1].priority = tempPriority
  }
}

// 下移变量
const moveVariableDown = (index) => {
  if (index < nodeData.variables.length - 1) {
    // 交换变量位置
    const temp = nodeData.variables[index]
    nodeData.variables[index] = nodeData.variables[index + 1]
    nodeData.variables[index + 1] = temp
    
    // 交换优先级
    const tempPriority = nodeData.variables[index].priority
    nodeData.variables[index].priority = nodeData.variables[index + 1].priority
    nodeData.variables[index + 1].priority = tempPriority
  }
}

// 添加分组
const addGroup = () => {
  nodeData.advanced_settings.groups.push({
    groupId: uuidv4(),
    group_name: `分组${nodeData.advanced_settings.groups.length + 1}`,
    output_type: 'any',
    variables: []
  })
}

// 移除分组
const removeGroup = (index) => {
  nodeData.advanced_settings.groups.splice(index, 1)
}

// 添加分组变量
const addGroupVariable = (group) => {
  if (!group.variables) group.variables = []
  
  group.variables.push({
    priority: group.variables.length + 1,
    selector: ['', '']
  })
}

// 移除分组变量
const removeGroupVariable = (group, index) => {
  group.variables.splice(index, 1)
  
  // 重新排序优先级
  group.variables.forEach((variable, idx) => {
    variable.priority = idx + 1
  })
}

// 保存更改
const saveChanges = () => {
  emit('save', {
    id: props.node.id,
    data: nodeData
  })
}

// 关闭编辑器
const closeEditor = () => {
  emit('close')
}

// 处理 JSON 编辑器变化
const onJsonEditorChange = (newData) => {
  // 更新节点数据
  Object.assign(nodeData, newData)
}
</script>

<style scoped>
/* 表单样式 */
.form-group {
  margin-bottom: 20px;
}

/* 覆盖 Arco Input 样式 */
:deep(.arco-input-wrapper) {
  background-color: #fff !important;
}

:deep(.arco-input) {
  background-color: #fff !important;
}

:deep(.arco-input-number) {
  background-color: #fff !important;
}

:deep(.arco-input-number-input) {
  background-color: #fff !important;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

input[type="text"],
input[type="number"],
textarea,
select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
}

input[type="text"]:focus,
input[type="number"]:focus,
textarea:focus,
select:focus {
  border-color: #2196f3;
  outline: none;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkbox-group input[type="checkbox"] {
  margin: 0;
}

/* 变量配置样式 */
.variables-section,
.groups-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h4 {
  margin: 0;
  color: #333;
}

.variable-actions,
.group-actions {
  display: flex;
  gap: 10px;
}

.add-btn {
  padding: 8px 12px;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-btn:hover {
  background-color: #0b7dda;
}

.add-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.no-variables,
.no-groups,
.no-group-variables {
  padding: 20px;
  text-align: center;
  color: #666;
  background-color: #f9f9f9;
  border-radius: 6px;
}

.variable-list,
.group-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.variable-item {
  display: flex;
  justify-content: space-between;
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 15px;
  gap: 15px;
}

.variable-content {
  flex-grow: 1;
}

.variable-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.variable-row:last-child {
  margin-bottom: 0;
}

.variable-row label {
  width: 80px;
  margin-bottom: 0;
}

.priority-input {
  width: 80px;
}

.selector-inputs {
  display: flex;
  gap: 10px;
  flex-grow: 1;
}

.variable-actions {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-left: 10px;
}

.move-btn {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.move-btn:hover {
  background-color: #e0e0e0;
}

.move-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.remove-btn {
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  padding: 5px 10px;
  transition: background-color 0.2s;
}

.remove-btn:hover {
  background-color: #d32f2f;
}

/* 分组样式 */
.group-item {
  flex-direction: column;
  background-color: #f5f5f5;
  border-radius: 6px;
  padding: 15px;
}

.group-header {
  width: 100%;
  margin-bottom: 15px;
}

.group-title-row {
  display: flex;
  gap: 10px;
  align-items: center;
}

.group-title-row .form-control {
  flex-grow: 1;
}

.group-title-row .remove-btn {
  flex-shrink: 0;
}

.group-variables {
  width: 100%;
  background-color: #fff;
  border-radius: 4px;
  padding: 15px;
}

.group-variables-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.group-variables-header h5 {
  margin: 0;
  font-size: 14px;
  color: #333;
}

.group-variable-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.group-variable-item {
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 10px;
  position: relative;
}

.group-variable-actions {
  position: absolute;
  top: 10px;
  right: 10px;
}
</style> 