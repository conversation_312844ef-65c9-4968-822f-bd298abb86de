<template>
  <div class="output-selector">
    <a-select
      v-model="selectedOutput"
      :placeholder="placeholder"
      allow-clear
      :disabled="isSelectionDisabled"
      style="width: 100%"
    >
      <a-option v-for="output in outputs" :key="output" :value="output">
        {{ output }}
      </a-option>
    </a-select>
    <div v-if="!nodeId" class="empty-tip">
      请先选择节点
    </div>
    <div v-else-if="outputs.length === 0" class="empty-tip">
      该节点没有可用输出
    </div>
    <div v-if="isSelfReference" class="warning-tip">
      <icon-exclamation-circle /> 警告: 节点引用自身可能导致循环依赖
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { getCurrentWorkflowFull, getWorkflowData } from '../../utils/storage'
import { IconExclamationCircle } from '@arco-design/web-vue/es/icon'
import { Message } from '@arco-design/web-vue'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  nodeId: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '选择输出变量'
  },
  currentNodeId: { // 当前正在编辑的节点ID
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue'])

const route = useRoute()
const allNodes = ref([])
const outputs = computed(() => getNodeOutputs(props.nodeId))
const selectedOutput = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 是否是自引用（节点引用自己）
const isSelfReference = computed(() => {
  return props.currentNodeId && props.nodeId === props.currentNodeId
})

onMounted(async () => {
  console.log('NodeOutputSelector组件挂载, 节点ID:', props.nodeId)
  const nodes = await fetchWorkflowNodes()
  console.log('初始化时获取到节点数量:', nodes.length, '输出变量数量:', outputs.value.length)
  
  // 初始化时检查节点类型和输出
  if (props.nodeId && !props.modelValue) {
    const node = allNodes.value.find(n => n.id === props.nodeId)
    if (node && node.data.type === 'llm') {
      // 如果是LLM节点，默认选择"text"
      console.log('LLM节点，默认选择text输出')
      emit('update:modelValue', 'text')
    } else if (node && node.data.type === 'answer') {
      // 如果是Answer节点，默认选择"answer"
      console.log('Answer节点，默认选择answer输出')
      emit('update:modelValue', 'answer')
    }
  }
})

// 获取工作流中的所有节点
const fetchWorkflowNodes = async () => {
  try {
    console.log('开始获取工作流节点数据...')
    const clientKey = route.query.clientKey || ''
    console.log('客户端Key:', clientKey)
    
    // 方法1：尝试从getCurrentWorkflowFull获取
    let workflowData = getCurrentWorkflowFull(clientKey)
    console.log('方法1 - getCurrentWorkflowFull获取结果:', workflowData ? '有数据' : '无数据')
    
    // 如果第一种方法失败，尝试方法2
    if (!workflowData || !workflowData.nodes || workflowData.nodes.length === 0) {
      console.log('尝试方法2 - 从getWorkflowData获取')
      workflowData = getWorkflowData(clientKey)
      if (workflowData) {
        console.log('方法2 - 成功获取数据')
      }
    }
    
    // 如果前两种方法都失败，尝试方法3
    if (!workflowData || !workflowData.nodes || workflowData.nodes.length === 0) {
      console.log('尝试方法3 - 直接从localStorage读取')
      // 直接从localStorage中读取数据
      const keys = ['current_workflow_full', `current_workflow_full_${clientKey}`, 'workflowData', `workflowData_${clientKey}`]
      
      for (const key of keys) {
        try {
          const raw = localStorage.getItem(key)
          if (raw) {
            const parsed = JSON.parse(raw)
            if (parsed && parsed.nodes && parsed.nodes.length > 0) {
              console.log(`从localStorage键'${key}'成功获取数据，节点数量:`, parsed.nodes.length)
              workflowData = parsed
              break
            }
          }
        } catch (e) {
          console.error(`解析localStorage键'${key}'失败:`, e)
        }
      }
    }
    
    // 最终检查是否有可用数据
    if (workflowData && workflowData.nodes && workflowData.nodes.length > 0) {
      console.log('成功获取工作流节点，数量:', workflowData.nodes.length)
      allNodes.value = workflowData.nodes
      return workflowData.nodes
    } else {
      // 如果尝试了所有方法仍然没有数据，显示提示
      console.error('无法获取工作流数据，所有方法均失败')
      Message.error('无法获取工作流数据，请确保当前页面已加载工作流')
      return []
    }
  } catch (error) {
    console.error('获取工作流节点时发生错误:', error)
    Message.error('获取节点数据失败: ' + error.message)
    return []
  }
}

// 获取节点的可用输出
const getNodeOutputs = (nodeId) => {
  if (!nodeId) {
    console.log('没有提供节点ID，无法获取输出')
    return []
  }
  
  const node = allNodes.value.find(n => n.id === nodeId)
  if (!node) {
    console.log(`未找到ID为${nodeId}的节点，无法获取输出`)
    return []
  }
  
  console.log(`获取节点(${nodeId})的输出变量, 节点类型:`, node.data.type)
  
  // 根据节点类型返回可用输出
  let outputs = []
  switch(node.data.type) {
    case 'llm':
      outputs = ['text', 'response', 'response_role', 'response_content']
      break
    case 'method':
      // 如果是方法节点，返回其输出字段
      outputs = Object.keys(node.data.outputs || {})
      break
    case 'variable-aggregator':
      outputs = ['result']
      break
    case 'answer':
      // 对于回答节点，确保answer是第一个选项
      const variables = node.data.variables?.map(v => v.variable) || []
      outputs = ['answer', ...variables]
      break
    case 'if-else':
      outputs = ['condition_result', 'true', 'false'] // 添加条件分支结果
      break
    case 'start':
      outputs = ['start'] // 开始节点可以有初始值
      break
    default:
      outputs = []
  }
  
  console.log(`节点(${nodeId})输出变量:`, outputs)
  return outputs
}

// 计算是否禁用选择
const isSelectionDisabled = computed(() => {
  if (!props.nodeId) return true
  
  const node = allNodes.value.find(n => n.id === props.nodeId)
  if (!node) return true
  
  // 对于LLM节点，如果已经选择了text，则禁用选择框
  if (node.data.type === 'llm' && props.modelValue === 'text') {
    return true
  }
  
  // 对于Answer节点，如果已经选择了answer，则禁用选择框
  if (node.data.type === 'answer' && props.modelValue === 'answer') {
    return true
  }
  
  return outputs.value.length === 0
})

// 监听节点ID变化
watch(() => props.nodeId, async (newNodeId, oldNodeId) => {
  console.log(`节点ID从${oldNodeId}变为${newNodeId}`)
  
  // 如果allNodes为空，先获取数据
  if ((!allNodes.value || allNodes.value.length === 0) && newNodeId) {
    console.log('节点数据为空，重新获取')
    await fetchWorkflowNodes()
  }
  
  // 如果没有输出列表，无需处理
  const currentOutputs = getNodeOutputs(newNodeId)
  if (currentOutputs.length === 0) {
    console.log(`节点${newNodeId}没有输出变量，重置选择`)
    emit('update:modelValue', '')
    return
  }
  
  // 检查节点类型
  const node = allNodes.value.find(n => n.id === newNodeId)
  console.log(`检查节点(${newNodeId})类型:`, node?.data?.type)
  
  // 如果当前选择的输出不在新节点的输出列表中，重置选择
  if (props.modelValue && !currentOutputs.includes(props.modelValue)) {
    console.log(`当前选择的输出${props.modelValue}不在新节点的输出列表中`)
    if (node && node.data.type === 'llm') {
      // 如果是LLM节点，默认选择"text"
      console.log('设置LLM节点默认输出为text')
      emit('update:modelValue', 'text')
    } else if (node && node.data.type === 'answer') {
      // 如果是Answer节点，默认选择"answer"
      console.log('设置Answer节点默认输出为answer')
      emit('update:modelValue', 'answer')
    } else {
      // 其他节点类型，清空选择
      console.log('重置输出变量选择')
      emit('update:modelValue', '')
    }
  } else if (!props.modelValue && currentOutputs.length > 0) {
    // 如果没有选中值但有输出变量
    console.log('没有选中的输出变量，但有可选输出')
    if (node && node.data.type === 'llm') {
      // 如果是LLM节点，默认选择"text"
      console.log('设置LLM节点默认输出为text')
      emit('update:modelValue', 'text')
    } else if (node && node.data.type === 'answer') {
      // 如果是Answer节点，默认选择"answer"
      console.log('设置Answer节点默认输出为answer')
      emit('update:modelValue', 'answer')
    } else if (currentOutputs.length > 0) {
      // 默认选择第一个输出
      console.log(`默认选择第一个输出: ${currentOutputs[0]}`)
      emit('update:modelValue', currentOutputs[0])
    }
  }
})

// 更新监听节点ID变化以处理边缘情况
watch([() => props.nodeId, () => allNodes.value.length], async ([newNodeId, nodesCount], [oldNodeId, oldCount]) => {
  // 如果组件刚刚获取到节点数据，需要重新检查输出
  if (oldCount === 0 && nodesCount > 0 && props.nodeId) {
    console.log('节点数据首次加载完成，重新检查输出变量')
    const outputs = getNodeOutputs(props.nodeId)
    
    if (outputs.length > 0 && !props.modelValue) {
      // 根据节点类型设置默认输出
      const node = allNodes.value.find(n => n.id === props.nodeId)
      if (node) {
        switch(node.data.type) {
          case 'llm':
            console.log('设置LLM节点默认输出为text')
            emit('update:modelValue', 'text')
            break
          case 'answer':
            console.log('设置Answer节点默认输出为answer')
            emit('update:modelValue', 'answer')
            break
          default:
            // 默认选择第一个输出
            console.log(`默认选择节点第一个输出: ${outputs[0]}`)
            emit('update:modelValue', outputs[0])
            break
        }
      }
    }
  }
})
</script>

<style scoped>
.output-selector {
  width: 100%;
  position: relative;
}

.empty-tip {
  margin-top: 4px;
  font-size: 12px;
  color: #999;
  padding: 4px 8px;
}

.warning-tip {
  margin-top: 4px;
  font-size: 12px;
  color: #ff7875;
  padding: 4px 8px;
  background-color: #fff2f0;
  border-radius: 4px;
  border: 1px solid #ffccc7;
  display: flex;
  align-items: center;
  gap: 4px;
}
</style> 