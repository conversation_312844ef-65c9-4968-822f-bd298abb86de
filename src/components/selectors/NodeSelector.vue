<template>
  <div class="node-selector">
    <div class="search-input-wrapper">
      <a-input-search
        v-model="searchQuery"
        :placeholder="placeholder"
        allow-clear
        @search="handleSearch"
        @input="handleInput"
        @focus="handleFocus"
        search-button
      />
      <div v-if="showSearchPanel" class="node-search-panel">
        <div v-if="searchResults && searchResults.length > 0" class="search-results">
          <div 
            v-for="node in searchResults" 
            :key="node.id"
            class="search-result-item"
            @click="() => selectNode(node)"
          >
            <div class="result-content">
              <div class="node-info">
                <span class="node-title">{{ node.data.title }}</span>
                <span class="node-id">ID: {{ node.id }}</span>
              </div>
              <div class="node-tags">
                <span class="node-type" :class="`type-${node.data.type}`">
                  {{ getNodeTypeLabel(node.data.type) }}
                </span>
                <span v-if="node.data.intermediate" class="node-tag intermediate-tag">
                  中间节点
                </span>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="no-results">
          未找到匹配的节点
        </div>
      </div>
    </div>
    <div v-if="modelValue" class="selected-node">
      <span>已选择: </span>
      <strong>{{ getNodeTitle(modelValue) }}</strong>
      <a-button type="text" size="mini" @click="clearSelectedNode">
        清除
      </a-button>
    </div>
    <div v-if="isSelectedNodeSpecial" class="warning-tip">
      <icon-exclamation-circle /> 警告: 选择特殊节点可能影响工作流逻辑
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { getCurrentWorkflowFull, getWorkflowData } from '../../utils/storage'
import { IconExclamationCircle } from '@arco-design/web-vue/es/icon'
import { Message } from '@arco-design/web-vue'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '搜索选择节点...'
  },
  excludeNodeId: {
    type: String,
    default: ''
  },
  filterSpecialNodes: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:modelValue', 'select', 'clear'])

const route = useRoute()
const searchQuery = ref('')
const searchResults = ref([])
const showSearchPanel = ref(false)
const allNodes = ref([])
const hasSearched = ref(false)

// 计算可选择的节点
const selectableNodes = computed(() => {
  if (!props.filterSpecialNodes) {
    return allNodes.value;
  }
  
  // 找到当前选中的节点
  const selectedNode = props.modelValue 
    ? allNodes.value.find(node => node.id === props.modelValue) 
    : null;
  
  const filtered = allNodes.value.filter(node => {
    // 过滤掉当前正在编辑的节点（除非它是当前选中的节点）
    if (node.id === props.excludeNodeId && node.id !== props.modelValue) {
      return false;
    }
    
    // 过滤掉条件节点和开始节点（除非它们是当前选中的节点）
    if ((node.data.type === 'if-else' || node.data.type === 'start') && node.id !== props.modelValue) {
      return false;
    }
    
    return true;
  });
  
  return filtered;
});

// 监听 modelValue 变化，更新搜索框
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    const selectedNode = allNodes.value.find(n => n.id === newValue)
    if (selectedNode) {
      searchQuery.value = selectedNode.data.title
    } else {
      searchQuery.value = `节点ID: ${newValue}`
    }
  } else {
    searchQuery.value = ''
  }
}, { immediate: true })

onMounted(async () => {
  console.log('NodeSelector组件挂载')
  const nodes = await fetchWorkflowNodes()
  console.log('初始化时获取到节点数量:', nodes.length)
  
  // 如果已有选中值，设置搜索框文本
  if (props.modelValue) {
    const selectedNode = allNodes.value.find(n => n.id === props.modelValue)
    if (selectedNode) {
      searchQuery.value = selectedNode.data.title
    } else {
      searchQuery.value = `节点ID: ${props.modelValue}`
    }
  }
  
  // 如果组件加载时自动初始化搜索结果
  searchResults.value = selectableNodes.value.slice(0, 10)
})

// 获取工作流中的所有节点
const fetchWorkflowNodes = async () => {
  try {
    console.log('开始获取工作流节点数据...')
    const clientKey = route.query.clientKey || ''
    console.log('客户端Key:', clientKey)
    
    // 方法1：尝试从getCurrentWorkflowFull获取
    let workflowData = getCurrentWorkflowFull(clientKey)
    console.log('方法1 - getCurrentWorkflowFull获取结果:', workflowData ? '有数据' : '无数据')
    
    // 如果第一种方法失败，尝试方法2
    if (!workflowData || !workflowData.nodes || workflowData.nodes.length === 0) {
      console.log('尝试方法2 - 从getWorkflowData获取')
      workflowData = getWorkflowData(clientKey)
      if (workflowData) {
        console.log('方法2 - 成功获取数据')
      }
    }
    
    // 如果前两种方法都失败，尝试方法3
    if (!workflowData || !workflowData.nodes || workflowData.nodes.length === 0) {
      console.log('尝试方法3 - 直接从localStorage读取')
      // 直接从localStorage中读取数据
      const keys = ['current_workflow_full', `current_workflow_full_${clientKey}`, 'workflowData', `workflowData_${clientKey}`]
      
      for (const key of keys) {
        try {
          const raw = localStorage.getItem(key)
          if (raw) {
            const parsed = JSON.parse(raw)
            if (parsed && parsed.nodes && parsed.nodes.length > 0) {
              console.log(`从localStorage键'${key}'成功获取数据，节点数量:`, parsed.nodes.length)
              workflowData = parsed
              break
            }
          }
        } catch (e) {
          console.error(`解析localStorage键'${key}'失败:`, e)
        }
      }
    }
    
    // 最终检查是否有可用数据
    if (workflowData && workflowData.nodes && workflowData.nodes.length > 0) {
      console.log('成功获取工作流节点，数量:', workflowData.nodes.length)
      allNodes.value = workflowData.nodes
      
      // 更新可选节点和初始搜索结果
      console.log('可选节点数量:', selectableNodes.value.length)
      searchResults.value = selectableNodes.value.slice(0, 10)
      
      return workflowData.nodes
    } else {
      // 如果尝试了所有方法仍然没有数据，显示提示
      console.error('无法获取工作流数据，所有方法均失败')
      Message.error('无法获取工作流数据，请确保当前页面已加载工作流')
      return []
    }
  } catch (error) {
    console.error('获取工作流节点时发生错误:', error)
    Message.error('获取节点数据失败: ' + error.message)
    return []
  }
}

// 处理搜索
const handleSearch = (query) => {
  hasSearched.value = true
  searchQuery.value = query.trim()
  
  console.log('搜索查询:', query, '可选节点数量:', selectableNodes.value.length)
  
  if (!searchQuery.value) {
    searchResults.value = selectableNodes.value.slice(0, 10)
    return
  }
  
  const lowercaseQuery = searchQuery.value.toLowerCase()
  searchResults.value = selectableNodes.value
    .filter(node => {
      const title = node.data?.title?.toLowerCase() || ''
      const type = getNodeTypeLabel(node.data?.type)?.toLowerCase() || ''
      const id = node.id?.toLowerCase() || ''
      const matches = title.includes(lowercaseQuery) || 
                      type.includes(lowercaseQuery) || 
                      id.includes(lowercaseQuery)
      
      if (matches) {
        console.log('匹配节点:', node.id, node.data?.title)
      }
      return matches
    })
    .slice(0, 10)
  
  console.log('搜索结果数量:', searchResults.value.length)
}

// 处理输入变化
const handleInput = (value) => {
  console.log('输入变化:', value)
  
  if (!value && props.modelValue) {
    clearSelectedNode()
  }
  
  if (!showSearchPanel.value) {
    showSearchPanel.value = true
  }
  
  handleSearch(value)
}

// 处理聚焦
const handleFocus = async () => {
  console.log('搜索框获得焦点')
  showSearchPanel.value = true
  
  // 如果节点数据为空，重新获取
  if (!allNodes.value || allNodes.value.length === 0) {
    console.log('节点为空，重新获取数据')
    await fetchWorkflowNodes()
  }
  
  // 初始加载结果
  searchResults.value = selectableNodes.value.slice(0, 10)
  console.log('聚焦时加载的结果数量:', searchResults.value.length)
  
  // 添加点击外部关闭面板的事件
  setTimeout(() => {
    const clickOutsideHandler = (event) => {
      const selectorEl = document.querySelector('.node-selector')
      if (selectorEl && !selectorEl.contains(event.target)) {
        showSearchPanel.value = false
        document.removeEventListener('click', clickOutsideHandler)
      }
    }
    
    document.addEventListener('click', clickOutsideHandler)
  }, 100)
}

// 选择节点
const selectNode = (node) => {
  searchQuery.value = node.data.title || `节点ID: ${node.id}`
  showSearchPanel.value = false
  emit('update:modelValue', node.id)
  emit('select', node)
}

// 清除选择
const clearSelectedNode = () => {
  searchQuery.value = ''
  emit('update:modelValue', '')
  emit('clear')
}

// 获取节点类型显示名称
const getNodeTypeLabel = (type) => {
  const nodeTypes = {
    'start': '开始',
    'llm': '大语言模型',
    'if-else': '条件判断',
    'method': '方法',
    'answer': '回答',
    'variable-aggregator': '变量聚合器',
    'coze_bot': 'COZE 机器人'
  }
  return nodeTypes[type] || type
}

// 获取节点标题
const getNodeTitle = (nodeId) => {
  const node = allNodes.value.find(n => n.id === nodeId)
  if (!node) return `节点ID: ${nodeId}`
  return `${node.data.title || '未命名节点'} (${getNodeTypeLabel(node.data.type)})`
}

// 是否已选择了特殊节点（开始节点、条件节点或当前正在编辑的节点）
const isSelectedNodeSpecial = computed(() => {
  if (!props.modelValue) return false;
  
  const selectedNode = allNodes.value.find(node => node.id === props.modelValue);
  if (!selectedNode) return false;
  
  // 检查是否是开始节点或条件节点
  if (selectedNode.data.type === 'if-else' || selectedNode.data.type === 'start') {
    return true;
  }
  
  // 检查是否是当前正在编辑的节点
  if (selectedNode.id === props.excludeNodeId) {
    return true;
  }
  
  return false;
});
</script>

<style scoped>
/* 节点选择器样式 */
.node-selector {
  position: relative;
  width: 100%;
}

.search-input-wrapper {
  position: relative;
  width: 100%;
}

.node-search-panel {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 350px;
  overflow-y: auto;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  margin-top: 4px;
  border: 1px solid #eaeaea;
}

.search-results {
  max-height: 350px;
  overflow-y: auto;
}

.search-result-item {
  padding: 15px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.2s ease;
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-item:hover {
  background-color: #f8f9fa;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.node-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.node-title {
  font-size: 15px;
  font-weight: 500;
  color: #2c3e50;
  flex-shrink: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 70%;
}

.node-id {
  font-size: 12px;
  color: #666;
  font-family: monospace;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.node-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: center;
}

.node-type {
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 6px;
  color: white;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.node-tag {
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 6px;
  font-weight: 500;
}

.intermediate-tag {
  background-color: #e9ecef;
  color: #495057;
}

.selected-node {
  margin-top: 8px;
  padding: 5px 8px;
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #333;
  background-color: #e8f5ff;
  border-radius: 4px;
  border: 1px solid #bedcff;
}

.selected-node strong {
  margin: 0 5px;
  color: #2196f3;
}

/* 节点类型颜色 */
.node-type.type-start {
  background: linear-gradient(135deg, #ff4b4b, #ff6b6b);
}

.node-type.type-llm {
  background: linear-gradient(135deg, #4caf50, #66bb6a);
}

.node-type.type-answer {
  background: linear-gradient(135deg, #9c27b0, #ba68c8);
}

.node-type.type-method {
  background: linear-gradient(135deg, #ff9800, #ffa726);
}

.node-type.type-variable-aggregator {
  background: linear-gradient(135deg, #607d8b, #78909c);
}

.node-type.type-if-else {
  background: linear-gradient(135deg, #2196f3, #42a5f5);
}

.node-type.type-coze_bot {
  background: linear-gradient(135deg, #9c27b0, #ba68c8);
}

.no-results {
  padding: 20px;
  text-align: center;
  color: #6c757d;
  font-size: 14px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-top: 10px;
}

.warning-tip {
  margin-top: 8px;
  padding: 5px 8px;
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #333;
  background-color: #fff3e0;
  border-radius: 4px;
  border: 1px solid #ffd591;
}

.warning-tip .icon-exclamation-circle {
  margin-right: 5px;
}

/* 自定义滚动条 */
.search-results::-webkit-scrollbar {
  width: 6px;
}

.search-results::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.search-results::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 3px;
}

.search-results::-webkit-scrollbar-thumb:hover {
  background: #999;
}
</style> 