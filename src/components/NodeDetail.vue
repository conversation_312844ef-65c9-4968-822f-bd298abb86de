<template>
  <div class="node-detail-page">
    <!-- 组件标签筛选区 -->
    <div class="component-filter">
      <div class="selected-components">
        <span class="filter-label">组件筛选:</span>
        <a-tag 
          v-for="comp in selectedComponents" 
          :key="comp" 
          closable 
          @close="removeComponent(comp)"
          color="arcoblue"
        >
          {{ comp }}
        </a-tag>
        <a-input-search
          v-model="newComponent"
          placeholder="添加组件..."
          search-button
          style="width: 180px; margin-left: 8px;"
          @search="addComponent"
        />
      </div>
    </div>
    
    <!-- 顶部导航栏 -->
    <div class="header">
      <div class="left">
        <a-button class="back-btn" type="text" @click="$emit('close')">
          <template #icon><icon-left /></template>
          返回
        </a-button>
        <div class="divider"></div>
        <div class="title">
          <span class="component-name">{{ componentName }}</span>
          <a-tag size="small" class="trace-id">
            <template #icon><icon-tag /></template>
            Trace ID: {{ displayTraceId }}
          </a-tag>
        </div>
      </div>
      <div class="right">
        <a-space>
          <a-tooltip content="刷新日志">
            <a-button type="text" :loading="loading" @click="fetchLogs">
              <template #icon><icon-refresh /></template>
            </a-button>
          </a-tooltip>
          <a-tooltip content="导出日志">
            <a-button type="text" @click="exportLogs">
              <template #icon><icon-download /></template>
            </a-button>
          </a-tooltip>
          <a-select
            v-model="filterLevel"
            style="width: 100px"
            placeholder="日志级别"
            allow-clear
          >
            <a-option value="INFO">INFO</a-option>
            <a-option value="WARN">WARN</a-option>
            <a-option value="ERROR">ERROR</a-option>
          </a-select>
          <a-input-search
            v-model="searchKeyword"
            placeholder="搜索日志..."
            style="width: 200px"
            allow-clear
          />
        </a-space>
      </div>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="content">
      <div v-if="loading" class="loading">
        <a-spin dot size="large" />
        <span>加载中...</span>
      </div>
      
      <div v-else-if="error" class="error">
        <a-result status="error" :subtitle="error">
          <template #extra>
            <a-button type="primary" @click="fetchLogs">
              重试
            </a-button>
          </template>
        </a-result>
      </div>
      
      <template v-else>
        <div v-if="filteredLogs.length === 0" class="no-data">
          <a-empty description="未找到相关日志" />
        </div>
        
        <div v-else class="logs">
          <a-timeline>
            <a-timeline-item
              v-for="(log, index) in filteredLogs"
              :key="index"
              :dot-color="getLogColor(log.level)"
            >
              <div class="log-item" :data-level="log.level.toUpperCase()">
                <div class="log-header">
                  <span class="timestamp">{{ formatTime(log.timestamp) }}</span>
                  <span :class="['log-tag', `log-tag-${log.level.toLowerCase()}`]">
                    {{ log.level }}
                  </span>
                </div>
                <div class="log-content" :class="{ 'highlight': isHighlighted(log.message) }">
                  {{ formatLogMessage(log.message, index) }}
                  <div v-if="needsToggle(log.message)" class="log-toggle">
                    <button 
                      class="toggle-button" 
                      @click="toggleLogExpand(index)"
                    >
                      <span v-if="expandedLogs[index]">收起 ↑</span>
                      <span v-else>展开全部 ↓</span>
                    </button>
                  </div>
                </div>
                <div v-if="log.data" class="log-data">
                  <div class="data-header">
                    <span>详细数据</span>
                    <a-button
                      type="text"
                      size="mini"
                      @click="toggleDataExpand(index)"
                    >
                      <template #icon>
                        <icon-up v-if="expandedData[index]" />
                        <icon-down v-else />
                      </template>
                    </a-button>
                  </div>
                  <div v-show="expandedData[index]" class="data-content">
                    <vue-json-pretty
                      :data="log.data"
                      :deep="3"
                      :show-length="true"
                      :show-line="true"
                      :show-double-quotes="false"
                      :path-selectable="(path, data) => true"
                      @click="handleDataClick"
                    />
                  </div>
                </div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </div>

        <!-- 加载更多按钮 -->
        <div v-if="hasMore" class="load-more">
          <a-button type="text" :loading="loadingMore" @click="loadMore">
            加载更多
          </a-button>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, onUnmounted, nextTick } from 'vue'
import { 
  IconLeft,
  IconTag,
  IconRefresh,
  IconDownload,
  IconUp,
  IconDown,
  IconSync
} from '@arco-design/web-vue/es/icon'
import { Message } from '@arco-design/web-vue'
import VueJsonPretty from 'vue-json-pretty'
import { 
  getLogTraceId, 
  saveLogTraceId, 
  getLogTraceEnv, 
  saveLogTraceEnv,
  getAuthTokenFromStorage,
  saveAuthToken
} from '@/utils/storage'
import { searchLogs, getAuthToken } from '@/api/log/logs'
import 'vue-json-pretty/lib/styles.css'

const props = defineProps({
  componentName: {
    type: String,
    required: true
  },
  traceId: {
    type: String,
    required: false,
    default: ''
  }
})

const emit = defineEmits(['close'])

// 固定 token 和 index
const token = ref('')
const index = 'roki_ai_ckb_agent_manager'
const envList = ['dev', 'test', 'prod']

// 状态管理
const loading = ref(false)
const loadingMore = ref(false)
const error = ref(null)
const logs = ref([])
const page = ref(1)
const pageSize = ref(20)
const hasMore = ref(true)
const expandedData = ref({})
const expandedLogs = ref({})
const filterLevel = ref('')
const searchKeyword = ref('')

// 组件查询相关变量
const componentQuery = ref('')

// 组件标签相关变量
const selectedComponents = ref([props.componentName]) // 默认选中传入的组件
const newComponent = ref('')

// 计算过滤后的日志
const filteredLogs = computed(() => {
  return logs.value.filter(log => {
    const levelMatch = !filterLevel.value || log.level === filterLevel.value
    const keywordMatch = !searchKeyword.value || 
      log.message.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      JSON.stringify(log.data).toLowerCase().includes(searchKeyword.value.toLowerCase())
    return levelMatch && keywordMatch
  })
})

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  })
}

// 获取日志颜色
const getLogColor = (level) => {
  switch (level) {
    case 'ERROR': return 'red'
    case 'WARN': return 'orange'
    case 'INFO': return 'green'
    default: return 'gray'
  }
}

// 检查是否需要高亮显示
const isHighlighted = (message) => {
  if (!searchKeyword.value) return false
  return message.toLowerCase().includes(searchKeyword.value.toLowerCase())
}

// 切换数据展开状态
const toggleDataExpand = (index) => {
  expandedData.value[index] = !expandedData.value[index]
}

// 处理数据点击
const handleDataClick = (path, data) => {
  console.log('Clicked data:', { path, data })
}

// 导出日志
const exportLogs = () => {
  try {
    const data = JSON.stringify(filteredLogs.value, null, 2)
    const blob = new Blob([data], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `logs_${props.componentName}_${props.traceId}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  } catch (err) {
    console.error('导出日志失败:', err)
  }
}

// 在 script setup 部分添加 currentTraceId 的响应式引用
const currentTraceId = computed(() => {
  const storageTraceId = getLogTraceId()
  console.log('获取 storage traceId:', storageTraceId)
  return storageTraceId || props.traceId
})

// 监听 currentTraceId 的变化
watch(currentTraceId, (newVal, oldVal) => {
  console.log('traceId 发生变化:', { newVal, oldVal })
  if (newVal && newVal !== oldVal) {
    fetchLogs()
  }
})

// 添加组件到筛选列表
const addComponent = () => {
  if (!newComponent.value) {
    Message.warning('请输入要添加的组件名称')
    return
  }
  
  if (selectedComponents.value.includes(newComponent.value)) {
    Message.warning('该组件已添加')
    return
  }
  
  selectedComponents.value.push(newComponent.value)
  newComponent.value = '' // 清空输入框
  
  // 添加后重新查询日志
  fetchLogsByComponents()
}

// 从筛选列表移除组件
const removeComponent = (component) => {
  // 移除组件限制检查，允许删除所有组件
  selectedComponents.value = selectedComponents.value.filter(c => c !== component)
  
  // 移除后重新查询日志
  fetchLogsByComponents()
}

// 添加一个函数，用于获取真实的日志级别字段
const getLogLevelQuery = (level) => {
  if (!level) return null
  
  // 使用match_phrase查询日志级别，考虑末尾可能有空格的情况
  return {
    bool: {
      should: [
        { match_phrase: { level: level } },
        { match_phrase: { level: level.toLowerCase() } },
        { match_phrase: { level: level + " " } },         // 添加带空格版本
        { match_phrase: { level: level.toLowerCase() + " " } } // 添加带空格的小写版本
      ],
      minimum_should_match: 1
    }
  }
}

// 根据选中的组件查询日志
const fetchLogsByComponents = async () => {
  const traceId = currentTraceId.value
  if (!traceId) {
    error.value = '未找到 Trace ID'
    loading.value = false
    return
  }
  
  // 检查token是否存在，如果不存在则尝试获取
  if (!token.value) {
    try {
      token.value = await getAuthToken()
      console.log('授权 token 获取成功：', token.value)
    } catch (err) {
      console.error('获取授权token失败:', err)
      error.value = '获取授权token失败，请重试'
      loading.value = false
      return
    }
  }
  
  try {
    loading.value = true
    error.value = null
    page.value = 1
    logs.value = [] // 清空日志，避免显示旧数据
    expandedLogs.value = {} // 重置日志展开状态
    expandedData.value = {} // 重置数据展开状态
    
    const env = getLogTraceEnv()
    if (!env) {
      throw new Error('未找到日志查询环境')
    }
    
    const body = {
      size: pageSize.value,
      sort: [
        { '@timestamp': { order: 'desc' } }
      ],
      query: {
        bool: {
          must: [
            { terms: { 'traceId.keyword': [traceId] } }
          ]
        }
      }
    }
    
    // 只有当有选择组件时，才添加组件筛选条件
    if (selectedComponents.value.length > 0) {
      const componentFilters = selectedComponents.value.map(comp => {
        return { match_phrase: { component: comp } }
      })
      
      body.query.bool.should = componentFilters
      body.query.bool.minimum_should_match = 1
    }
    
    // 添加日志级别筛选 - 使用新的查询方式
    if (filterLevel.value) {
      const levelQuery = getLogLevelQuery(filterLevel.value)
      if (levelQuery) {
        body.query.bool.must.push(levelQuery)
      }
    }
    
    // 添加搜索关键词筛选
    if (searchKeyword.value) {
      body.query.bool.must.push({
        multi_match: {
          query: searchKeyword.value,
          fields: ['message', 'data']
        }
      })
    }
    
    console.log('查询参数:', JSON.stringify(body, null, 2))
    
    const res = await searchLogs({ index, body, token: token.value, env })
    
    // 检查是否有新token返回（token失效自动更新的情况）
    if (res.newToken) {
      console.log('检测到token已更新，保存新token')
      token.value = res.newToken
      saveAuthToken(res.newToken) // 保存到本地存储
    }
    
    console.log('查询结果:', res.data.data?.hits?.total, '条记录')
    const hits = res.data.data?.hits?.hits || []
    
    if (hits.length > 0) {
      // 记录第一条日志的格式，帮助调试
      console.log('日志格式示例:', JSON.stringify(hits[0]._source, null, 2))
    }
    
    // 转换日志数据格式 - 修正日志级别处理
    logs.value = hits.map(hit => {
      const source = hit._source
      let levelValue = source.level ? source.level.trim() : 'INFO'
      
      // 标准化日志级别为大写，不带空格
      if (levelValue.toUpperCase().includes('INFO')) levelValue = 'INFO'
      else if (levelValue.toUpperCase().includes('WARN')) levelValue = 'WARN'
      else if (levelValue.toUpperCase().includes('ERROR')) levelValue = 'ERROR'
      else levelValue = 'INFO'
      
      // 调试日志级别
      console.log('处理后的日志级别:', levelValue);
      
      // 处理消息内容，ERROR级别时添加stack_trace
      let messageContent = source.message || '';
      if (levelValue === 'ERROR' && source.stack_trace) {
        messageContent += '\n\n堆栈信息:\n' + source.stack_trace;
      }
      
      return {
        timestamp: source['@timestamp'] || source.time || '',
        level: levelValue,
        message: messageContent,
        data: source.data || extractDataFromSource(source) || null
      }
    })
    
    console.log('处理后的日志数据:', logs.value.length, '条记录')
    if (logs.value.length > 0) {
      console.log('第一条日志示例:', logs.value[0])
    }
    
    hasMore.value = hits.length >= pageSize.value
    
    if (logs.value.length === 0) {
      if (selectedComponents.value.length > 0) {
        Message.info('未找到选中组件的相关日志')
      } else {
        Message.info('未找到相关日志')
      }
    }
  } catch (err) {
    console.error('查询日志失败:', err)
    
    // 检查是否是token相关错误
    if (err.response && (err.response.status === 401 || err.response.status === 403)) {
      console.log('可能是token问题，尝试强制刷新token')
      try {
        // 强制刷新token
        token.value = await getAuthToken(undefined, true)
        console.log('强制刷新token成功，重新尝试查询')
        // 递归调用自身重试
        return fetchLogsByComponents()
      } catch (tokenErr) {
        console.error('强制刷新token失败:', tokenErr)
        error.value = '授权失败，请刷新页面重试'
      }
    } else {
      error.value = err?.response?.data?.message || err.message || '查询日志失败'
    }
    
    logs.value = []
  } finally {
    loading.value = false
  }
}

// 从日志源中提取数据
const extractDataFromSource = (source) => {
  // 如果没有data字段，尝试从其他字段中提取有用信息
  if (!source.data) {
    const dataFields = {}
    
    // 复制可能有用的字段
    const usefulFields = [
      'sessionId', 'code', 'env', 'userId', 
      'agentId', 'thread', 'class', 'component'
    ]
    
    usefulFields.forEach(field => {
      if (source[field]) {
        dataFields[field] = source[field]
      }
    })
    
    // 如果提取了有用字段，则返回
    if (Object.keys(dataFields).length > 0) {
      return dataFields
    }
  }
  
  return source.data
}

// 修改 fetchLogs 函数，调用 fetchLogsByComponents
const fetchLogs = () => {
  fetchLogsByComponents()
}

// 修改加载更多日志函数，考虑组件筛选
const loadMore = async () => {
  if (loadingMore.value) return
  
  const traceId = currentTraceId.value
  if (!traceId) {
    error.value = '未找到 Trace ID'
    return
  }
  
  // 检查token是否存在，如果不存在则尝试获取
  if (!token.value) {
    try {
      token.value = await getAuthToken()
      console.log('授权 token 获取成功：', token.value)
    } catch (err) {
      console.error('获取授权token失败:', err)
      error.value = '获取授权token失败，请重试'
      return
    }
  }

  try {
    loadingMore.value = true
    page.value++
    
    const env = getLogTraceEnv()
    if (!env) {
      throw new Error('未找到日志查询环境')
    }

    const body = {
      from: (page.value - 1) * pageSize.value,
      size: pageSize.value,
      sort: [
        { '@timestamp': { order: 'desc' } }
      ],
      query: {
        bool: {
          must: [
            { terms: { 'traceId.keyword': [traceId] } }
          ]
        }
      }
    }
    
    // 只有当有选择组件时，才添加组件筛选条件
    if (selectedComponents.value.length > 0) {
      const componentFilters = selectedComponents.value.map(comp => {
        return { match_phrase: { component: comp } }
      })
      
      body.query.bool.should = componentFilters
      body.query.bool.minimum_should_match = 1
    }
    
    // 添加日志级别筛选 - 使用新的查询方式
    if (filterLevel.value) {
      const levelQuery = getLogLevelQuery(filterLevel.value)
      if (levelQuery) {
        body.query.bool.must.push(levelQuery)
      }
    }
    
    // 添加搜索关键词筛选
    if (searchKeyword.value) {
      body.query.bool.must.push({
        multi_match: {
          query: searchKeyword.value,
          fields: ['message', 'data']
        }
      })
    }

    const res = await searchLogs({ index, body, token: token.value, env })
    
    // 检查是否有新token返回（token失效自动更新的情况）
    if (res.newToken) {
      console.log('检测到token已更新，保存新token')
      token.value = res.newToken
      saveAuthToken(res.newToken) // 保存到本地存储
    }
    
    const hits = res.data.data?.hits?.hits || []
    
    // 转换并添加新的日志数据 - 修正日志级别处理
    const newLogs = hits.map(hit => {
      const source = hit._source
      let levelValue = source.level ? source.level.trim() : 'INFO'
      
      // 标准化日志级别为大写，不带空格
      if (levelValue.toUpperCase().includes('INFO')) levelValue = 'INFO'
      else if (levelValue.toUpperCase().includes('WARN')) levelValue = 'WARN'
      else if (levelValue.toUpperCase().includes('ERROR')) levelValue = 'ERROR'
      else levelValue = 'INFO'
      
      // 处理消息内容，ERROR级别时添加stack_trace
      let messageContent = source.message || '';
      if (levelValue === 'ERROR' && source.stack_trace) {
        messageContent += '\n\n堆栈信息:\n' + source.stack_trace;
      }
      
      return {
        timestamp: source['@timestamp'] || source.time || '',
        level: levelValue,
        message: messageContent,
        data: source.data || extractDataFromSource(source) || null
      }
    })
    
    logs.value = [...logs.value, ...newLogs]
    hasMore.value = hits.length >= pageSize.value
  } catch (err) {
    console.error('加载更多日志失败:', err)
    
    // 检查是否是token相关错误
    if (err.response && (err.response.status === 401 || err.response.status === 403)) {
      console.log('可能是token问题，尝试强制刷新token')
      try {
        // 强制刷新token
        token.value = await getAuthToken(undefined, true)
        console.log('强制刷新token成功，重新尝试加载更多')
        // 递归调用自身重试
        return loadMore()
      } catch (tokenErr) {
        console.error('强制刷新token失败:', tokenErr)
        Message.error('授权失败，请刷新页面重试')
      }
    }
  } finally {
    loadingMore.value = false
  }
}

// 监听筛选条件变化
watch([filterLevel, searchKeyword], () => {
  console.log('Filter changed:', { filterLevel: filterLevel.value, searchKeyword: searchKeyword.value })
  // 当筛选条件变化时，自动触发查询
  fetchLogsByComponents()
})

// 在组件挂载时添加 computed 属性
const displayTraceId = computed(() => {
  return getLogTraceId() || props.traceId || '未设置'
})

// 格式化日志消息，支持截断和展开
const formatLogMessage = (message, index) => {
  if (!message) return '';
  
  if (message.length <= 1500 || expandedLogs.value[index]) {
    return message;
  }
  
  return message.substring(0, 1500) + '...';
}

// 判断是否需要显示展开/收起按钮
const needsToggle = (message) => {
  if (!message) return false;
  return message.length > 1500;
}

// 切换日志展开状态
const toggleLogExpand = (index) => {
  expandedLogs.value[index] = !expandedLogs.value[index];
  console.log('切换日志展开状态:', index, expandedLogs.value[index]);
}

// 添加刷新token的方法
const refreshToken = async () => {
  try {
    loading.value = true
    console.log('手动刷新token...')
    token.value = await getAuthToken(undefined, true) // 强制刷新
    console.log('token刷新成功:', token.value)
    Message.success('授权刷新成功')
    // 刷新日志
    fetchLogs()
  } catch (err) {
    console.error('刷新token失败:', err)
    error.value = '刷新授权失败，请重试'
    Message.error('刷新授权失败，请重试')
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  console.log('组件挂载，初始参数:', { 
    componentName: props.componentName,
    storageTraceId: getLogTraceId(),
    propsTraceId: props.traceId,
    currentTraceId: currentTraceId.value
  })

  // 获取授权token
  getAuthToken()
    .then(authToken => {
      token.value = authToken
      console.log('授权 token 获取成功：', token.value)
      // 确保立即调用 fetchLogs
      nextTick(() => {
        console.log('开始初始化查询')
        fetchLogs()
      })
    })
    .catch(err => {
      console.error('获取授权token失败:', err)
      error.value = '获取授权token失败，请重试'
      Message.error('获取授权token失败，请重试')
    })
})

// 在组件销毁时清理
onUnmounted(() => {
  // 可以选择是否要清除 traceId
  // clearLogTraceId()
})
</script>

<style scoped>
.node-detail-page {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  background: #fff;
  border-bottom: 1px solid #e5e6eb;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.divider {
  width: 1px;
  height: 24px;
  background: #e5e6eb;
}

.title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.component-name {
  font-size: 16px;
  font-weight: 600;
  color: rgb(var(--green-6));
}

.trace-id {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  background: #f2f3f5;
  border: none;
}

.content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  background: #f7f8fa;
}

.loading, .error, .no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
}

.logs {
  max-width: 1200px;
  margin: 0 auto;
}

.log-item {
  background: #fff;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.timestamp {
  font-size: 13px;
  color: rgb(var(--green-4));
}

.log-content {
  font-size: 14px;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-all;
  margin: 8px 0;
  padding: 8px;
  background: #fff;
  border-radius: 4px;
  color: #333333 !important; /* 默认为黑色文本 */
}

/* 只有ERROR级别保持红色，其他级别都使用黑色 */
.log-item[data-level="ERROR"] .log-content {
  color: #f53f3f !important; /* 错误信息为红色 */
}

.log-item[data-level="WARN"] .log-content {
  color: #333333 !important; /* 警告信息改为黑色 */
}

.log-item[data-level="INFO"] .log-content {
  color: #333333 !important; /* 信息类型改为黑色 */
}

.log-content.highlight {
  background: #fff;
  border: 1px solid rgb(var(--green-2));
}

.log-data {
  margin-top: 8px;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
}

.data-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #fff;
  border-bottom: 1px solid #e5e6eb;
  font-size: 13px;
  color: rgb(var(--green-5));
}

.data-content {
  padding: 12px;
  background: #fff;
  color: rgb(var(--green-5));
}

/* 设置标签颜色，保持原样 */
.log-tag-error {
  color: #f53f3f;
  background: #ffece8;
  border: 1px solid #ffbfb5;
}

.log-tag-warn {
  color: #ff7d00;
  background: #fff7e8;
  border: 1px solid #ffd77a;
}

.log-tag-info {
  color: #00b42a;
  background: #e8ffea;
  border: 1px solid #adffc2;
}

/* 修改JSON数据颜色，保持一致的黑色 */
.log-item .data-content :deep(.vjs-tree),
.log-item .data-content :deep(.vjs-value) {
  color: #333333 !important;
}

/* 错误级别的JSON数据保持红色 */
.log-item[data-level="ERROR"] .data-content :deep(.vjs-tree),
.log-item[data-level="ERROR"] .data-content :deep(.vjs-value) {
  color: #f53f3f !important;
}

:deep(.vjs-tree) {
  color: #333333 !important;
  background: #fff !important;
}

:deep(.vjs-value) {
  color: #333333 !important;
}

:deep(.vjs-key) {
  color: #666666 !important;
}

.load-more {
  text-align: center;
  margin-top: 24px;
}

/* 自定义滚动条样式 */
.content::-webkit-scrollbar {
  width: 8px;
}

.content::-webkit-scrollbar-track {
  background: #f7f8fa;
}

.content::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 4px;
}

.content::-webkit-scrollbar-thumb:hover {
  background: #999;
}

/* Timeline 样式优化 */
:deep(.arco-timeline) {
  padding: 8px 0;
}

:deep(.arco-timeline-item) {
  padding-bottom: 24px;
}

:deep(.arco-timeline-item-content) {
  margin-left: 24px;
}

/* 动画效果 */
.log-item {
  transition: transform 0.2s ease;
}

.log-item:hover {
  transform: translateX(4px);
}

/* 响应式布局 */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 12px;
    padding: 12px;
  }

  .right {
    width: 100%;
  }

  .content {
    padding: 12px;
  }

  .logs {
    max-width: 100%;
  }
}

/* 添加标签颜色覆盖样式 */
:deep(.arco-tag.arco-tag-arcoblue) {
  color: rgb(0, 180, 42);
  background: rgb(226, 255, 233);
  border-color: rgb(226, 255, 233);
}

.log-tag {
  display: inline-flex;
  align-items: center;
  height: 24px;
  padding: 0 8px;
  font-size: 12px;
  border-radius: 2px;
}

.log-tag-info {
  color: #00b42a;
  background: #e8ffea;
  border: 1px solid #adffc2;
}

.log-tag-warn {
  color: #ff7d00;
  background: #fff7e8;
  border: 1px solid #ffd77a;
}

.log-tag-error {
  color: #f53f3f;
  background: #ffece8;
  border: 1px solid #ffbfb5;
}

.log-tag-default {
  color: #86909c;
  background: #f2f3f5;
  border: 1px solid #e5e6eb;
}

/* 日志操作区域 */
.log-toggle {
  margin-top: 10px;
  text-align: center;
}

.toggle-button {
  color: #fff;
  background-color: #00b42a;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 13px;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.toggle-button:hover {
  background-color: #009a23;
}

.component-filter {
  padding: 12px 24px;
  background: #fff;
  border-bottom: 1px solid #e5e6eb;
}

.selected-components {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  color: #86909c;
  margin-right: 8px;
}

@media (max-width: 768px) {
  .selected-components {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .selected-components .arco-input-search {
    width: 100%;
    margin-left: 0;
  }
}
</style> 