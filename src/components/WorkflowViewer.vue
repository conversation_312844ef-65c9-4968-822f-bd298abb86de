<template>
  <div class="workflow-viewer">
    <!-- 添加返回按钮和暂存按钮 -->
    <div class="top-buttons">
      <a-button class="back-button" type="outline" @click="goBack">
        <template #icon><icon-left /></template>
        返回
      </a-button>
      <a-button class="save-button" type="primary" @click="handleSaveWorkflowData">
        <template #icon><icon-save /></template>
        暂存
      </a-button>
    </div>

    <!-- 添加节点面板 -->
    <div class="add-node-panel" :class="{ 'collapsed': isPanelCollapsed }">
      <div class="panel-header">
        <h3>添加节点</h3>
        <a-button class="toggle-panel-btn" type="text" size="small" @click="togglePanel">
          {{ isPanelCollapsed ? '展开' : '收起' }}
        </a-button>
      </div>

      <div v-if="!isPanelCollapsed" class="panel-content">
        <div class="node-types">
          <a-button
              v-for="nodeType in nodeTypes"
              :key="nodeType.type"
              class="add-node-btn"
              :class="`btn-${nodeType.type}`"
              @click="addNode(nodeType)"
          >
            {{ nodeType.label }}
          </a-button>
        </div>

        <div class="json-actions">
          <div class="json-btn-group">
            <a-button class="json-btn" @click="copyWorkflowJson">
              <span v-if="!copySuccess">复制 JSON</span>
              <span v-else>复制成功 <icon-check /></span>
            </a-button>

            <a-button class="json-btn import-btn" @click="showImportDialog">
              导入 JSON
            </a-button>
          </div>

          <div class="json-btn-group">
            <a-button class="json-btn compare-btn" type="primary" status="warning" @click="compareWorkflowJson">
              对比 JSON
            </a-button>

            <a-button class="json-btn import-node-btn" type="primary" @click="showImportNodeDialog">
              导入组件
            </a-button>
          </div>

          <div class="json-btn-group">
            <a-button
                class="json-btn export-selected-btn"
                type="primary"
                status="success"
                @click="exportSelectedNodes"
                :disabled="selectedNodesCount === 0"
            >
              导出选中节点 <span v-if="selectedNodesCount > 0">({{ selectedNodesCount }})</span>
            </a-button>
          </div>

        </div>

      </div>
    </div>

    <!-- 搜索面板 -->
    <div class="search-panel" :class="{ 'collapsed': isSearchPanelCollapsed }">
      <div class="panel-header">
        <h3>搜索节点</h3>
        <a-button class="toggle-panel-btn" type="text" size="small" @click="toggleSearchPanel">
          {{ isSearchPanelCollapsed ? '展开' : '收起' }}
        </a-button>
      </div>
      <div v-if="!isSearchPanelCollapsed" class="panel-content">
        <a-input-search
            v-model="searchQuery"
            placeholder="输入节点标题搜索..."
            :style="{ width: '100%' }"
            @search="handleSearch"
            @keyup.enter="handleSearch"
            search-button
        />
        <div v-if="searchResults.length > 0" class="search-results">
          <div
              v-for="node in searchResults"
              :key="node.id"
              class="search-result-item"
              @click="focusOnNode(node)"
          >
            <div class="result-content">
              <div class="node-info">
                <span class="node-title">{{ node.data.title }}</span>
                <span class="node-id">ID: {{ node.id }}</span>
              </div>
              <div class="node-tags">
                <span class="node-type" :class="`type-${node.data.type}`">
                  {{ getNodeTypeLabel(node.data.type) }}
                </span>
                <span v-if="node.data.intermediate" class="node-tag intermediate-tag">
                  中间节点
                </span>
              </div>
            </div>
          </div>
        </div>
        <div v-else-if="hasSearched && searchQuery" class="no-results">
          未找到匹配的节点
        </div>
      </div>
    </div>

    <!-- 收起状态下的展开按钮组 -->
    <div class="expand-buttons-group">
      <a-button
          v-if="isPanelCollapsed"
          class="expand-panel-btn add-btn"
          type="primary"
          shape="circle"
          @click="togglePanel"
          title="展开添加节点面板"
      >
        <template #icon><icon-plus /></template>
      </a-button>

      <a-button
          v-if="isSearchPanelCollapsed"
          class="expand-panel-btn search-btn"
          type="primary"
          shape="circle"
          @click="toggleSearchPanel"
          title="展开搜索面板"
      >
        <template #icon><icon-search /></template>
      </a-button>
    </div>

    <!-- 添加缩放控制按钮 -->
    <div class="zoom-controls">
      <a-button class="zoom-btn" @click="zoomIn" title="放大">
        <template #icon><icon-plus /></template>
      </a-button>
      <div class="zoom-level">{{ Math.round(viewport.zoom * 100) }}%</div>
      <a-button class="zoom-btn" @click="zoomOut" title="缩小">
        <template #icon><icon-minus /></template>
      </a-button>
      <a-button class="zoom-btn fit-btn" type="primary" @click="fitToView" title="适应视图">
        <template #icon><icon-fullscreen /></template>
      </a-button>
    </div>

    <VueFlow
        v-model="elements"
        :default-viewport="{ zoom: 0.7 }"
        @paneReady="onPaneReady"
        @connect="onConnect"
        :connect-on-drop="true"
        :default-edge-options="defaultEdgeOptions"
        :delete-key-code="['Delete', 'Backspace']"
        @nodeClick="onNodeClick"
        @edgeClick="onEdgeClick"
        @panelClick="closeDebugDrawer"
        @nodesDelete="handleNodesDelete"
        @keydown="handleKeyDown"
        :min-zoom="0.1"
        :max-zoom="2"
        :multi-selection-key-code="['Control', 'Meta', 'Shift']"
        :selection-key-code="null"
    >
      <template #node-custom="nodeProps">
        <CustomNode
            v-bind="nodeProps"
            @editLlm="openLlmEditor"
            @editAnswer="openAnswerEditor"
            @editMethod="openMethodEditor"
            @editVariableAggregator="openVariableAggregatorEditor"
            @editIfElse="openIfElseEditor"
            @editCoze="openCozeEditor"
            @editEnd="openEndEditor"
            @deleteNode="deleteNode"
            @copyNode="copyNode"
            @debugLlm="openDebugDrawer"
            @debugMethod="openMethodDebugDrawer"
            @showNodeDetail="openNodeDetail"
        />
      </template>
      <Background pattern-color="#aaa" :gap="16" />
      <MiniMap />
      <Controls />
    </VueFlow>

    <!-- LLM 节点编辑器 -->
    <LlmNodeEditor
        v-if="showLlmEditor"
        :node="editingLlmNode"
        @save="saveLlmNode"
        @close="closeLlmEditor"
    />

    <!-- Answer 节点编辑器 -->
    <AnswerNodeEditor
        v-if="showAnswerEditor"
        :node="editingAnswerNode"
        @save="saveAnswerNode"
        @close="closeAnswerEditor"
    />

    <!-- Method 节点编辑器 -->
    <MethodNodeEditor
        v-if="showMethodEditor"
        :node="editingMethodNode"
        @save="saveMethodNode"
        @close="closeMethodEditor"
    />

    <!-- 变量聚合节点编辑器 -->
    <VariableAggregatorNodeEditor
        v-if="showVariableAggregatorEditor"
        :node="editingVariableAggregatorNode"
        @save="saveVariableAggregatorNode"
        @close="closeVariableAggregatorEditor"
    />

    <!-- If-Else 节点编辑器 -->
    <IfElseNodeEditor
        v-if="showIfElseEditor"
        :node="editingIfElseNode"
        @save="saveIfElseNode"
        @close="closeIfElseEditor"
        @removeEdges="removeIfElseEdges"
    />

    <!-- COZE 节点编辑器 -->
    <CozeNodeEditor
        v-if="showCozeEditor"
        :node="editingCozeNode"
        @save="saveCozeNode"
        @close="closeCozeEditor"
    />

    <!-- 结束节点编辑器 -->
    <EndNodeEditor
        v-if="showEndEditor"
        :node="editingEndNode"
        @save="saveEndNode"
        @close="closeEndEditor"
    />

    <!-- 删除确认对话框 -->
    <a-modal
        v-model:visible="showDeleteConfirm"
        @cancel="cancelDelete"
        @before-ok="confirmDelete"
        simple
        title="确认删除"
    >
      <div class="delete-confirm-content">
        <p>此节点被其他节点引用，删除可能导致工作流错误。确定要删除吗？</p>
        <p v-if="nodeToDelete">节点类型: {{ getNodeTypeLabel(nodeToDelete.data.type) }}</p>
        <p v-if="nodeToDelete">节点标题: {{ nodeToDelete.data.title || nodeToDelete.id }}</p>

        <div v-if="referencingNodesInfo && referencingNodesInfo.length > 0" class="referencing-nodes">
          <p class="referencing-title">引用此节点的节点:</p>
          <ul class="referencing-list">
            <li v-for="node in referencingNodesInfo" :key="node.id" class="referencing-item">
              <span class="node-title">{{ node.title }}</span>
              <span class="node-type">({{ getNodeTypeLabel(node.type) }})</span>
            </li>
          </ul>
        </div>
      </div>
    </a-modal>

    <!-- 多节点删除确认对话框 -->
    <a-modal
        v-model:visible="showMultiDeleteConfirm"
        @cancel="cancelMultiDelete"
        @before-ok="confirmMultiDelete"
        simple
        title="确认删除多个节点"
    >
      <div class="delete-confirm-content">
        <p>以下节点被其他节点引用，删除可能导致工作流错误。确定要删除吗？</p>

        <div v-if="multipleReferencingInfo && multipleReferencingInfo.length > 0" class="referencing-nodes">
          <div v-for="(info, index) in multipleReferencingInfo" :key="index" class="multi-node-reference">
            <p class="referencing-title">节点: {{ info.node.data.title || info.node.id }}</p>
            <p class="referencing-subtitle">被以下节点引用:</p>
            <ul class="referencing-list">
              <li v-for="node in info.referencingNodes" :key="node.id" class="referencing-item">
                <span class="node-title">{{ node.title }}</span>
                <span class="node-type">({{ getNodeTypeLabel(node.type) }})</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 保存成功提示 -->
    <a-message id="save-message" />

    <!-- 添加导入 JSON 对话框 -->
    <a-modal
        v-model:visible="showImportModal"
        title="导入工作流 JSON"
        @ok="handleImportJson"
        @cancel="closeImportDialog"
        :mask-closable="false"
    >
      <a-textarea
          v-model="importJson"
          placeholder="请粘贴工作流 JSON 数据"
          :auto-size="{ minRows: 10, maxRows: 20 }"
      />
      <template #footer>
        <a-button @click="closeImportDialog">取消</a-button>
        <a-button type="primary" @click="handleImportJson" :disabled="!importJson.trim()">导入</a-button>
      </template>
    </a-modal>

    <!-- 添加导入组件对话框 -->
    <a-modal
        v-model:visible="showImportNodeModal"
        title="导入节点组件"
        @ok="handleImportNode"
        @cancel="closeImportNodeDialog"
        :mask-closable="false"
    >
      <div class="import-node-info">
        <div class="import-node-tip">
          <p><b>说明：</b>导入的节点将添加到当前工作流中，不会清除现有数据</p>
          <p>支持以下格式：</p>
          <ul>
            <li>单个节点JSON</li>
            <li>节点数组JSON</li>
            <li>完整工作流JSON（只导入节点部分）</li>
          </ul>
        </div>
      </div>
      <a-textarea
          v-model="importNodeJson"
          placeholder="请粘贴节点 JSON 数据"
          :auto-size="{ minRows: 10, maxRows: 20 }"
      />
      <template #footer>
        <a-button @click="closeImportNodeDialog">取消</a-button>
        <a-button type="primary" @click="handleImportNode" :disabled="!importNodeJson.trim()">导入节点</a-button>
      </template>
    </a-modal>

    <!-- 返回确认对话框 -->
    <a-modal
        v-model:visible="showBackModal"
        @cancel="cancelBack"
        @before-ok="handleBack"
        simple
        title="确认返回首页"
    >
      <div class="back-confirm-content">
        <p>是否要清理暂存内容后返回首页？</p>
      </div>
      <template #footer>
        <a-button @click="cancelBack">取消</a-button>
        <a-button type="primary" @click="handleBack(true)">清理并返回首页</a-button>
        <a-button @click="handleBack(false)">直接返回首页</a-button>
      </template>
    </a-modal>

    <!-- 替换原有的抽屉组件 -->
    <LlmDebugDrawer
        v-model:visible="showDebugDrawer"
        :node="debuggingNode"
        @close="closeDebugDrawer"
        @submit="handleDebugSubmit"
    />

    <!-- 调试面板 -->
    <LlmDebugPanel
        v-model:visible="debugPanelVisible"
        :node-data="currentNodeData"
        @close="closeDebugPanel"
        @back="handleDebugBack"
    />

    <!-- 方法节点调试抽屉 -->
    <MethodDebugDrawer
        v-model:visible="methodDebugDrawerVisible"
        :node="currentMethodNode"
        @close="methodDebugDrawerVisible = false"
    />

    <!-- 添加节点详情组件 -->
    <NodeDetail
        v-if="showNodeDetail"
        :component-name="nodeDetailData.componentName"
        :trace-id="nodeDetailData.traceId"
        @close="closeNodeDetail"
    />

    <!-- 粘贴提示 -->
    <div class="paste-tip" v-if="showPasteTip">
      <div class="tip-content">
        <div class="tip-text">
          <p><b>快捷操作提示:</b></p>
          <p>• 直接拖拽空白处可以框选多个节点</p>
          <p>• 按住 <kbd>Ctrl</kbd> 或 <kbd>⌘</kbd> 可点选多个节点</p>
          <p>• 选中后可使用"导出选中节点"功能</p>
          <p>• 导出的节点可在其他工作流中导入</p>
        </div>
        <a-button class="close-tip-btn" type="text" size="mini" @click="closePasteTip">
          <template #icon><icon-close /></template>
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch, nextTick, h } from 'vue'
import { VueFlow, useVueFlow, Position } from '@vue-flow/core'
import { Background } from '@vue-flow/background'
import { Controls } from '@vue-flow/controls'
import { MiniMap } from '@vue-flow/minimap'
import '@vue-flow/core/dist/style.css'
import '@vue-flow/core/dist/theme-default.css'
import '@vue-flow/controls/dist/style.css'
import '@vue-flow/minimap/dist/style.css'
import CustomNode from './CustomNode.vue'
import { useRouter, useRoute } from 'vue-router'
import {cacheUpdateWorkflow, getWorkflowGraph } from '../api/workflow'
import { customAlphabet } from 'nanoid'
import { v4 as uuidv4 } from 'uuid'
import { Message, Modal, Button } from '@arco-design/web-vue'
import { IconLeft, IconSave, IconPlus, IconMinus, IconFullscreen, IconCheck, IconSearch, IconClose } from '@arco-design/web-vue/es/icon'
import {
  getWorkflowData,
  saveWorkflowData,
  saveCurrentWorkflowJson,
  saveCurrentWorkflowFull,
  saveCurrentZoom,
  getNewWorkflowData,
  saveNewWorkflowData,
  clearAllWorkflowData
} from '../utils/storage'

// 导入节点编辑器组件
import LlmNodeEditor from './editors/LlmNodeEditor.vue'
import AnswerNodeEditor from './editors/AnswerNodeEditor.vue'
import MethodNodeEditor from './editors/MethodNodeEditor.vue'
import VariableAggregatorNodeEditor from './editors/VariableAggregatorNodeEditor.vue'
import IfElseNodeEditor from './editors/IfElseNodeEditor.vue'
import CozeNodeEditor from './editors/CozeNodeEditor.vue'
import EndNodeEditor from './editors/EndNodeEditor.vue'
import LlmDebugDrawer from './drawer/LlmDebugDrawer.vue'
import LlmDebugPanel from './drawer/LlmDebugPanel.vue'
import MethodDebugDrawer from './drawer/MethodDebugDrawer.vue'
import NodeDetail from './NodeDetail.vue'

// 创建只使用数字的 nanoid 生成器
const nanoid = customAlphabet('0123456789', 12)

// 定义组件接收的属性
const props = defineProps({
  initialData: {
    type: Object,
    default: null
  }
})

const router = useRouter()
const route = useRoute()

const { fitView, onNodesChange, onEdgesChange, addNodes, project, viewport, getViewport, zoomTo, setViewport } = useVueFlow()

const elements = ref([])

// LLM 编辑器相关状态
const editingLlmNode = ref(null)
const showLlmEditor = ref(false)

// Answer 编辑器相关状态
const editingAnswerNode = ref(null)
const showAnswerEditor = ref(false)

// Method 编辑器相关状态
const editingMethodNode = ref(null)
const showMethodEditor = ref(false)

// 变量聚合节点编辑器相关状态
const editingVariableAggregatorNode = ref(null)
const showVariableAggregatorEditor = ref(false)

// If-Else 节点编辑器相关状态
const editingIfElseNode = ref(null)
const showIfElseEditor = ref(false)

// COZE 节点编辑器相关状态
const editingCozeNode = ref(null)
const showCozeEditor = ref(false)

// 添加复制成功状态
const copySuccess = ref(false)

// 搜索相关状态
const searchQuery = ref('')
const searchResults = ref([])
const hasSearched = ref(false)

// 添加面板折叠状态
const isPanelCollapsed = ref(true)

// 添加保存状态
const isSaving = ref(false)
const saveError = ref(null)

// 添加原始工作流数据引用，用于比较是否有改动
const originalWorkflowData = ref(null)

// 添加提示控制
const showTips = ref(true)

// 添加导入相关状态
const showImportModal = ref(false)
const importJson = ref('')

// 添加导入节点相关状态
const showImportNodeModal = ref(false)
const importNodeJson = ref('')

// 添加返回确认相关的响应式变量
const showBackModal = ref(false)

// 添加搜索面板折叠状态
const isSearchPanelCollapsed = ref(true)

// 添加调试相关的响应式变量
const showDebugDrawer = ref(false)
const debuggingNode = ref(null)

// 添加删除确认相关的响应式变量
const showDeleteConfirm = ref(false)
const nodeToDelete = ref(null)
const referencingNodesInfo = ref([])

// 添加多节点删除相关的响应式变量
const showMultiDeleteConfirm = ref(false)
const nodesToDelete = ref([])
const multipleReferencingInfo = ref([])

// 调试面板相关状态
const debugPanelVisible = ref(false)
const currentNodeData = ref(null)

// 方法调试相关状态
const methodDebugDrawerVisible = ref(false)
const currentMethodNode = ref(false)

// 添加节点详情相关状态
const showNodeDetail = ref(false)
const nodeDetailData = ref({
  componentName: '',
  traceId: ''
})

// 添加结束节点编辑器相关状态
const editingEndNode = ref(null)
const showEndEditor = ref(false)

// 切换面板折叠状态
const togglePanel = () => {
  isPanelCollapsed.value = !isPanelCollapsed.value
}

// 切换搜索面板折叠状态
const toggleSearchPanel = () => {
  isSearchPanelCollapsed.value = !isSearchPanelCollapsed.value
  // 展开面板时清空搜索内容和结果
  if (!isSearchPanelCollapsed.value) {
    searchQuery.value = ''
    searchResults.value = []
    hasSearched.value = false
  }
}

// 生成唯一的数字 ID
const generateUniqueId = () => {
  // 生成数字 ID
  return nanoid()
}

// 获取视口中心位置
const getViewportCenter = () => {
  // 获取当前视口信息
  const { x, y, zoom } = getViewport()

  // 获取容器尺寸
  const container = document.querySelector('.vue-flow')
  if (!container) return { x: 100, y: 100 }

  const width = container.clientWidth
  const height = container.clientHeight

  // 计算视口中心在画布坐标系中的位置
  const centerX = (width / 2 - x) / zoom
  const centerY = (height / 2 - y) / zoom

  return { x: centerX, y: centerY }
}

// 定义默认边的选项
const defaultEdgeOptions = {
  animated: true,
  style: { stroke: '#555', strokeWidth: 2 }
}

// 定义节点类型
const nodeTypes = [
  { type: 'start', label: '开始' },
  { type: 'llm', label: '大语言模型' },
  { type: 'if-else', label: '条件判断' },
  { type: 'method', label: '方法' },
  { type: 'answer', label: '回答' },
  { type: 'variable-aggregator', label: '变量聚合器' },
  { type: 'coze_bot', label: 'COZE 机器人' },
  { type: 'end', label: '结束' }
]

// 初始化工作流数据
const initializeWorkflow = () => {
  // 如果是新建工作流页面，先检查 localStorage
  if (route.path === '/new-workflow') {
    const storedWorkflow = getNewWorkflowData()
    if (storedWorkflow) {
      console.log('从 localStorage 获取到新建工作流数据:', storedWorkflow)
      transformWorkflowData(storedWorkflow)
      return
    }
    // 如果没有 localStorage 数据，使用空数据
    console.log('使用空工作流数据')
    transformWorkflowData({
      nodes: [],
      edges: []
    })
    return
  }

  // 优先使用传入的数据
  if (props.initialData) {
    console.log('使用传入的工作流数据:', props.initialData)
    transformWorkflowData(props.initialData)
    return
  }

  // 尝试从 localStorage 获取数据
  const storedWorkflow = getWorkflowData(clientKey.value)
  if (storedWorkflow) {
    console.log('从 localStorage 获取到工作流数据:', storedWorkflow)
    transformWorkflowData(storedWorkflow)
    return
  }

  // 使用空数据
  console.log('使用空工作流数据')
  transformWorkflowData({
    nodes: [],
    edges: []
  })
}

// 监听 initialData 变化
watch(() => props.initialData, (newData) => {
  if (newData) {
    console.log('工作流数据已更新:', newData)
    transformWorkflowData(newData)
  }
}, { deep: true })

onMounted(async () => {
  // 初始化工作流数据
  initializeWorkflow()

  // 获取客户端标识
  const clientKey = route.query.clientKey || ''

  if (clientKey) {
    try {
      // 获取原始工作流数据
      const response = await getWorkflowGraph(clientKey)
      if (response && response.code === 0 && response.data) {
        originalWorkflowData.value = JSON.parse(response.data.graphString || '{}')
      }
    } catch (err) {
      console.error('获取原始工作流数据失败:', err)
    }
  }

  // 检查是否有保存的编辑状态
  const savedWorkflow = localStorage.getItem('current_workflow_full')
  if (savedWorkflow) {
    try {
      const workflowData = JSON.parse(savedWorkflow)
      // 恢复编辑状态
      elements.value = [...workflowData.nodes, ...workflowData.edges]
      // 清除保存的状态，避免影响下次编辑
      localStorage.removeItem('current_workflow_full')
    } catch (err) {
      console.error('恢复编辑状态失败:', err)
    }
  }
})

const onPaneReady = () => {
  // 检查是否有保存的缩放比例
  const savedZoom = localStorage.getItem('current_zoom')
  if (savedZoom) {
    try {
      const zoom = parseFloat(savedZoom)
      // 先适应视图
      fitView({ padding: 0.2 })
      // 然后设置缩放比例
      setTimeout(() => {
        zoomTo(zoom, { duration: 300 })
        // 清除保存的缩放比例
        localStorage.removeItem('current_zoom')
      }, 300)
    } catch (err) {
      console.error('恢复缩放比例失败:', err)
      fitView({ padding: 0.2 })
    }
  } else {
    fitView({ padding: 0.2 })
  }
}

// 修改 transformWorkflowData 函数，接受工作流数据作为参数
const transformWorkflowData = (data) => {
  if (!data || !data.nodes) {
    console.error('无效的工作流数据:', data)
    return
  }

  const nodes = []
  const edges = []

  // 提取节点
  data.nodes.forEach(node => {
    nodes.push({
      id: node.id,
      type: 'custom',
      position: {
        x: node.position?.x || 0,
        y: node.position?.y || 0
      },
      data: node.data, // 直接使用完整的节点数据，而不是只提取部分字段
      // 添加连接点
      sourcePosition: Position.Right,
      targetPosition: Position.Left,
    })
  })

  // 提取边
  if (data.edges) {
    data.edges.forEach(edge => {
      edges.push({
        id: edge.id,
        source: edge.source,
        target: edge.target,
        sourceHandle: edge.sourceHandle,
        targetHandle: edge.targetHandle,
        type: edge.type || 'smoothstep',
        animated: true,
        data: edge.data || {},
        style: { stroke: '#555', strokeWidth: 2 }
      })
    })
  }

  // 更新元素
  elements.value = [...nodes, ...edges]

  // 适应视图
  setTimeout(() => {
    fitView({ padding: 0.2 })
  }, 100)
}

// 保存工作流
const saveWorkflow = () => {
  try {
    const workflowData = {
      nodes: elements.value.filter(el => !el.source).map(node => ({
        id: node.id,
        data: node.data,
        type: node.type,
        position: node.position
      })),
      edges: elements.value.filter(el => el.source).map(edge => ({
        id: edge.id,
        source: edge.source,
        target: edge.target,
        sourceHandle: edge.sourceHandle,
        targetHandle: edge.targetHandle,
        type: edge.type,
        data: edge.data
      }))
    }

    // 保存到 localStorage
    localStorage.setItem('workflowData', JSON.stringify(workflowData))

    alert('工作流已保存')
  } catch (err) {
    console.error('保存工作流失败:', err)
    alert('保存失败: ' + err.message)
  }
}

// 修改 addNode 函数
const addNode = (nodeType) => {
  console.log('添加节点:', nodeType)

  // 生成唯一的数字 ID
  const id = generateUniqueId()
  console.log('生成的唯一数字 ID:', id)

  // 获取视口中心位置
  const viewportCenter = getViewportCenter()
  console.log('视口中心位置:', viewportCenter)

  // 创建新节点
  const newNode = {
    id,
    type: 'custom',
    position: viewportCenter,
    data: {
      title: nodeType.type === 'start' ? '开始' :
          nodeType.type === 'end' ? '结束' :
              `新${nodeType.label}`,
      type: nodeType.type,
      desc: ''
    },
    // 添加连接点
    sourcePosition: Position.Right,
    targetPosition: Position.Left,
  }

  // 如果是 LLM 节点，添加特定属性
  if (nodeType.type === 'llm') {
    newNode.data.model = {
      mode: 'chat',
      name: '',
      provider: '',
      completion_params: {
        temperature: 0.7
      }
    }
    newNode.data.prompt_template = [{
      id: uuidv4(),
      role: 'system',
      text: ''
    }]
  }

  // 如果是 Answer 节点，添加特定属性
  if (nodeType.type === 'answer') {
    newNode.data.answer = ''
    newNode.data.intermediate = false
    newNode.data.variables = []
  }

  // 如果是 Method 节点，添加特定属性
  if (nodeType.type === 'method') {
    newNode.data.code = '\ndef main() -> dict:\n    # 处理逻辑\n    \n    # 返回结果\n    return {\n        "result": "",\n    }\n'
    newNode.data.component_name = ''
    newNode.data.outputs = {
      result: {
        type: 'string',
        children: null
      }
    }
    newNode.data.variables = []
  }

  // 如果是变量聚合节点，添加特定属性
  if (nodeType.type === 'variable-aggregator') {
    newNode.data.variables = []
    newNode.data.output_type = 'string'
    newNode.data.advanced_settings = {
      group_enabled: false,
      groups: []
    }
  }

  // 如果是 If-Else 节点，添加特定属性
  if (nodeType.type === 'if-else') {
    newNode.data.cases = [
      {
        id: 'true',
        case_id: 'true',
        conditions: [],
        logical_operator: 'and'
      }
    ]
  }

  // 如果是结束节点，可以添加特定属性
  if (nodeType.type === 'end') {
    // 结束节点不需要输出连接点
    newNode.sourcePosition = null;
  }

  console.log('新节点:', newNode)

  // 添加到画布
  elements.value = [...elements.value, newNode]

  console.log('当前元素数量:', elements.value.length)
}

// 连接节点
const onConnect = (connection) => {
  console.log('连接节点:', connection)

  // 获取源节点类型
  const sourceType = getNodeType(connection.source)

  // 处理 sourceHandle
  let sourceHandle = connection.sourceHandle || 'source'

  // 如果是 if-else 节点，尝试找到完整的 case_id
  if (sourceType === 'if-else') {
    const sourceNode = elements.value.find(el => el.id === connection.source)
    if (sourceNode && sourceNode.data.cases) {
      // 查找匹配的 case
      const matchingCase = sourceNode.data.cases.find(c =>
          c.case_id === sourceHandle || c.case_id.includes(sourceHandle)
      )
      if (matchingCase) {
        sourceHandle = matchingCase.case_id
      }
    }
  }

  // 处理 targetHandle
  let targetHandle = connection.targetHandle || 'target'
  // 如果是从节点ID-target格式，提取出实际的handle
  if (targetHandle.includes('-')) {
    targetHandle = targetHandle.split('-')[1]
  }

  // 创建新边，使用正确的 ID 格式
  const newEdge = {
    id: `${connection.source}-${sourceHandle}-${connection.target}-${targetHandle}`,
    source: connection.source,
    target: connection.target,
    sourceHandle: sourceHandle,
    targetHandle: targetHandle,
    type: 'custom',
    zIndex: 0,
    animated: true,
    style: {
      stroke: '#555',
      strokeWidth: 2
    },
    data: {
      sourceType: sourceType,
      targetType: getNodeType(connection.target),
      isInIteration: false
    }
  }

  // 添加到画布
  elements.value = [...elements.value, newEdge]
}

// 获取节点类型的辅助函数
const getNodeType = (nodeId) => {
  const node = elements.value.find(el => el.id === nodeId)
  return node ? node.data.type : 'unknown'
}

// 复制工作流 JSON 到剪贴板
const copyWorkflowJson = async () => {
  try {
    // 将当前工作流数据转换为精简格式
    const workflowJson = {
      edges: elements.value
          .filter(el => el.source)
          .map(edge => ({
            id: edge.id,
            data: edge.data,
            type: edge.type,
            source: edge.source,
            target: edge.target,
            zIndex: edge.zIndex,
            selected: edge.selected,
            sourceHandle: edge.sourceHandle,
            targetHandle: edge.targetHandle
          }))
          .sort((a, b) => a.id.localeCompare(b.id)),
      nodes: elements.value
          .filter(el => !el.source)
          .map(node => ({
            id: node.id,
            data: node.data,
            // type: node.type,
            position: node.position
          }))
          .sort((a, b) => a.id.localeCompare(b.id))
    }

    // 转换为格式化的 JSON 字符串
    const jsonString = JSON.stringify(workflowJson, null, 2)

    // 尝试使用现代 Clipboard API
    try {
      await navigator.clipboard.writeText(jsonString)
      console.log('使用 Clipboard API 复制成功')
    } catch (clipboardErr) {
      console.warn('Clipboard API 失败，尝试备用方法:', clipboardErr)
      
      // 备用方法：创建临时文本区域
      const textArea = document.createElement('textarea')
      textArea.value = jsonString
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      
      const successful = document.execCommand('copy')
      document.body.removeChild(textArea)
      
      if (!successful) {
        throw new Error('备用复制方法失败')
      }
      console.log('使用备用方法复制成功')
    }

    // 显示复制成功状态
    copySuccess.value = true
    setTimeout(() => {
      copySuccess.value = false
    }, 2000)

    console.log('工作流 JSON 已复制到剪贴板')
  } catch (err) {
    console.error('复制失败:', err)
    alert('复制失败，请检查浏览器权限')
  }
}

// LLM 节点编辑器相关方法
const openLlmEditor = (node) => {
  editingLlmNode.value = {
    id: node.id,
    data: JSON.parse(JSON.stringify(node.data)) // 深拷贝以避免直接修改
  }
  showLlmEditor.value = true
}

const closeLlmEditor = () => {
  showLlmEditor.value = false
  editingLlmNode.value = null
}

// 修改 saveLlmNode 函数
const saveLlmNode = (updatedNode) => {
  // 更新节点数据
  elements.value = elements.value.map(el => {
    if (el.id === updatedNode.id) {
      return {
        ...el,
        data: updatedNode.data
      }
    }
    return el
  })

  // 关闭编辑器
  closeLlmEditor()
}

// Answer 节点编辑器相关方法
const openAnswerEditor = (node) => {
  editingAnswerNode.value = {
    id: node.id,
    data: JSON.parse(JSON.stringify(node.data)) // 深拷贝以避免直接修改
  }
  showAnswerEditor.value = true
}

const closeAnswerEditor = () => {
  showAnswerEditor.value = false
  editingAnswerNode.value = null
}

const saveAnswerNode = (updatedNode) => {
  // 更新节点数据
  elements.value = elements.value.map(el => {
    if (el.id === updatedNode.id) {
      return {
        ...el,
        data: updatedNode.data
      }
    }
    return el
  })

  // 关闭编辑器
  closeAnswerEditor()
}

// Method 节点编辑器相关方法
const openMethodEditor = (node) => {
  editingMethodNode.value = {
    id: node.id,
    data: JSON.parse(JSON.stringify(node.data)) // 深拷贝以避免直接修改
  }
  showMethodEditor.value = true
}

const closeMethodEditor = () => {
  showMethodEditor.value = false
  editingMethodNode.value = null
}

const saveMethodNode = (updatedNode) => {
  // 更新节点数据
  elements.value = elements.value.map(el => {
    if (el.id === updatedNode.id) {
      return {
        ...el,
        data: updatedNode.data
      }
    }
    return el
  })

  // 关闭编辑器
  closeMethodEditor()
}

// 变量聚合节点编辑器相关方法
const openVariableAggregatorEditor = (node) => {
  editingVariableAggregatorNode.value = {
    id: node.id,
    data: JSON.parse(JSON.stringify(node.data)) // 深拷贝以避免直接修改
  }
  showVariableAggregatorEditor.value = true
}

const closeVariableAggregatorEditor = () => {
  showVariableAggregatorEditor.value = false
  editingVariableAggregatorNode.value = null
}

const saveVariableAggregatorNode = (updatedNode) => {
  // 更新节点数据
  elements.value = elements.value.map(el => {
    if (el.id === updatedNode.id) {
      return {
        ...el,
        data: updatedNode.data
      }
    }
    return el
  })

  // 关闭编辑器
  closeVariableAggregatorEditor()
}

// If-Else 节点编辑器相关方法
const openIfElseEditor = (node) => {
  editingIfElseNode.value = {
    id: node.id,
    data: JSON.parse(JSON.stringify(node.data)) // 深拷贝以避免直接修改
  }
  showIfElseEditor.value = true
}

const closeIfElseEditor = () => {
  showIfElseEditor.value = false
  editingIfElseNode.value = null
}

const saveIfElseNode = (updatedNode) => {
  // 更新节点数据
  elements.value = elements.value.map(el => {
    if (el.id === updatedNode.id) {
      return {
        ...el,
        data: updatedNode.data
      }
    }
    return el
  })

  // 关闭编辑器
  closeIfElseEditor()
}

// 删除节点
const deleteNode = (nodeId) => {
  console.log('准备删除节点 - 调试:', nodeId)

  // 找到要删除的节点
  const node = elements.value.find(el => el.id === nodeId)
  if (!node) {
    console.error('找不到要删除的节点:', nodeId)
    return
  }

  // 检查是否有其他节点引用了此节点
  const result = checkNodeReferencesInJson(nodeId)
  console.log('节点引用检查结果:', result)

  if (result.hasReferences) {
    // 显示确认对话框
    nodeToDelete.value = node
    referencingNodesInfo.value = result.referencingNodes
    showDeleteConfirm.value = true
  } else {
    // 直接删除节点
    console.log('节点无引用，直接删除:', nodeId)
    performDeleteNode(nodeId)
  }
}

// 取消删除
const cancelDelete = () => {
  showDeleteConfirm.value = false
  nodeToDelete.value = null
}

// 确认删除
const confirmDelete = (done) => {
  if (nodeToDelete.value) {
    performDeleteNode(nodeToDelete.value.id)
    nodeToDelete.value = null
  }
  done()
}

// 通过检查JSON中是否包含节点ID来检测引用关系
const checkNodeReferencesInJson = (nodeId) => {
  // 创建一个临时的元素数组，排除要删除的节点
  const tempElements = elements.value.filter(el => el.id !== nodeId && !el.source && !el.target)

  // 将临时数组转换为JSON字符串
  const jsonString = JSON.stringify(tempElements)

  console.log('检查节点引用，nodeId:', nodeId)

  // 检查JSON字符串中是否包含节点ID的各种引用模式
  // 1. 检查变量引用，如 "nodeId": "123456" 或 "id": "123456"
  const pattern1 = new RegExp(`"(nodeId|id|variable|source|target)"\\s*:\\s*"${nodeId}"`, 'g')

  // 2. 检查Answer节点中的模板引用 {{123456.
  const pattern2 = new RegExp(`{{${nodeId}\\.`, 'g')

  // 3. 检查IF-ELSE节点的条件引用 "leftNodeId": "123456" 或 "rightNodeId": "123456"
  const pattern3 = new RegExp(`"(leftNodeId|rightNodeId)"\\s*:\\s*"${nodeId}"`, 'g')

  // 4. 检查数组中的引用 ["123456"]
  const pattern4 = new RegExp(`["']${nodeId}["']`, 'g')

  // 5. 检查更宽泛的引用模式
  const pattern5 = new RegExp(`${nodeId}`, 'g')

  // 排除节点自身的ID引用
  const selfIdPattern = new RegExp(`"id"\\s*:\\s*"${nodeId}"`, 'g')

  // 检查各种模式
  const match1 = pattern1.test(jsonString)
  const match2 = pattern2.test(jsonString)
  const match3 = pattern3.test(jsonString)
  const match4 = pattern4.test(jsonString)
  const match5 = pattern5.test(jsonString) && !selfIdPattern.test(jsonString)

  console.log('引用检查结果:', {
    match1,
    match2,
    match3,
    match4,
    match5,
    jsonLength: jsonString.length
  })

  const hasReference = match1 || match2 || match3 || match4 || match5

  // 查找引用此节点的节点
  const referencingNodes = []

  if (hasReference) {
    // 遍历所有节点，检查它们的JSON表示是否包含对当前节点的引用
    tempElements.forEach(node => {
      if (!node.source) { // 只检查节点，不检查边
        const nodeJson = JSON.stringify(node)

        // 重置正则表达式状态
        pattern1.lastIndex = 0
        pattern2.lastIndex = 0
        pattern3.lastIndex = 0
        pattern4.lastIndex = 0
        pattern5.lastIndex = 0
        selfIdPattern.lastIndex = 0

        // 检查节点是否包含对当前节点的引用
        const hasNodeReference =
            nodeJson.includes(nodeId) &&
            (
                pattern1.test(nodeJson) ||
                pattern2.test(nodeJson) ||
                pattern3.test(nodeJson) ||
                pattern4.test(nodeJson) ||
                (pattern5.test(nodeJson) && !selfIdPattern.test(nodeJson))
            )

        if (hasNodeReference) {
          referencingNodes.push({
            id: node.id,
            type: node.data.type,
            title: node.data.title || node.id
          })
        }
      }
    })

    console.log(`节点 ${nodeId} 被以下节点引用:`, referencingNodes)

    // 如果没有找到引用节点但检测到了引用，尝试更简单的方法
    if (referencingNodes.length === 0) {
      console.log('未找到引用节点，尝试更简单的检测方法')

      // 简单地检查每个节点的JSON是否包含节点ID
      tempElements.forEach(node => {
        if (!node.source) { // 只检查节点，不检查边
          const nodeJson = JSON.stringify(node)
          if (nodeJson.includes(nodeId)) {
            referencingNodes.push({
              id: node.id,
              type: node.data.type,
              title: node.data.title || node.id
            })
            console.log(`发现可能的引用节点: ${node.id}`)
          }
        }
      })
    }

    // 如果仍然没有找到引用节点，创建一个通用的引用节点
    if (referencingNodes.length === 0) {
      referencingNodes.push({
        id: 'unknown',
        type: 'unknown',
        title: '未知节点 (可能在复杂数据结构中)'
      })
    }
  }

  return {
    hasReferences: hasReference,
    referencingNodes: referencingNodes
  }
}

// 执行删除节点操作
const performDeleteNode = (nodeId) => {
  console.log('执行删除节点:', nodeId)

  // 找到要删除的节点
  const nodeIndex = elements.value.findIndex(el => el.id === nodeId)
  if (nodeIndex === -1) return

  // 找到与该节点相关的所有边的索引
  const edgeIndexesToRemove = elements.value
      .map((el, index) => (el.source === nodeId || el.target === nodeId) ? index : -1)
      .filter(index => index !== -1)
      .sort((a, b) => b - a) // 从大到小排序，以便从后往前删除

  // 创建新的元素数组，排除要删除的节点和边
  const newElements = [...elements.value]

  // 先删除边（从后往前删除，避免索引变化）
  edgeIndexesToRemove.forEach(index => {
    newElements.splice(index, 1)
  })

  // 再删除节点
  newElements.splice(elements.value.findIndex(el => el.id === nodeId), 1)

  // 更新元素数组
  elements.value = newElements

  console.log('删除后的元素:', elements.value)
}

// COZE 节点编辑器相关方法
const openCozeEditor = (node) => {
  editingCozeNode.value = {
    id: node.id,
    data: JSON.parse(JSON.stringify(node.data)) // 深拷贝以避免直接修改
  }
  showCozeEditor.value = true
}

const closeCozeEditor = () => {
  showCozeEditor.value = false
  editingCozeNode.value = null
}

const saveCozeNode = (updatedNode) => {
  // 查找并更新节点
  const nodeIndex = elements.value.findIndex(el => el.id === updatedNode.id)
  if (nodeIndex !== -1) {
    elements.value[nodeIndex].data = updatedNode.data
  }
  closeCozeEditor()
}

// 获取节点类型标签
const getNodeTypeLabel = (type) => {
  const nodeType = nodeTypes.find(nt => nt.type === type)
  return nodeType ? nodeType.label : type
}

// 处理搜索
const handleSearch = () => {
  const query = searchQuery.value.trim().toLowerCase()
  hasSearched.value = true

  if (!query) {
    searchResults.value = []
    return
  }

  searchResults.value = elements.value
      .filter(el => !el.source) // 只搜索节点，不搜索边
      .filter(node => {
        const title = node.data?.title?.toLowerCase() || ''
        const type = getNodeTypeLabel(node.data?.type)?.toLowerCase() || ''
        const id = node.id?.toLowerCase() || ''
        return title.includes(query) || type.includes(query) || id.includes(query)
      })

  // 如果只有一个结果，自动定位到该节点
  if (searchResults.value.length === 1) {
    focusOnNode(searchResults.value[0])
  }
}

// 聚焦到节点
const focusOnNode = (node) => {
  console.log('开始定位节点:', node)

  // 不再清空搜索结果
  // searchResults.value = []
  // searchQuery.value = ''
  // hasSearched.value = false

  // 获取当前节点的实例
  const currentNode = elements.value.find(el => el.id === node.id)
  if (!currentNode) {
    console.error('未找到节点:', node.id)
    return
  }

  console.log('找到节点:', currentNode)

  // 获取画布容器
  const container = document.querySelector('.vue-flow')
  if (!container) {
    console.error('未找到画布容器')
    return
  }

  // 计算视口中心位置
  const containerWidth = container.clientWidth
  const containerHeight = container.clientHeight

  // 计算目标缩放级别
  const targetZoom = 1.2 // 使用固定的缩放级别

  // 计算目标位置（使节点位于视口中心）
  const targetX = -(currentNode.position.x * targetZoom - containerWidth / 2)
  const targetY = -(currentNode.position.y * targetZoom - containerHeight / 2)

  console.log('目标位置:', { x: targetX, y: targetY, zoom: targetZoom })

  // 设置视口
  setViewport(
      {
        x: targetX,
        y: targetY,
        zoom: targetZoom
      },
      { duration: 800 }
  )

  // 高亮显示节点
  setTimeout(() => {
    const nodeElement = document.querySelector(`[data-id="${node.id}"]`)
    if (nodeElement) {
      nodeElement.classList.add('highlight-node')
      setTimeout(() => {
        nodeElement.classList.remove('highlight-node')
      }, 2000)
    }
  }, 800)

  // 显示提示消息
  Message.success({
    content: `已定位到节点：${node.data.title}`,
    duration: 2000
  })

  // 如果搜索面板是收起的，展开它
  if (isSearchPanelCollapsed.value) {
    isSearchPanelCollapsed.value = false
  }
}

// 修改返回方法
const goBack = () => {
  showBackModal.value = true
}

// 处理返回相关的操作
const handleBack = (shouldClearCache) => {
  const clientKey = route.query.clientKey || ''
  if (shouldClearCache) {
    clearAllWorkflowData(clientKey)
  }
  showBackModal.value = false
  // 直接返回首页，不再跳转到详情页
  router.push('/')
}

// 取消返回
const cancelBack = () => {
  showBackModal.value = false
}

// 导出工作流 JSON 数据
const exportWorkflowJson = () => {
  return {
    nodes: elements.value.filter(el => !el.source).map(node => ({
      id: node.id,
      data: node.data,
      type: node.type,
      position: node.position
    })),
    edges: elements.value.filter(el => el.source).map(edge => ({
      id: edge.id,
      source: edge.source,
      target: edge.target,
      type: edge.type,
      sourceHandle: edge.sourceHandle,
      targetHandle: edge.targetHandle
    }))
  }
}

// 修改检查工作流是否有改动的方法，确保正确比较
const hasWorkflowChanged = () => {
  if (!originalWorkflowData.value) {
    console.log('没有原始数据，默认认为有改动')
    return true
  }

  try {
    // 获取当前工作流数据
    const currentWorkflow = exportWorkflowJson()

    // 深度比较两个对象，忽略位置信息等非关键属性
    const compareObjects = (obj1, obj2) => {
      // 如果两个对象引用相同，则它们相等
      if (obj1 === obj2) return true

      // 如果其中一个不是对象或为null，则比较值
      if (typeof obj1 !== 'object' || obj1 === null ||
          typeof obj2 !== 'object' || obj2 === null) {
        return obj1 === obj2
      }

      // 获取两个对象的所有键
      const keys1 = Object.keys(obj1).filter(k => !['position', 'positionAbsolute', 'selected', 'dragging'].includes(k))
      const keys2 = Object.keys(obj2).filter(k => !['position', 'positionAbsolute', 'selected', 'dragging'].includes(k))

      // 如果键的数量不同，则对象不相等
      if (keys1.length !== keys2.length) return false

      // 检查所有键值对
      return keys1.every(key => {
        if (!keys2.includes(key)) return false
        return compareObjects(obj1[key], obj2[key])
      })
    }

    // 比较节点和边
    const originalNodes = originalWorkflowData.value.nodes || []
    const originalEdges = originalWorkflowData.value.edges || []
    const currentNodes = currentWorkflow.nodes || []
    const currentEdges = currentWorkflow.edges || []

    // 如果节点或边的数量不同，则有改动
    if (originalNodes.length !== currentNodes.length || originalEdges.length !== currentEdges.length) {
      console.log('节点或边的数量不同，有改动')
      return true
    }

    // 检查每个节点是否有改动
    const nodesChanged = !originalNodes.every((node, index) => {
      return compareObjects(node, currentNodes[index])
    })

    // 检查每个边是否有改动
    const edgesChanged = !originalEdges.every((edge, index) => {
      return compareObjects(edge, currentEdges[index])
    })

    const hasChanged = nodesChanged || edgesChanged
    console.log('工作流是否有改动:', hasChanged)
    return hasChanged
  } catch (err) {
    console.error('比较工作流数据失败:', err)
    return true // 出错时默认认为有改动
  }
}

// 修改暂存功能，使用精简格式
const handleSaveWorkflowData = async () => {
  if (isSaving.value) return

  try {
    // 检查是否有改动
    console.log('检查工作流是否有改动...')
    if (!hasWorkflowChanged()) {
      console.log('工作流没有改动，无需保存')
      alert('工作流没有改动，无需保存')
      return
    }

    console.log('工作流有改动，开始保存...')
    isSaving.value = true
    saveError.value = null

    // 获取当前工作流 JSON 数据
    const workflowData = exportWorkflowJson()

    // 获取客户端标识
    const clientKey = route.query.clientKey || ''

    // 保存到 localStorage
    if (route.path === '/new-workflow') {
      saveNewWorkflowData(workflowData)
    } else {
      saveWorkflowData(workflowData, clientKey)
    }

    // 更新原始数据引用
    originalWorkflowData.value = JSON.parse(JSON.stringify(workflowData))

    // 显示保存成功提示
    Message.success('工作流已暂存到本地')
  } catch (err) {
    console.error('保存工作流数据失败:', err)
    saveError.value = err.message || '保存失败，请稍后重试'
    Message.error(saveError.value)
  } finally {
    isSaving.value = false
  }
}

// 处理节点点击
const onNodeClick = (event, node) => {
  // 检查是否按下了Ctrl/⌘键(多选)
  const isMultiSelectKeyPressed = event.ctrlKey || event.metaKey || event.shiftKey

  if (!isMultiSelectKeyPressed) {
    // 单选模式：取消其他节点的选择
    elements.value = elements.value.map(el => ({
      ...el,
      selected: el.id === node.id && !el.source
    }))
  } else {
    // 多选模式：切换当前节点的选择状态，保持其他节点状态不变
    elements.value = elements.value.map(el => {
      if (el.id === node.id && !el.source) {
        return {
          ...el,
          selected: !el.selected
        }
      }
      return el
    })
  }

  console.log('节点被点击:', node.id, '选中状态:', node.selected)
}

// 处理边点击
const onEdgeClick = (event, edge) => {
  console.log('连线被点击:', edge.id)
  // 显示提示
  showTips.value = true
  // 5秒后自动隐藏提示
  setTimeout(() => {
    showTips.value = false
  }, 5000)
}

// 修改对比 JSON 功能
const compareWorkflowJson = () => {
  try {
    // 创建一个函数来确保对象字段顺序一致
    const createOrderedEdge = (edge) => {
      return {
        id: edge.id,
        source: edge.source,
        target: edge.target,
        sourceHandle: edge.sourceHandle,
        targetHandle: edge.targetHandle,
        type: edge.type
      };
    };

    const createOrderedNode = (node) => {
      return {
        id: node.id,
        data: node.data,
        position: node.position
      };
    };

    // 获取当前工作流数据
    const workflowData = {
      nodes: elements.value
          .filter(el => !el.source)
          .map(node => createOrderedNode(node))
          .sort((a, b) => a.id.localeCompare(b.id)),

      edges: elements.value
          .filter(el => el.source)
          .map(edge => createOrderedEdge(edge))
          .sort((a, b) => a.id.localeCompare(b.id))
    }

    // 获取客户端标识
    const clientKey = route.query.clientKey || ''

    if (!clientKey) {
      alert('缺少客户端标识(clientKey)，无法进行对比')
      return
    }

    // 保存当前编辑状态到 localStorage
    saveCurrentWorkflowJson(workflowData, clientKey)

    // 保存完整的工作流数据，用于返回时恢复
    saveCurrentWorkflowFull({
      nodes: elements.value.filter(el => !el.source),
      edges: elements.value.filter(el => el.source)
    }, clientKey)

    // 只保存当前缩放比例
    saveCurrentZoom(viewport.value.zoom, clientKey)

    // 跳转到对比页面
    router.push({
      path: '/compare',
      query: {
        clientKey: clientKey,
        source: 'editor' // 标记来源为编辑器
      }
    })
  } catch (err) {
    console.error('准备对比数据失败:', err)
    alert('准备对比数据失败: ' + (err.message || '未知错误'))
  }
}

// 缩放控制函数 - 调整缩放范围
const zoomIn = () => {
  // 限制最大缩放为 200%
  const newZoom = Math.min(viewport.value.zoom * 1.25, 2)
  zoomTo(newZoom, { duration: 300 })
}

const zoomOut = () => {
  // 限制最小缩放为 10%
  const newZoom = Math.max(viewport.value.zoom * 0.8, 0.1)
  zoomTo(newZoom, { duration: 300 })
}

const fitToView = () => {
  // 确保适应视图时的缩放在合理范围内
  fitView({
    padding: 0.2,
    duration: 500,
    minZoom: 0.1,  // 最小缩放为 10%
    maxZoom: 2     // 最大缩放为 200%
  })
}

// 显示导入对话框
const showImportDialog = () => {
  showImportModal.value = true
  importJson.value = ''
}

// 关闭导入对话框
const closeImportDialog = () => {
  showImportModal.value = false
  importJson.value = ''
}

// 处理导入 JSON
const handleImportJson = () => {
  try {
    const parsedData = JSON.parse(importJson.value)

    // 验证数据格式
    if (!parsedData.nodes || !Array.isArray(parsedData.nodes)) {
      throw new Error('无效的工作流数据格式：缺少 nodes 数组')
    }

    // 如果是新建工作流页面，保存到 newWorkflowData
    if (route.path === '/new-workflow') {
      localStorage.setItem('newWorkflowData', JSON.stringify(parsedData))
    } else {
      localStorage.setItem('workflowData', JSON.stringify(parsedData))
    }

    // 更新工作流视图
    transformWorkflowData(parsedData)

    // 关闭对话框
    closeImportDialog()

    // 显示成功提示
    Message.success('工作流数据导入成功')
  } catch (err) {
    console.error('导入 JSON 失败:', err)
    Message.error(`导入失败: ${err.message}`)
  }
}

// 修改 copyNode 函数
const copyNode = (nodeId) => {
  console.log('复制节点:', nodeId)

  // 找到要复制的节点
  const sourceNode = elements.value.find(el => el.id === nodeId)
  if (!sourceNode) return

  // 生成新的节点 ID
  const newId = generateUniqueId()

  // 创建新节点，深拷贝原节点数据
  const newNode = {
    id: newId,
    type: sourceNode.type,
    position: {
      x: sourceNode.position.x + 50, // 向右偏移
      y: sourceNode.position.y + 50  // 向下偏移
    },
    data: JSON.parse(JSON.stringify(sourceNode.data)), // 深拷贝节点数据
    sourcePosition: sourceNode.sourcePosition,
    targetPosition: sourceNode.targetPosition
  }

  // 如果是 LLM 节点，需要重新生成 prompt_template 中的 id
  if (newNode.data.type === 'llm' && newNode.data.prompt_template) {
    newNode.data.prompt_template = newNode.data.prompt_template.map(item => ({
      ...item,
      id: uuidv4()
    }))
  }

  // 修改节点标题，添加"副本"标识
  newNode.data.title = `${newNode.data.title} (副本)`

  // 添加到画布
  elements.value = [...elements.value, newNode]

  // 自动选中新节点
  elements.value = elements.value.map(el => ({
    ...el,
    selected: el.id === newId
  }))

  // 定位到新节点
  nextTick(() => {
    fitView({
      padding: 0.5,
      duration: 800,
      includeHiddenNodes: false,
      nodes: [newNode]
    })
  })

  console.log('节点复制完成:', newNode)
}

// 删除 If-Else 节点的边
const removeIfElseEdges = ({ nodeId, caseId }) => {
  // 找到与该节点和分支相关的所有边的索引
  const edgeIndexesToRemove = elements.value
      .map((el, index) =>
          (el.source === nodeId && el.sourceHandle === caseId) ? index : -1
      )
      .filter(index => index !== -1)
      .sort((a, b) => b - a) // 从大到小排序，以便从后往前删除

  // 创建新的元素数组
  const newElements = [...elements.value]

  // 删除边（从后往前删除，避免索引变化）
  edgeIndexesToRemove.forEach(index => {
    newElements.splice(index, 1)
  })

  // 更新元素数组
  elements.value = newElements
}

// 修改调试相关的方法
const openDebugDrawer = (node) => {
  debuggingNode.value = node
  showDebugDrawer.value = true
}

const closeDebugDrawer = () => {
  showDebugDrawer.value = false
  debuggingNode.value = null
}

const handleDebugSubmit = (updatedNode) => {
  // 更新节点数据
  elements.value = elements.value.map(el => {
    if (el.id === updatedNode.id) {
      return {
        ...el,
        data: updatedNode.data
      }
    }
    return el
  })

  // 关闭调试抽屉
  showDebugDrawer.value = false

  // 设置当前节点数据并打开调试面板
  currentNodeData.value = updatedNode
  debugPanelVisible.value = true
}

const closeDebugPanel = () => {
  debugPanelVisible.value = false
  currentNodeData.value = null
}

// 添加调试面板返回处理方法
const handleDebugBack = () => {
  // 关闭调试面板
  debugPanelVisible.value = false

  // 重新打开调试抽屉，并保持当前节点数据
  debuggingNode.value = currentNodeData.value
  showDebugDrawer.value = true

  // 清除当前节点数据
  currentNodeData.value = null
}

// 打开方法调试抽屉
const openMethodDebugDrawer = ({ id, data }) => {
  currentMethodNode.value = elements.value.find(node => node.id === id)
  methodDebugDrawerVisible.value = true
}

// 添加节点详情相关方法
const openNodeDetail = ({ data }) => {
  nodeDetailData.value = {
    componentName: data.component_name,
    traceId: data.traceId
  }
  showNodeDetail.value = true
}

const closeNodeDetail = () => {
  showNodeDetail.value = false
  nodeDetailData.value = {
    componentName: '',
    traceId: ''
  }
}

// 添加结束节点编辑器相关方法
const openEndEditor = (node) => {
  editingEndNode.value = {
    id: node.id,
    data: JSON.parse(JSON.stringify(node.data)) // 深拷贝以避免直接修改
  }
  showEndEditor.value = true
}

const closeEndEditor = () => {
  showEndEditor.value = false
  editingEndNode.value = null
}

const saveEndNode = (updatedNode) => {
  // 更新节点数据
  elements.value = elements.value.map(el => {
    if (el.id === updatedNode.id) {
      return {
        ...el,
        data: updatedNode.data
      }
    }
    return el
  })

  // 关闭编辑器
  closeEndEditor()
}

// 显示导入节点对话框
const showImportNodeDialog = () => {
  showImportNodeModal.value = true
  importNodeJson.value = ''
}

// 关闭导入节点对话框
const closeImportNodeDialog = () => {
  showImportNodeModal.value = false
  importNodeJson.value = ''
}

// 从导入数据创建新节点
const createNodesFromPastedData = (importedData, options = {}) => {
  const { keepOriginalIds = true } = options; // 默认保留原始ID
  let nodesToAdd = []
  let edgesToAdd = []

  // 获取视口中心位置
  const centerPos = getViewportCenter()

  // 处理不同格式的数据
  if (importedData.nodes && Array.isArray(importedData.nodes)) {
    // 完整工作流数据，处理节点部分
    nodesToAdd = importedData.nodes

    // 如果有边数据，也一并处理
    if (importedData.edges && Array.isArray(importedData.edges)) {
      edgesToAdd = importedData.edges
    }
  } else if (Array.isArray(importedData)) {
    // 节点数组
    nodesToAdd = importedData
  } else if (importedData.data) {
    // 单个节点
    nodesToAdd = [importedData]
  }

  // 检查是否已存在导入节点的ID，避免ID冲突
  const existingIds = elements.value.filter(el => !el.source).map(node => node.id);

  // 创建ID映射表，用于后续更新边的引用
  const idMapping = {};

  // 创建新节点并添加到画布
  const newNodes = nodesToAdd.map((node, index) => {
    // 决定是否使用原始ID
    let nodeId;
    if (keepOriginalIds && node.id && !existingIds.includes(node.id)) {
      // 使用原始ID
      nodeId = node.id;
      console.log(`保留原始节点ID: ${nodeId}`);
    } else {
      // 生成新的唯一ID
      nodeId = generateUniqueId();
      // 记录ID映射，用于更新边
      if (node.id) {
        idMapping[node.id] = nodeId;
      }
      console.log(`生成新节点ID: ${nodeId}，${keepOriginalIds ? '(原ID已存在)' : '(未保留原ID)'}`);
    }

    // 创建新节点
    return {
      id: nodeId,
      type: 'custom',
      position: node.position ? { ...node.position } : {
        // 如果没有原始位置，在视口中心附近创建，多个节点时错开位置
        x: centerPos.x + (index * 30),
        y: centerPos.y + (index * 30)
      },
      data: { ...node.data }, // 深拷贝节点数据
      sourcePosition: Position.Right,
      targetPosition: Position.Left,
    };
  });

  // 添加节点到画布
  elements.value = [...elements.value, ...newNodes];

  // 如果有边数据，处理边
  if (edgesToAdd.length > 0) {
    // 创建新边，更新引用的节点ID
    const newEdges = edgesToAdd.map(edge => {
      // 更新源节点ID和目标节点ID（如果有映射）
      const sourceId = idMapping[edge.source] || edge.source;
      const targetId = idMapping[edge.target] || edge.target;

      return {
        id: `${sourceId}-${edge.sourceHandle || 'source'}-${targetId}-${edge.targetHandle || 'target'}`,
        source: sourceId,
        target: targetId,
        sourceHandle: edge.sourceHandle,
        targetHandle: edge.targetHandle,
        type: edge.type || 'smoothstep',
        animated: edge.animated || true,
        data: edge.data || {},
        style: { stroke: '#555', strokeWidth: 2 }
      };
    });

    // 添加边到画布
    elements.value = [...elements.value, ...newEdges];

    // 显示提示消息
    Message.success({
      content: `已添加 ${newNodes.length} 个节点和 ${newEdges.length} 条边${keepOriginalIds ? '，保留原始ID和位置' : ''}`,
      duration: 2000
    });
  } else {
    // 显示提示消息
    Message.success({
      content: `已添加 ${newNodes.length} 个节点${keepOriginalIds ? '，保留原始ID和位置' : ''}`,
      duration: 2000
    });
  }
}

// 处理导入节点
const handleImportNode = () => {
  try {
    const parsedData = JSON.parse(importNodeJson.value)

    // 检查数据是否是有效的节点数据
    if (isValidNodeData(parsedData)) {
      // 使用之前添加的函数创建节点，并保留原始ID
      createNodesFromPastedData(parsedData, { keepOriginalIds: true })

      // 关闭对话框
      closeImportNodeDialog()

      // 显示成功提示
      Message.success('节点导入成功')
    } else {
      throw new Error('无效的节点数据格式')
    }
  } catch (err) {
    console.error('导入节点失败:', err)
    Message.error(`导入失败: ${err.message}`)
  }
}

// 验证节点数据格式
const isValidNodeData = (data) => {
  // 检查是否是完整的工作流数据（包含nodes和edges）
  if (data.nodes && Array.isArray(data.nodes)) {
    return true
  }

  // 检查是否是节点+边格式
  if (data.nodes && data.edges && Array.isArray(data.nodes) && Array.isArray(data.edges)) {
    return true
  }

  // 检查是否是单个节点数据（包含data和type字段）
  if (data.data && (data.type || data.id)) {
    return true
  }

  // 检查是否是节点数组
  if (Array.isArray(data) && data.length > 0 && data[0].data) {
    return true
  }

  return false
}

// 添加选中节点数量计算属性
const selectedNodesCount = computed(() => {
  return elements.value.filter(el => !el.source && el.selected).length
})

// 导出选中的节点
const exportSelectedNodes = async () => {
  try {
    // 获取所有选中的节点
    const selectedNodes = elements.value.filter(el => !el.source && el.selected)

    if (selectedNodes.length === 0) {
      Message.warning('请先选择要导出的节点')
      return
    }

    // 获取选中节点的ID列表
    const selectedNodeIds = selectedNodes.map(node => node.id)

    // 准备节点数据，保留原始ID和位置信息
    const nodesToExport = selectedNodes.map(node => ({
      id: node.id, // 保留原始ID
      data: { ...node.data },
      position: { ...node.position } // 保留原始位置信息
    }))

    // 查找选中节点之间的连接边
    const edgesToExport = elements.value
        .filter(el => el.source && el.target) // 过滤出所有边
        .filter(edge =>
            // 选择源节点和目标节点都在选中列表中的边
            selectedNodeIds.includes(edge.source) && selectedNodeIds.includes(edge.target)
        )
        .map(edge => ({
          id: edge.id,
          source: edge.source,
          target: edge.target,
          sourceHandle: edge.sourceHandle,
          targetHandle: edge.targetHandle,
          type: edge.type || 'smoothstep',
          animated: edge.animated,
          data: edge.data || {}
        }))

    // 准备完整的导出数据
    const exportData = {
      nodes: nodesToExport,
      edges: edgesToExport
    }

    // 转换为 JSON 字符串
    const jsonString = JSON.stringify(exportData, null, 2)

    // 尝试使用现代 Clipboard API
    let copySuccess = false
    try {
      await navigator.clipboard.writeText(jsonString)
      copySuccess = true
      console.log('使用 Clipboard API 导出成功')
    } catch (clipboardErr) {
      console.warn('Clipboard API 失败，尝试备用方法:', clipboardErr)
      
      // 备用方法：创建临时文本区域
      const textArea = document.createElement('textarea')
      textArea.value = jsonString
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      
      try {
        const successful = document.execCommand('copy')
        if (successful) {
          copySuccess = true
          console.log('使用备用方法导出成功')
        } else {
          console.error('备用复制方法执行失败')
        }
      } catch (execErr) {
        console.error('execCommand 执行失败:', execErr)
      } finally {
        document.body.removeChild(textArea)
      }
    }

    // 显示成功或失败消息
    if (copySuccess) {
      Message.success({
        content: `已导出 ${nodesToExport.length} 个节点和 ${edgesToExport.length} 条边到剪贴板`,
        duration: 2000
      })
    } else {
      // 如果两种方法都失败，提供下载选项
      const blob = new Blob([jsonString], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `workflow_nodes_${new Date().getTime()}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      
      Message.info({
        content: `已下载 ${nodesToExport.length} 个节点和 ${edgesToExport.length} 条边到文件`,
        duration: 2000
      })
    }
  } catch (err) {
    console.error('导出选中节点失败:', err)
    Message.error('导出失败: ' + err.message)
  }
}

// 在粘贴提示中添加多选信息
const showPasteTip = ref(true)
const closePasteTip = () => {
  showPasteTip.value = false
  // 保存到本地存储
  localStorage.setItem('paste_tip_closed', 'true')
}

// 处理键盘删除节点
const handleNodesDelete = (event) => {
  // 阻止默认删除行为
  event.preventDefault()
  console.log('键盘删除事件被触发')

  // 获取选中的节点
  const selectedNodes = elements.value.filter(el => !el.source && el.selected)
  console.log('选中的节点数量:', selectedNodes.length)

  if (selectedNodes.length === 0) return

  // 如果只有一个节点，使用我们的删除逻辑
  if (selectedNodes.length === 1) {
    console.log('删除单个节点:', selectedNodes[0].id)
    deleteNode(selectedNodes[0].id)
    return
  }

  // 如果有多个节点，检查是否有被引用的节点
  const referencedNodes = []
  const referencingInfo = []

  // 检查每个选中的节点是否被引用
  for (const node of selectedNodes) {
    const result = checkNodeReferencesInJson(node.id)
    if (result.hasReferences) {
      referencedNodes.push(node)
      referencingInfo.push({
        node: node,
        referencingNodes: result.referencingNodes
      })
    }
  }

  console.log('被引用的节点数量:', referencedNodes.length)

  if (referencedNodes.length > 0) {
    // 如果有被引用的节点，显示警告
    const nodeNames = referencedNodes.map(node => node.data.title || node.id).join(', ')
    console.log('被引用的节点:', nodeNames)

    // 设置要删除的节点和引用信息
    nodesToDelete.value = selectedNodes
    multipleReferencingInfo.value = referencingInfo
    showMultiDeleteConfirm.value = true
  } else {
    // 如果没有被引用的节点，直接删除
    console.log('直接删除多个节点')
    selectedNodes.forEach(node => {
      performDeleteNode(node.id)
    })
  }
}

// 处理键盘事件
const handleKeyDown = (event) => {
  console.log('键盘事件被触发:', event.key)

  // 如果按下了删除键或退格键，并且有选中的节点
  if ((event.key === 'Delete' || event.key === 'Backspace')) {
    console.log('检测到删除键按下')

    // 获取选中的节点
    const selectedNodes = elements.value.filter(el => !el.source && el.selected)
    if (selectedNodes.length === 0) return

    // 阻止默认删除行为
    event.preventDefault()
    event.stopPropagation()

    // 如果只有一个节点，使用我们的删除逻辑
    if (selectedNodes.length === 1) {
      console.log('删除单个节点:', selectedNodes[0].id)
      deleteNode(selectedNodes[0].id)
    } else {
      // 如果有多个节点，使用我们的多节点删除逻辑
      console.log('删除多个节点')

      // 检查是否有被引用的节点
      const referencedNodes = []
      const referencingInfo = []

      // 检查每个选中的节点是否被引用
      for (const node of selectedNodes) {
        const result = checkNodeReferencesInJson(node.id)
        if (result.hasReferences) {
          referencedNodes.push(node)
          referencingInfo.push({
            node: node,
            referencingNodes: result.referencingNodes
          })
        }
      }

      console.log('被引用的节点数量:', referencedNodes.length)

      if (referencedNodes.length > 0) {
        // 如果有被引用的节点，显示警告
        const nodeNames = referencedNodes.map(node => node.data.title || node.id).join(', ')
        console.log('被引用的节点:', nodeNames)

        // 设置要删除的节点和引用信息
        nodesToDelete.value = selectedNodes
        multipleReferencingInfo.value = referencingInfo
        showMultiDeleteConfirm.value = true
      } else {
        // 如果没有被引用的节点，直接删除
        console.log('直接删除多个节点')
        selectedNodes.forEach(node => {
          performDeleteNode(node.id)
        })
      }
    }
  }
}

// 取消多节点删除
const cancelMultiDelete = () => {
  showMultiDeleteConfirm.value = false
  nodesToDelete.value = []
  multipleReferencingInfo.value = []
}

// 确认多节点删除
const confirmMultiDelete = (done) => {
  if (nodesToDelete.value && nodesToDelete.value.length > 0) {
    nodesToDelete.value.forEach(node => {
      performDeleteNode(node.id)
    })
    nodesToDelete.value = []
    multipleReferencingInfo.value = []
  }
  done()
}
</script>

<style scoped>
.workflow-viewer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background-color: #f5f5f5;
}

/* 添加顶部按钮容器样式 */
.top-buttons {
  position: fixed;
  top: 10px;
  left: 10px;
  display: flex;
  gap: 10px;
  z-index: 100;
}

/* 修改返回按钮样式 */
.back-button {
  position: static;
  display: flex;
  align-items: center;
  padding: 8px 15px;
  background-color: #333 !important;
  color: white !important;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.back-button:hover {
  background-color: #555 !important;
  color: white !important;
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* 添加暂存按钮样式 */
.save-button {
  display: flex;
  align-items: center;
  padding: 8px 15px;
  background-color: #4caf50 !important; /* 使用绿色 */
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.save-button:hover {
  background-color: #388e3c !important;
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.save-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.save-icon {
  margin-right: 5px;
}

.back-icon {
  margin-right: 5px;
}

.add-node-panel {
  position: fixed;
  top: 70px;
  left: 10px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 15px;
  z-index: 10;
  width: 280px;
  transition: all 0.3s ease;
}

.add-node-panel.collapsed {
  width: auto;
  padding: 0;
  transform: translateX(-300px); /* 完全移出视图 */
  opacity: 0;
  pointer-events: none; /* 禁用交互 */
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  border-bottom: 1px solid #eaeaea;
  padding-bottom: 12px;
}

.panel-header h3 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin: 0;
}

.toggle-panel-btn {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  font-size: 14px;
  padding: 5px;
}

.toggle-panel-btn:hover {
  color: #1890ff;
}

.panel-content {
  transition: all 0.3s ease;
}

.node-types {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  margin-bottom: 16px;
}

.add-node-btn {
  text-align: center;
  padding: 10px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid #e0e0e0 !important;
  background-color: #fafafa !important;
  color: #333 !important;
  font-size: 13px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* 使用更专业的配色方案 */
.btn-start {
  border-left: 3px solid #f5222d !important;
}

.btn-llm {
  border-left: 3px solid #52c41a !important;
}

.btn-answer {
  border-left: 3px solid #722ed1 !important;
}

.btn-method {
  border-left: 3px solid #fa8c16 !important;
}

.btn-variable-aggregator {
  border-left: 3px solid #13c2c2 !important;
}

.btn-if-else {
  border-left: 3px solid #1890ff !important;
}

.btn-coze_bot {
  border-left: 3px solid #722ed1 !important;
}

.btn-end {
  border-left: 3px solid #f5222d !important;
}

/* 悬停效果 */
.add-node-btn:hover {
  background-color: #f0f0f0 !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 搜索面板样式优化 */
.search-panel {
  position: fixed;
  top: 70px;
  right: 10px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 15px;
  z-index: 100;
  width: 320px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 1;
  transform: scale(1);
  transform-origin: top right;
}

.search-panel.collapsed {
  opacity: 0;
  transform: scale(0.95);
  pointer-events: none;
}

/* 搜索结果区域样式 */
.search-results {
  max-height: 400px;
  overflow-y: auto;
  border-radius: 4px;
  border: 1px solid #eaeaea;
  background: #fff;
  margin-top: 10px;
}

.search-result-item {
  padding: 12px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.2s ease;
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-item:hover {
  background-color: #f9f9f9;
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.node-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.node-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  flex-shrink: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.node-id {
  font-size: 12px;
  color: #999;
  font-family: monospace;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
}

.node-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: center;
}

.node-type {
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 3px;
  color: #666;
  font-weight: 400;
  background-color: #f5f5f5;
  border: 1px solid #e8e8e8;
}

.node-tag {
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 3px;
  font-weight: 400;
}

.intermediate-tag {
  background-color: #f5f5f5;
  color: #666;
  border: 1px solid #e8e8e8;
}

/* 无搜索结果样式 */
.no-results {
  padding: 15px;
  text-align: center;
  color: #999;
  font-size: 14px;
  background: #f9f9f9;
  border-radius: 4px;
  margin-top: 10px;
}

/* 节点类型颜色优化 - 使用更专业的标签样式 */
.node-type.type-start {
  border-left: 3px solid #1890ff;
}

.node-type.type-llm {
  border-left: 3px solid #52c41a;
}

.node-type.type-answer {
  border-left: 3px solid #722ed1;
}

.node-type.type-method {
  border-left: 3px solid #fa8c16;
}

.node-type.type-variable-aggregator {
  border-left: 3px solid #13c2c2;
}

.node-type.type-if-else {
  border-left: 3px solid #1890ff;
}

.node-type.type-coze_bot {
  border-left: 3px solid #722ed1;
}

.node-type.type-end {
  border-left: 3px solid #f5222d;
}

/* 展开按钮组样式 */
.expand-buttons-group {
  position: fixed;
  top: 70px;
  left: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 100;
}

.expand-panel-btn {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  background-color: white !important;
  border: 1px solid #e0e0e0 !important;
  color: #666 !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.expand-panel-btn:hover {
  color: #1890ff !important;
  border-color: #1890ff !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.expand-panel-btn.add-btn {
  background-color: white !important;
}

.expand-panel-btn.search-btn {
  background-color: white !important;
}

/* JSON 操作按钮样式优化 */
.json-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 15px;
  border-top: 1px solid #eaeaea;
  padding-top: 15px;
}

.json-btn-group {
  display: flex;
  gap: 8px;
}

.json-btn {
  flex: 1;
  padding: 8px 12px;
  background-color: #fafafa !important;
  color: #666 !important;
  border: 1px solid #d9d9d9 !important;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.json-btn:hover {
  color: #1890ff !important;
  border-color: #1890ff !important;
  background-color: #f0f7ff !important;
}

.json-btn:disabled {
  background-color: #f5f5f5 !important;
  color: #bfbfbf !important;
  border-color: #d9d9d9 !important;
  cursor: not-allowed;
}

.import-btn {
  color: #1890ff !important;
  border-color: #1890ff !important;
}

.import-btn:hover {
  background-color: #e6f7ff !important;
}

.compare-btn {
  color: #fa8c16 !important;
  border-color: #fa8c16 !important;
}

.compare-btn:hover {
  background-color: #fff7e6 !important;
}

.import-node-btn {
  color: #52c41a !important;
  border-color: #52c41a !important;
}

.import-node-btn:hover {
  background-color: #f6ffed !important;
}

.export-selected-btn {
  color: #52c41a !important;
  border-color: #52c41a !important;
}

.export-selected-btn:hover {
  background-color: #f6ffed !important;
}

/* 添加调试抽屉相关样式 */
.debug-drawer-content {
  padding: 16px;
}

.debug-node-info {
  margin-bottom: 24px;
}

.debug-node-info h3,
.debug-actions h3 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
}

.debug-node-info p {
  margin: 8px 0;
  color: #333;
}

.debug-actions {
  margin-top: 24px;
}

/* 导入节点样式 */
.import-node-info {
  margin-bottom: 15px;
}

.import-node-tip {
  margin-bottom: 12px;
  padding: 12px 15px;
  background-color: #f8f9fa;
  border-left: 4px solid #4caf50;
  border-radius: 4px;
  color: #333;
  font-size: 14px;
}

.import-node-tip p {
  margin: 6px 0;
}

.import-node-tip ul {
  margin: 6px 0;
  padding-left: 20px;
}

.import-node-example {
  margin-bottom: 15px;
}

.json-example {
  font-family: monospace;
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  overflow: auto;
  font-size: 12px;
  line-height: 1.5;
  color: #333;
  margin: 0;
}

/* 优化选中节点的显示效果 - 不包含左边框 */
:deep(.vue-flow__node.selected) {
  box-shadow: 0 3px 3px 3px rgba(57, 255, 20, 0.7),
  3px 0 3px 3px rgba(57, 255, 20, 0.7),
  0 -3px 3px 3px rgba(57, 255, 20, 0.7) !important;
  border-radius: 6px;
  animation: selected-pulse 2s infinite;
}

@keyframes selected-pulse {
  0% {
    box-shadow: 0 3px 3px 3px rgba(57, 255, 20, 0.7),
    3px 0 3px 3px rgba(57, 255, 20, 0.7),
    0 -3px 3px 3px rgba(57, 255, 20, 0.7) !important;
  }
  50% {
    box-shadow: 0 3px 8px 3px rgba(57, 255, 20, 0.4),
    3px 0 8px 3px rgba(57, 255, 20, 0.4),
    0 -3px 8px 3px rgba(57, 255, 20, 0.4) !important;
  }
  100% {
    box-shadow: 0 3px 3px 3px rgba(57, 255, 20, 0.7),
    3px 0 3px 3px rgba(57, 255, 20, 0.7),
    0 -3px 3px 3px rgba(57, 255, 20, 0.7) !important;
  }
}

/* 优化选区样式 */
:deep(.vue-flow__selection) {
  background: rgba(57, 255, 20, 0.1);
  border: 2px solid rgba(57, 255, 20, 0.6);
  border-radius: 8px;
}

/* 添加缩放控制样式 */
.zoom-controls {
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 6px;
  z-index: 100;
}

.zoom-btn {
  width: 36px; /* 增加按钮尺寸 */
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 20px; /* 增加图标尺寸 */
  transition: all 0.2s;
}

.zoom-btn:hover {
  background-color: #e0e0e0;
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.zoom-level {
  margin: 0 10px;
  font-size: 16px; /* 增加字体大小 */
  min-width: 60px;
  text-align: center;
  font-weight: 500; /* 加粗显示 */
}

.fit-btn {
  margin-left: 10px;
  background-color: #2196f3;
  color: white;
}

.fit-btn:hover {
  background-color: #0d8aee;
}

.zoom-icon, .fit-icon {
  font-weight: bold;
  line-height: 1;
}

/* 添加删除确认对话框样式 */
.delete-confirm-content {
  padding: 10px;
}

.delete-confirm-content p {
  margin: 8px 0;
}

.referencing-nodes {
  margin-top: 10px;
}

.referencing-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.referencing-list {
  list-style-type: none;
  padding-left: 20px;
}

.referencing-item {
  margin-bottom: 5px;
}

.node-title {
  font-size: 12px;
  font-weight: 400;
  color: #666;
}

.node-type {
  font-size: 10px;
  font-weight: 400;
  color: #999;
}
</style> 