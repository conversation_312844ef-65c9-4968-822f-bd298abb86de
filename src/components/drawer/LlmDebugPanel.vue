<template>
  <a-drawer
    v-model:visible="isVisible"
    :width="700"
    title="节点调试"
    placement="right"
    :mask="false"
    :closable="false"
  >
    <template #extra>
      <a-space>
        <a-button @click="handleBack">返回</a-button>
      </a-space>
    </template>

    <div class="debug-panel-content">
      <!-- 配置信息摘要 -->
      <div class="config-summary">
        <h3>当前配置</h3>
        <div class="summary-content">
          <template v-if="isSingleModel">
            <div class="summary-item">
              <span class="label">模型类型：</span>
              <span class="value">单个模型</span>
            </div>
            <div class="summary-item">
              <span class="label">提供商：</span>
              <span class="value">{{ nodeData?.data?.model?.provider || '-' }}</span>
            </div>
            <div class="summary-item">
              <span class="label">模型名称：</span>
              <span class="value">{{ nodeData?.data?.model?.name || '-' }}</span>
            </div>
            <div class="summary-item">
              <span class="label">温度：</span>
              <span class="value">{{ nodeData?.data?.model?.completion_params?.temperature || '-' }}</span>
            </div>
          </template>
          <template v-else>
            <div class="summary-item">
              <span class="label">模型类型：</span>
              <span class="value">模型组</span>
            </div>
            <div class="summary-item">
              <span class="label">当前组：</span>
              <span class="value">{{ currentGroupKey }}</span>
            </div>
            <div class="summary-item">
              <span class="label">提供商：</span>
              <span class="value">{{ currentGroupModel?.provider || '-' }}</span>
            </div>
            <div class="summary-item">
              <span class="label">模型名称：</span>
              <span class="value">{{ currentGroupModel?.name || '-' }}</span>
            </div>
            <div class="summary-item">
              <span class="label">温度：</span>
              <span class="value">{{ currentGroupModel?.completion_params?.temperature || '-' }}</span>
            </div>
          </template>
        </div>
      </div>

      <a-divider />

      <!-- 提示词预览 -->
      <div class="prompt-preview">
        <h3>提示词配置</h3>
        <div class="prompt-list">
          <div v-for="(prompt, index) in currentPrompts" :key="prompt.id" class="prompt-item">
            <div class="prompt-header">
              <span class="prompt-role">{{ prompt.role }}</span>
              <span class="prompt-index">#{{ index + 1 }}</span>
            </div>
            <div class="prompt-content">
              <template v-if="typeof prompt.text === 'string'">
                {{ prompt.text || '空' }}
              </template>
              <template v-else-if="Array.isArray(prompt.text)">
                <pre>{{ JSON.stringify(prompt.text, null, 2) }}</pre>
              </template>
              <template v-else-if="typeof prompt.text === 'object' && prompt.text !== null">
                <pre>{{ JSON.stringify(prompt.text, null, 2) }}</pre>
              </template>
              <template v-else>
                {{ String(prompt.text) || '空' }}
              </template>
            </div>
            <div v-if="prompt.images && prompt.images.length" class="prompt-images">
              <div v-for="(imgUrl, imgIndex) in prompt.images" :key="imgIndex" class="image-item">
                <img :src="imgUrl" alt="提示词图片" class="prompt-image" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <a-divider />

      <!-- 调试操作区 -->
      <div class="debug-actions">
        <div class="action-header">
          <h3>调试操作</h3>
          <a-button type="primary" @click="handleDebug" :loading="isDebugging">
            执行调试
          </a-button>
        </div>
        
        <!-- 调试结果 -->
        <div class="debug-result" :class="{ 'is-streaming': isDebugging }">
          <div class="result-header">
            <span class="result-title">调试结果</span>
            <a-button type="text" size="mini" @click="debugResult = ''" v-if="!isDebugging">
              <icon-close />
            </a-button>
          </div>
          <pre class="result-content" v-if="debugResult || isDebugging">{{ debugResult }}</pre>
          <div class="empty-result" v-else>暂无调试结果</div>
        </div>
      </div>
    </div>

    <!-- 修改底部按钮栏 -->
    <template #footer>
      <div class="arco-drawer-footer">
        <a-button class="arco-btn-secondary" @click="handleBack">返回</a-button>
<!--        <a-button type="primary" @click="handleClose">确定</a-button>-->
      </div>
    </template>
  </a-drawer>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { Message } from '@arco-design/web-vue'
import { chatWithLLM } from '@/api/workflow'
import { getLlmDebugData } from '@/utils/storage'
import { v4 as uuidv4 } from 'uuid'

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  nodeData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:visible', 'close', 'back'])

// 控制抽屉显示
const isVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 判断是否为单个模型
const isSingleModel = computed(() => {
  return !!props.nodeData?.data?.model
})

// 获取当前模型组信息
const currentGroupKey = computed(() => {
  if (!isSingleModel.value && props.nodeData?.data?.model_group) {
    return Object.keys(props.nodeData.data.model_group)[0] || ''
  }
  return ''
})

const currentGroupModel = computed(() => {
  if (currentGroupKey.value && props.nodeData?.data?.model_group) {
    return props.nodeData.data.model_group[currentGroupKey.value]?.model || {}
  }
  return {}
})

// 获取当前提示词
const currentPrompts = computed(() => {
  if (!props.nodeData?.data) return []
  
  // 优先从process_data中获取提示词
  if (props.nodeData?.data?.process_data?.messages) {
    return props.nodeData.data.process_data.messages.map(msg => {
      // 处理content为数组的情况（包含图片）
      if (msg.role === 'user' && Array.isArray(msg.content)) {
        // 提取文本内容
        const textContent = msg.content.find(item => item.type === 'text')?.text || ''
        // 提取图片URL
        const imageUrls = msg.content
          .filter(item => item.type === 'image_url')
          .map(item => item.image_url?.url)
          .filter(Boolean)
        
        return {
          id: uuidv4(),
          role: msg.role,
          text: textContent,
          images: imageUrls
        }
      }
      
      // 处理content是对象或数组的情况
      let textContent = msg.content
      if (typeof textContent === 'object' && textContent !== null) {
        // 保留原始对象，在模板中处理显示
        textContent = msg.content
      }
      
      return {
        id: uuidv4(),
        role: msg.role,
        text: textContent,
        images: []
      }
    })
  }
  
  if (isSingleModel.value) {
    return props.nodeData.data.prompt_template || []
  } else if (currentGroupKey.value) {
    return props.nodeData.data.model_group?.[currentGroupKey.value]?.prompt_template || []
  }
  return []
})

// 调试相关
const isDebugging = ref(false)
const debugResult = ref('')
const cleanupStream = ref(null)

// 监听可见性变化
watch(() => props.visible, (newVisible) => {
  if (!newVisible && cleanupStream.value) {
    console.log('🧹 调试面板 - 清理流')
    cleanupStream.value()
    cleanupStream.value = null
  }
  if (newVisible) {
    const debugData = getLlmDebugData()
    if (!debugData) {
      Message.error('未找到调试数据')
      handleBack()
      return
    }
    console.log('📄 调试面板 - 读取调试数据:', debugData)
  }
  debugResult.value = ''
  isDebugging.value = false
})

// 修改 handleDebug 方法
const handleDebug = async () => {
  const debugData = getLlmDebugData()
  if (!debugData) {
    Message.error('未找到调试数据')
    return
  }
  
  console.log('📝 调试面板 - 使用的提示词:')
  
  // 优先从process_data中获取提示词
  let prompts = []
  let requestMessages = []
  
  if (debugData.data?.process_data?.messages) {
    // 原始messages数据
    const originalMessages = debugData.data.process_data.messages
    
    // 处理提示词显示
    prompts = originalMessages.map(msg => {
      if (msg.role === 'user' && Array.isArray(msg.content)) {
        const textContent = msg.content.find(item => item.type === 'text')?.text || ''
        const imageUrls = msg.content
          .filter(item => item.type === 'image_url')
          .map(item => item.image_url?.url)
          .filter(Boolean)
          
        console.log(`#${prompts.length + 1} [${msg.role}]:`, textContent, imageUrls.length ? `(包含${imageUrls.length}张图片)` : '')
        
        return {
          role: msg.role,
          text: textContent,
          images: imageUrls
        }
      }
      
      // 处理对象和数组的日志输出
      let logContent = msg.content
      if (typeof logContent === 'object' && logContent !== null) {
        logContent = JSON.stringify(msg.content)
      }
      console.log(`#${prompts.length + 1} [${msg.role}]:`, logContent)
      
      return {
        role: msg.role,
        text: msg.content
      }
    })
    
    // 保持原始消息格式用于API请求
    requestMessages = originalMessages
  } else {
    prompts = isSingleModel.value 
    ? debugData.data.prompt_template 
    : debugData.data.model_group?.[currentGroupKey.value]?.prompt_template || []
  
  prompts.forEach((prompt, index) => {
      // 处理对象和数组的日志输出
      let logContent = prompt.text
      if (typeof logContent === 'object' && logContent !== null) {
        logContent = JSON.stringify(prompt.text)
      }
      console.log(`#${index + 1} [${prompt.role}]:`, logContent)
    })
    
    // 转换为API请求格式
    requestMessages = prompts.map(prompt => ({
      role: prompt.role,
      content: prompt.text
    }))
  }
  
  try {
    isDebugging.value = true
    debugResult.value = ''
    
    // 准备请求数据
    const model = isSingleModel.value 
      ? debugData.data.model 
      : debugData.data.model_group?.[currentGroupKey.value]?.model
    
    if (!model) {
      throw new Error('未找到模型配置')
    }
    
    const requestData = {
      provider: model.provider,
      modelName: model.name,
      stream: true,
      messages: requestMessages,
      temperature: model.completion_params?.temperature || 1.0
    }

    console.log('📤 调试面板 - 发送请求数据:', requestData)
    
    // 清理之前的流
    if (cleanupStream.value) {
      cleanupStream.value()
      cleanupStream.value = null
    }
    
    // 使用流式 API
    cleanupStream.value = await chatWithLLM(requestData, {
      onMessage: (message) => {
        console.log('🔄 LlmDebugPanel - 收到流式数据:', message)
        if (message.content) {
          debugResult.value += message.content
          console.log('📝 LlmDebugPanel - 当前调试结果长度:', debugResult.value.length)
        }
      },
      onError: (error) => {
        console.error('❌ 调试面板 - 调试失败:', error)
        Message.error(error.message || '调试失败')
        isDebugging.value = false
      },
      onComplete: () => {
        console.log('✅ 调试面板 - 调试完成')
        if (!debugResult.value) {
          Message.warning('模型未返回任何内容')
        }
        isDebugging.value = false
      }
    })
    
  } catch (error) {
    console.error('❌ 调试面板 - 调试失败:', error)
    Message.error(error.message || '调试失败')
    isDebugging.value = false
  }
}

// 修改关闭处理方法
const handleClose = () => {
  console.log('❌ 调试面板 - 关闭')
  isVisible.value = false
  emit('close')
}

// 修改返回方法
const handleBack = () => {
  console.log('⬅️ 调试面板 - 返回')
  isVisible.value = false
  emit('back')
}
</script>

<style scoped>
.debug-panel-content {
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

.config-summary h3,
.prompt-preview h3,
.debug-actions h3 {
  margin: 0 0 16px;
  font-size: 16px;
  font-weight: 500;
  color: var(--color-text-1);
}

.summary-content {
  background: var(--color-fill-2);
  border-radius: 4px;
  padding: 16px;
}

.summary-item {
  display: flex;
  margin-bottom: 8px;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-item .label {
  width: 80px;
  color: var(--color-text-3);
  flex-shrink: 0;
}

.summary-item .value {
  color: var(--color-text-1);
  flex: 1;
}

.prompt-list {
  margin-top: 12px;
}

.prompt-item {
  background: #f5f5f5;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
}

.prompt-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.prompt-role {
  font-weight: 500;
  color: #165DFF;
  background: rgba(22, 93, 255, 0.1);
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 13px;
}

.prompt-index {
  color: #86909C;
  font-size: 12px;
}

.prompt-content {
  color: #4E5969;
  white-space: pre-wrap;
  word-break: break-all;
  font-size: 13px;
  line-height: 1.6;
}

.action-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.debug-result {
  background: #f8f9fa;
  border-radius: 4px;
  margin-top: 16px;
}

.debug-result.is-streaming .result-content {
  min-height: 100px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--color-border-2);
}

.result-title {
  font-weight: 500;
  color: var(--color-text-1);
}

.result-content {
  margin: 0;
  padding: 16px;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  max-height: 400px;
  overflow-y: auto;
}

.empty-result {
  padding: 16px;
  text-align: center;
  color: var(--color-text-3);
}

.prompt-images {
  margin-top: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.image-item {
  width: 100px;
  height: 100px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid var(--color-border-2);
}

.prompt-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.prompt-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
  font-family: monospace;
  font-size: 13px;
  line-height: 1.6;
  background-color: rgba(0, 0, 0, 0.03);
  padding: 8px;
  border-radius: 4px;
}
</style>