<template>
  <a-drawer
    v-model:visible="isVisible"
    :width="700"
    title="LLM 节点调试"
    placement="right"
    :mask="true"
    :mask-closable="true"
    @close="handleClose"
  >
    <!-- 添加调试结果对话框 -->
    <a-modal
      v-model:visible="debugResultVisible"
      title="调试结果"
      :footer="false"
      :mask-closable="true"
      :width="600"
    >
      <div class="debug-result">
        <a-alert v-if="debugError" type="error" :content="debugError" />
        <div v-else class="result-content">
          <pre>{{ debugResult }}</pre>
        </div>
      </div>
    </a-modal>

    <div class="debug-drawer-content">
      <div v-if="node">
        <div class="debug-node-info">
          <h3>节点信息</h3>
          <div class="info-item">
            <span class="label">标题:</span>
            <span class="value">{{ node.data.title }}</span>
          </div>

          <a-divider />

          <h3>模型配置</h3>
          <div class="model-type-select">
            <a-radio-group v-model="modelType" type="button">
              <a-radio value="single">使用单个模型</a-radio>
              <a-radio value="group" :disabled="!modelGroupOptions.length">使用模型组</a-radio>
            </a-radio-group>
          </div>

          <!-- 单个模型配置 -->
          <div v-if="modelType === 'single'" class="model-config-form">
            <a-form :model="modelConfig" layout="vertical">
              <a-form-item label="提供商" field="provider">
                <a-select
                  v-model="modelConfig.provider"
                  placeholder="请选择模型提供商"
                  allow-clear
                  :loading="loadingModels"
                >
                  <a-option
                    v-for="option in providerOptions"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </a-option>
                </a-select>
              </a-form-item>

              <a-form-item label="模型名称" field="name">
                <a-select
                  v-model="modelConfig.name"
                  placeholder="请选择模型"
                  allow-clear
                  :loading="loadingModels"
                  :disabled="!modelConfig.provider"
                >
                  <a-option
                    v-for="option in modelOptions"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </a-option>
                </a-select>
              </a-form-item>

              <a-form-item label="温度" field="temperature">
                <a-slider
                  v-model="modelConfig.temperature"
                  :min="0"
                  :max="2"
                  :step="0.1"
                  show-ticks
                  show-input
                />
              </a-form-item>
            </a-form>
          </div>

          <!-- 模型组配置 -->
          <div v-if="modelType === 'group'" class="model-group-form">
            <a-form :model="groupModelConfig" layout="vertical">
              <a-form-item label="选择模型组">
                <a-select
                  v-model="selectedGroupKey"
                  placeholder="请选择模型组"
                  @change="handleGroupModelChange"
                >
                  <a-option
                    v-for="option in modelGroupOptions"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </a-option>
                </a-select>
              </a-form-item>

              <template v-if="selectedGroupKey">
                <a-form-item label="提供商" field="provider">
                  <a-select
                    v-model="groupModelConfig.provider"
                    placeholder="请选择模型提供商"
                    allow-clear
                    :loading="loadingModels"
                  >
                    <a-option
                      v-for="option in providerOptions"
                      :key="option.value"
                      :value="option.value"
                    >
                      {{ option.label }}
                    </a-option>
                  </a-select>
                </a-form-item>

                <a-form-item label="模型名称" field="name">
                  <a-select
                    v-model="groupModelConfig.name"
                    placeholder="请选择模型"
                    allow-clear
                    :loading="loadingModels"
                    :disabled="!groupModelConfig.provider"
                  >
                    <a-option
                      v-for="option in groupModelOptions"
                      :key="option.value"
                      :value="option.value"
                    >
                      {{ option.label }}
                    </a-option>
                  </a-select>
                </a-form-item>

                <a-form-item label="温度" field="temperature">
                  <a-slider
                    v-model="groupModelConfig.temperature"
                    :min="0"
                    :max="2"
                    :step="0.1"
                    show-ticks
                    show-input
                  />
                </a-form-item>
              </template>
            </a-form>
          </div>

          <a-divider />

          <h3>提示词配置</h3>
          <div v-if="!modelType" class="empty-prompt">
            请先选择使用模型或模型组
          </div>
          <div v-else-if="modelType === 'group' && !selectedGroupKey" class="empty-prompt">
            请先选择模型组
          </div>
          <div v-else class="prompt-list">
            <div v-for="(prompt, index) in currentPrompts" :key="prompt.id" class="prompt-item">
              <div class="prompt-header">
                <a-select v-model="prompt.role" class="prompt-role" size="mini">
                  <a-option value="system">系统</a-option>
                  <a-option value="user">用户</a-option>
                  <a-option value="assistant">助手</a-option>
                </a-select>
                <div class="prompt-actions">
                  <span class="prompt-index">#{{ index + 1 }}</span>
                  <a-button-group class="action-buttons">
                    <a-button type="text" size="mini" @click="movePromptUp(index)" :disabled="index === 0">
                      <icon-up />
                    </a-button>
                    <a-button type="text" size="mini" @click="movePromptDown(index)" :disabled="index === currentPrompts.length - 1">
                      <icon-down />
                    </a-button>
                    <a-button type="text" size="mini" @click="removePrompt(index)" :disabled="currentPrompts.length <= 1">
                      <icon-delete />
                    </a-button>
                  </a-button-group>
                </div>
              </div>
              <a-textarea
                v-if="typeof prompt.text === 'string'"
                v-model="prompt.text"
                :auto-size="{ minRows: 4, maxRows: 8 }"
                placeholder="输入提示词内容..."
                allow-clear
                class="prompt-content"
              />
              <div v-else class="prompt-content-object">
                <pre>{{ JSON.stringify(prompt.text, null, 2) }}</pre>
                <div class="object-note">对象类型内容不可编辑，请在原始数据中修改</div>
              </div>
              <!-- 添加图片预览 -->
              <div v-if="prompt.images && prompt.images.length" class="prompt-images">
                <div v-for="(imgUrl, imgIndex) in prompt.images" :key="imgIndex" class="image-item">
                  <img :src="imgUrl" alt="提示词图片" class="prompt-image" />
                </div>
              </div>
            </div>
            <div v-if="!currentPrompts.length" class="empty-prompt">
              暂无提示词配置
            </div>
            <a-button v-else long type="outline" size="small" @click="addPrompt()" style="margin-top: 12px;">
              <template #icon><icon-plus /></template>
              添加提示词
            </a-button>
          </div>
        </div>

        <a-divider />

        <!-- 显示当前选中的模型组信息 -->
        <template v-if="selectedGroupKey && node.data.model_group?.[selectedGroupKey]?.model">
          <div class="group-model-info">
            <div class="info-item">
              <span class="label">当前配置:</span>
              <span class="value">{{ selectedGroupKey }}</span>
            </div>
            <div class="info-item">
              <span class="label">提供商:</span>
              <span class="value">{{ node.data.model_group[selectedGroupKey].model.provider || '未设置' }}</span>
            </div>
            <div class="info-item">
              <span class="label">模型名称:</span>
              <span class="value">{{ node.data.model_group[selectedGroupKey].model.name || '未设置' }}</span>
            </div>
            <div class="info-item">
              <span class="label">温度:</span>
              <span class="value">{{ node.data.model_group[selectedGroupKey].model.completion_params?.temperature || 0.7 }}</span>
            </div>
          </div>
        </template>
      </div>
    </div>

    <!-- 调试结果展示 -->
    <div v-if="debugResult" class="debug-result-panel">
      <div class="debug-result-header">
        <span class="header-title">调试结果</span>
        <a-button type="text" size="mini" @click="debugResult = ''">
          <icon-close />
        </a-button>
      </div>
      <div class="debug-result-content">
        <pre>{{ debugResult }}</pre>
      </div>
    </div>

    <!-- 调试界面抽屉 -->
    <LlmDebugPanel
      v-model:visible="debugPanelVisible"
      :node-data="currentNodeData"
      @close="debugPanelVisible = false"
    />

    <!-- 添加底部按钮栏 -->
    <template #footer>
      <div class="arco-drawer-footer">
        <a-button class="arco-btn-secondary" @click="handleClose">取消</a-button>
        <a-button type="primary" @click="handleConfirm">调试</a-button>
      </div>
    </template>
  </a-drawer>
</template>

<script setup>
import { ref, watch, computed, onMounted } from 'vue'
import { getLlmModels } from '@/api/workflow'
import { Message } from '@arco-design/web-vue'
import { v4 as uuidv4 } from 'uuid'
import { cloneDeep } from 'lodash-es'
import LlmDebugPanel from './LlmDebugPanel.vue'
import { saveLlmDebugData, clearLlmDebugData } from '@/utils/storage'

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  node: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:visible', 'close', 'debug'])

// 使用计算属性处理 visible 的双向绑定
const isVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 模型类型选择
const modelType = ref('') // 'single' 或 'group'

// 模型配置的响应式数据
const modelConfig = ref({
  provider: '',
  name: '',
  temperature: 0.7
})

// 模型组配置
const selectedGroupKey = ref('')
const groupModelConfig = ref({
  provider: '',
  name: '',
  temperature: 0.7
})

// 模型列表相关状态
const llmModels = ref([])
const loadingModels = ref(false)

// 本地提示词数据
const localPrompts = ref([])

// 调试相关的状态
const debugResultVisible = ref(false)
const debugResult = ref('')
const debugError = ref('')
const isDebugging = ref(false)

// 调试面板相关
const debugPanelVisible = ref(false)
const currentNodeData = ref(null)

// 获取模型列表
const fetchModels = async () => {
  loadingModels.value = true
  try {
    const response = await getLlmModels()
    if (response.code === 200) {
      llmModels.value = response.data
    } else {
      Message.error('获取模型列表失败: ' + response.message)
    }
  } catch (error) {
    console.error('获取模型列表失败:', error)
    Message.error('获取模型列表失败: ' + error.message)
  } finally {
    loadingModels.value = false
  }
}

// 监听抽屉显示状态，当打开时获取模型列表
watch(() => isVisible.value, (newVisible) => {
  if (newVisible) {
    fetchModels()
  }
})

// 获取提供商选项
const providerOptions = computed(() => {
  const providers = new Set(llmModels.value.map(provider => provider.name))
  return Array.from(providers).map(name => ({
    label: name,
    value: name
  }))
})

// 获取模型选项（根据提供商筛选）
const getModelOptionsByProvider = (provider) => {
  if (!provider) return []
  const providerData = llmModels.value.find(p => p.name === provider)
  // 如果当前选中的模型不在列表中，添加到选项中
  const options = providerData ? providerData.supportModels.map(model => ({
    label: model,
    value: model
  })) : []

  // 对于单个模型配置
  if (modelConfig.value.name && !options.find(opt => opt.value === modelConfig.value.name)) {
    options.unshift({
      label: modelConfig.value.name,
      value: modelConfig.value.name
    })
  }

  return options
}

// 获取模型组的模型选项
const getGroupModelOptionsByProvider = (provider, groupKey) => {
  if (!provider) return []
  const providerData = llmModels.value.find(p => p.name === provider)
  const options = providerData ? providerData.supportModels.map(model => ({
    label: model,
    value: model
  })) : []

  // 如果当前选中的模型不在列表中，添加到选项中
  if (groupKey && props.node?.data?.model_group?.[groupKey]?.model?.name) {
    const currentModel = props.node.data.model_group[groupKey].model.name
    if (!options.find(opt => opt.value === currentModel)) {
      options.unshift({
        label: currentModel,
        value: currentModel
      })
    }
  }

  return options
}

// 单个模型的模型选项
const modelOptions = computed(() => {
  return getModelOptionsByProvider(modelConfig.value.provider)
})

// 获取可用的模型组选项
const modelGroupOptions = computed(() => {
  if (!props.node?.data?.model_group) return []
  return Object.entries(props.node.data.model_group).map(([key]) => ({
    label: key,
    value: key
  }))
})

// 模型组的模型选项
const groupModelOptions = computed(() => {
  return getGroupModelOptionsByProvider(groupModelConfig.value.provider, selectedGroupKey.value)
})

// 监听提供商变化，但不清空模型名称
watch(() => modelConfig.value.provider, (newProvider) => {
  if (!newProvider) {
    modelConfig.value.name = ''
  }
})

watch(() => groupModelConfig.value.provider, (newProvider) => {
  if (!newProvider) {
    groupModelConfig.value.name = ''
  }
})

// 当节点数据变化时更新配置
watch(() => props.node, (newNode) => {
  if (newNode?.data) {
    if (newNode.data.model) {
      modelType.value = 'single'
      modelConfig.value = {
        provider: newNode.data.model.provider || '',
        name: newNode.data.model.name || '',
        temperature: newNode.data.model.completion_params?.temperature || 0.7
      }
      selectedGroupKey.value = ''
      groupModelConfig.value = {
        provider: '',
        name: '',
        temperature: 0.7
      }
    } else if (newNode.data.model_group) {
      modelType.value = 'group'
      const groupKeys = Object.keys(newNode.data.model_group)
      if (groupKeys.length > 0) {
        selectedGroupKey.value = groupKeys[0]
        const groupData = newNode.data.model_group[groupKeys[0]]?.model || {}
        groupModelConfig.value = {
          provider: groupData.provider || '',
          name: groupData.name || '',
          temperature: groupData.completion_params?.temperature || 0.7
        }
      }
    } else {
      modelType.value = ''
      modelConfig.value = {
        provider: '',
        name: '',
        temperature: 0.7
      }
      selectedGroupKey.value = ''
      groupModelConfig.value = {
        provider: '',
        name: '',
        temperature: 0.7
      }
    }
  }
}, { immediate: true })

// 监听选中的模型组变化
watch(() => selectedGroupKey.value, (newKey) => {
  if (newKey && props.node?.data?.model_group?.[newKey]) {
    const groupData = props.node.data.model_group[newKey]?.model || {}
    groupModelConfig.value = {
      provider: groupData.provider || '',
      name: groupData.name || '',
      temperature: groupData.completion_params?.temperature || 0.7
    }
  }
})

// 修改获取当前显示的提示词的逻辑
const currentPrompts = computed({
  get: () => {
    if (!props.node?.data) return []

    // 使用本地提示词数据，这样可以支持添加新的提示词
    return localPrompts.value
  },
  set: (val) => {
    localPrompts.value = val
  }
})

// 监听节点数据变化，更新本地提示词
watch(() => props.node?.data, (newNode) => {
  if (newNode) {
    // 优先从process_data中获取提示词
    if (newNode.process_data?.messages) {
      localPrompts.value = newNode.process_data.messages.map(msg => {
        // 处理content为数组的情况（包含图片）
        if (msg.role === 'user' && Array.isArray(msg.content)) {
          // 提取文本内容
          const textContent = msg.content.find(item => item.type === 'text')?.text || ''
          // 提取图片URL
          const imageUrls = msg.content
            .filter(item => item.type === 'image_url')
            .map(item => item.image_url?.url)
            .filter(Boolean)
          
          return {
            id: uuidv4(),
            role: msg.role,
            text: textContent,
            images: imageUrls
          }
        }
        
        // 处理content是对象或数组的情况
        let textContent = msg.content
        if (typeof textContent === 'object' && textContent !== null) {
          // 保留原始对象，在模板中处理显示
          textContent = msg.content
        }
        
        return {
          id: uuidv4(),
          role: msg.role,
          text: textContent,
          images: []
        }
      })
    } else if (modelType.value === 'single') {
      localPrompts.value = cloneDeep(newNode.prompt_template || [])
    } else if (modelType.value === 'group' && selectedGroupKey.value) {
      localPrompts.value = cloneDeep(newNode.model_group?.[selectedGroupKey.value]?.prompt_template || [])
    }
  }
}, { immediate: true })

// 监听模型类型和选中的组变化
watch([() => modelType.value, () => selectedGroupKey.value], ([newType, newKey]) => {
  if (props.node?.data) {
    // 优先从process_data中获取提示词
    if (props.node.data.process_data?.messages) {
      localPrompts.value = props.node.data.process_data.messages.map(msg => {
        // 处理content为数组的情况（包含图片）
        if (msg.role === 'user' && Array.isArray(msg.content)) {
          // 提取文本内容
          const textContent = msg.content.find(item => item.type === 'text')?.text || ''
          // 提取图片URL
          const imageUrls = msg.content
            .filter(item => item.type === 'image_url')
            .map(item => item.image_url?.url)
            .filter(Boolean)
          
          return {
            id: uuidv4(),
            role: msg.role,
            text: textContent,
            images: imageUrls
          }
        }
        
        // 处理content是对象或数组的情况
        let textContent = msg.content
        if (typeof textContent === 'object' && textContent !== null) {
          // 保留原始对象，在模板中处理显示
          textContent = msg.content
        }
        
        return {
          id: uuidv4(),
          role: msg.role,
          text: textContent,
          images: []
        }
      })
    } else if (newType === 'single') {
      localPrompts.value = cloneDeep(props.node.data.prompt_template || [])
    } else if (newType === 'group' && newKey) {
      localPrompts.value = cloneDeep(props.node.data.model_group?.[newKey]?.prompt_template || [])
    }
  }
})

// 处理关闭
const handleClose = () => {
  // 清除调试数据
  clearLlmDebugData()
  console.log('❌ 抽屉 - 清除调试数据')
  emit('update:visible', false)
  emit('close')
}

// 处理调试
const handleDebug = async () => {
  if (!props.node?.data) return

  try {
    // 保存当前节点数据到 localStorage
    saveLlmDebugData(props.node)
    console.log('📝 抽屉 - 已保存调试数据:', props.node)

    // 关闭抽屉，打开调试面板
    emit('update:visible', false)
    emit('debug')
  } catch (error) {
    console.error('❌ 抽屉 - 保存调试数据失败:', error)
    Message.error('保存调试数据失败')
  }
}

// 处理模型组切换
const handleGroupModelChange = async (groupKey) => {
  if (!groupKey || !props.node?.data?.model_group?.[groupKey]) return

  selectedGroupKey.value = groupKey
  const groupData = props.node.data.model_group[groupKey]?.model || {}
  groupModelConfig.value = {
    provider: groupData.provider || '',
    name: groupData.name || '',
    temperature: groupData.completion_params?.temperature || 0.7
  }
}

// 修改提示词相关方法
const addPrompt = () => {
  const newPrompt = {
    id: uuidv4(),
    role: 'user',
    text: ''
  }
  localPrompts.value.push(newPrompt)
}

const removePrompt = (index) => {
  if (localPrompts.value.length > 1) {
    localPrompts.value.splice(index, 1)
  }
}

const movePromptUp = (index) => {
  if (index === 0) return
  const temp = localPrompts.value[index]
  localPrompts.value[index] = localPrompts.value[index - 1]
  localPrompts.value[index - 1] = temp
}

const movePromptDown = (index) => {
  if (index === localPrompts.value.length - 1) return
  const temp = localPrompts.value[index]
  localPrompts.value[index] = localPrompts.value[index + 1]
  localPrompts.value[index + 1] = temp
}

// 处理确认按钮点击
const handleConfirm = () => {
  // 准备调试用的节点数据副本
  const debugNode = cloneDeep(props.node)

  if (modelType.value === 'single') {
    debugNode.data.model = {
      mode: 'chat',
      provider: modelConfig.value.provider,
      name: modelConfig.value.name,
      completion_params: {
        temperature: modelConfig.value.temperature
      }
    }
    
    // 保存提示词到prompt_template
    debugNode.data.prompt_template = cloneDeep(localPrompts.value)
    
    // 如果存在process_data，也更新messages
    if (debugNode.data.process_data) {
      debugNode.data.process_data.messages = localPrompts.value.map(prompt => {
        // 处理包含图片的提示词
        if (prompt.images && prompt.images.length > 0) {
          return {
            role: prompt.role,
            content: [
              { type: 'text', text: prompt.text },
              ...prompt.images.map(url => ({
                type: 'image_url',
                image_url: { url }
              }))
            ]
          }
        }
        
        return {
          role: prompt.role,
          content: prompt.text
        }
      })
    }
    
    delete debugNode.data.model_group
  } else if (modelType.value === 'group' && selectedGroupKey.value) {
    if (!debugNode.data.model_group) {
      debugNode.data.model_group = {}
    }

    const existingGroupConfig = debugNode.data.model_group[selectedGroupKey.value] || {}

    debugNode.data.model_group[selectedGroupKey.value] = {
      ...existingGroupConfig,
      model: {
        mode: 'chat',
        provider: groupModelConfig.value.provider,
        name: groupModelConfig.value.name,
        completion_params: {
          temperature: groupModelConfig.value.temperature
        }
      },
      prompt_template: cloneDeep(localPrompts.value)
    }
    
    // 如果存在process_data，也更新messages
    if (debugNode.data.process_data) {
      debugNode.data.process_data.messages = localPrompts.value.map(prompt => {
        // 处理包含图片的提示词
        if (prompt.images && prompt.images.length > 0) {
          return {
            role: prompt.role,
            content: [
              { type: 'text', text: prompt.text },
              ...prompt.images.map(url => ({
                type: 'image_url',
                image_url: { url }
              }))
            ]
          }
        }
        
        return {
          role: prompt.role,
          content: prompt.text
        }
      })
    }
    
    delete debugNode.data.model
  }

  // 保存调试用的节点数据到 localStorage
  saveLlmDebugData(debugNode)
  console.log('📥 抽屉 - 保存调试数据:', debugNode)

  // 打开调试面板
  debugPanelVisible.value = true
  currentNodeData.value = debugNode
}
</script>

<style scoped>
.debug-drawer-content {
  padding: 16px;
  min-height: 400px;
  height: 100%;
}

.debug-node-info {
  margin-bottom: 24px;
}

.debug-node-info h3 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
  color: #1d2129;
}

.info-item {
  display: flex;
  margin: 12px 0;
  align-items: flex-start;
}

.info-item .label {
  width: 80px;
  color: #4E5969;
  flex-shrink: 0;
}

.info-item .value {
  color: #1D2129;
  flex: 1;
}

.prompt-list {
  margin-top: 12px;
  min-height: 200px;
}

.prompt-item {
  background: #f5f5f5;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
}

.prompt-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.prompt-role {
  font-weight: 500;
  color: #165DFF;
  background: rgba(22, 93, 255, 0.1);
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 13px;
}

.prompt-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-buttons {
  opacity: 0;
  transition: opacity 0.2s;
}

.prompt-item:hover .action-buttons {
  opacity: 1;
}

.prompt-index {
  color: #86909C;
  font-size: 12px;
}

.prompt-content {
  color: #4E5969;
  white-space: pre-wrap;
  word-break: break-all;
  font-size: 13px;
  line-height: 1.6;
  min-height: 100px;
}

.prompt-content-object {
  margin-bottom: 12px;
}

.prompt-content-object pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
  font-family: monospace;
  font-size: 13px;
  line-height: 1.6;
  background-color: rgba(0, 0, 0, 0.03);
  padding: 8px;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
}

.object-note {
  color: #86909C;
  font-size: 12px;
  margin-top: 4px;
  font-style: italic;
}

.prompt-images {
  margin-top: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.image-item {
  width: 100px;
  height: 100px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid var(--color-border-2);
}

.prompt-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.empty-prompt {
  color: #86909C;
  text-align: center;
  padding: 24px;
  background: #f5f5f5;
  border-radius: 4px;
  font-size: 14px;
}

:deep(.arco-divider) {
  margin: 16px 0;
}

.model-type-select {
  margin-bottom: 24px;
  text-align: center;
}

:deep(.arco-radio-group-button) {
  display: inline-flex;
  gap: 8px;
}

:deep(.arco-radio-button) {
  border-radius: 4px !important;
}

.model-config-form,
.model-group-form {
  padding: 0 12px;
}

:deep(.arco-form-item) {
  margin-bottom: 20px;
}

:deep(.arco-form-item-label) {
  font-weight: 500;
}

:deep(.arco-select-view) {
  background-color: var(--color-fill-2);
  border-radius: 4px;
}

:deep(.arco-slider) {
  width: 100%;
  margin: 10px 0;
}

:deep(.arco-slider-input) {
  width: 80px;
  margin-left: 16px;
}

.group-model-info {
  margin-top: 16px;
  padding: 12px;
  background: var(--color-fill-2);
  border-radius: 4px;
}

.group-model-info .info-item {
  margin: 8px 0;
  display: flex;
  align-items: center;
}

.group-model-info .label {
  width: 80px;
  color: var(--color-text-3);
  flex-shrink: 0;
}

.group-model-info .value {
  color: var(--color-text-1);
  flex: 1;
}

.group-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.group-name {
  font-weight: 500;
}

.model-name {
  color: var(--color-text-3);
  font-size: 0.9em;
}

.current-group-info {
  background: var(--color-fill-2);
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 16px;
}

.current-group-info .info-item {
  display: flex;
  margin: 4px 0;
}

.current-group-info .label {
  width: 80px;
  color: var(--color-text-3);
  flex-shrink: 0;
}

.current-group-info .value {
  color: var(--color-text-1);
  flex: 1;
  word-break: break-all;
}

.model-group-form {
  .arco-form-item {
    margin-bottom: 24px;
  }
}

.current-model-info {
  margin-top: 8px;
  color: var(--color-text-3);
  font-size: 13px;
}

/* 添加选择器样式 */
:deep(.arco-select) {
  width: 100%;
}

:deep(.arco-select-view) {
  background-color: #fff !important;
}

:deep(.arco-select-option) {
  padding: 8px 12px;
}

.debug-result {
  max-height: 60vh;
  overflow-y: auto;
}

.result-content {
  padding: 16px;
  background: #f5f5f5;
  border-radius: 4px;
}

.result-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
}

:deep(.arco-modal-content) {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.debug-result-panel {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 1px solid var(--color-border-2);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
  z-index: 1;
}

.debug-result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--color-border-2);
}

.header-title {
  font-weight: 500;
  color: var(--color-text-1);
}

.debug-result-content {
  padding: 16px;
  max-height: 300px;
  overflow-y: auto;
  background: #f8f9fa;
}

.debug-result-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
}
</style> 