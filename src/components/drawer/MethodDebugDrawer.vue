<template>
  <a-drawer
    v-model:visible="isVisible"
    :width="700"
    title="方法节点调试"
    placement="right"
    :mask="true"
    :mask-closable="true"
    @close="handleClose"
  >
    <div class="debug-drawer-content">
      <div v-if="node">
        <div class="debug-node-info">
          <h3>节点信息</h3>
          <div class="info-item">
            <span class="label">标题:</span>
            <span class="value">{{ node.data.title }}</span>
          </div>
          
          <a-divider />
          
          <h3>组件配置</h3>
          <div class="component-config">
            <div class="info-item">
              <span class="label">组件名称:</span>
              <span class="value">{{ node.data.component_name || '未设置' }}</span>
            </div>
            
            <!-- 变量列表 -->
            <div v-if="localVariables.length > 0" class="variables-section">
              <h4>变量配置</h4>
              <div class="variables-list">
                <div v-for="(variable, index) in localVariables" :key="index" class="variable-item">
                  <div class="variable-header">
                    <div class="variable-info">
                      <span class="variable-name">{{ variable.variable }}</span>
                      <span class="variable-selector">{{ formatSelector(variable.value_selector) }}</span>
                    </div>
                    <div class="variable-value">
                      <span class="value-label">调试值</span>
                      <a-textarea
                        v-model="variable.debug_value"
                        :placeholder="'请输入' + variable.variable + '的调试值'"
                        allow-clear
                        :auto-size="{ minRows: 1, maxRows: 5 }"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 调试结果 -->
        <div v-if="debugResult" class="debug-result">
          <div class="result-header">
            <span class="result-title">调试结果</span>
            <a-button type="text" size="mini" @click="debugResult = ''">
              <icon-close />
            </a-button>
          </div>
          <div class="result-content">
            <pre>{{ formatDebugResult(debugResult) }}</pre>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加底部按钮栏 -->
    <template #footer>
      <div class="arco-drawer-footer">
        <a-button class="arco-btn-secondary" @click="handleClose">取消</a-button>
        <a-button type="primary" @click="handleDebug" :loading="isDebugging">调试</a-button>
      </div>
    </template>
  </a-drawer>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Message } from '@arco-design/web-vue'
import { cloneDeep } from 'lodash-es'
import { debugMethodNode } from '@/api/workflow'

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  node: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:visible', 'close'])

// 控制抽屉显示
const isVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 本地变量数据
const localVariables = ref([])

// 监听节点数据变化，初始化本地变量
watch(() => props.node?.data?.variables, (newVars) => {
  if (newVars) {
    localVariables.value = cloneDeep(newVars).map(v => ({
      ...v,
      debug_value: '' // 添加调试值字段
    }))
  }
}, { immediate: true })

// 格式化选择器显示
const formatSelector = (selector) => {
  if (!Array.isArray(selector)) return ''
  return `[${selector.join(' > ')}]`
}

// 调试相关状态
const isDebugging = ref(false)
const debugResult = ref('')

// 格式化值显示
const formatValue = (value) => {
  if (value === undefined || value === null) return '未设置'
  if (typeof value === 'object') return JSON.stringify(value)
  return String(value)
}

// 格式化调试结果
const formatDebugResult = (result) => {
  if (typeof result === 'object') {
    return JSON.stringify(result, null, 2)
  }
  return result
}

// 处理关闭
const handleClose = () => {
  debugResult.value = ''
  emit('update:visible', false)
  emit('close')
}

// 处理调试
const handleDebug = async () => {
  if (!props.node?.data) return
  
  try {
    isDebugging.value = true
    debugResult.value = ''
    
    // 收集变量输入
    const params = {}
    localVariables.value.forEach(v => {
      if (v.debug_value) {
        params[v.variable] = v.debug_value
      }
    })
    
    // 调用调试接口
    const result = await debugMethodNode({
      componentName: props.node.data.component_name,
      params
    })
    
    // 显示调试结果
    debugResult.value = result
    
    Message.success('调试完成')
  } catch (error) {
    console.error('❌ 方法节点调试 - 调试失败:', error)
    Message.error(error.message || '调试失败')
  } finally {
    isDebugging.value = false
  }
}
</script>

<style scoped>
.debug-drawer-content {
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

.debug-node-info h3 {
  margin: 0 0 16px;
  font-size: 16px;
  font-weight: 500;
  color: var(--color-text-1);
}

.info-item {
  display: flex;
  margin-bottom: 8px;
}

.info-item .label {
  width: 80px;
  color: var(--color-text-3);
  flex-shrink: 0;
}

.info-item .value {
  color: var(--color-text-1);
  flex: 1;
}

.component-config {
  background: var(--color-fill-2);
  border-radius: 4px;
  padding: 16px;
  margin-top: 12px;
}

.variables-section {
  margin-top: 20px;
}

h4 {
  font-size: 14px;
  margin-bottom: 12px;
  color: var(--color-text-2);
}

.variables-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.variable-item {
  background: white;
  border-radius: 4px;
  padding: 12px;
  border: 1px solid var(--color-border-2);
}

.variable-header {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.variable-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.variable-name {
  font-weight: 500;
  color: var(--color-text-1);
}

.variable-selector {
  color: #666;
  font-size: 11px;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
}

.variable-value {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
}

.value-label {
  color: var(--color-text-3);
  font-size: 12px;
}

.debug-result {
  margin-top: 24px;
  background: var(--color-fill-2);
  border-radius: 4px;
  overflow: hidden;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid var(--color-border-2);
}

.result-title {
  font-weight: 500;
  color: var(--color-text-1);
}

.result-content {
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.result-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
  font-family: monospace;
  font-size: 13px;
  line-height: 1.6;
  color: var(--color-text-1);
}

:deep(.arco-textarea-wrapper) {
  width: 100%;
  padding: 0;
}

:deep(.arco-textarea) {
  width: 100%;
  resize: vertical;
  line-height: 22px;
  padding: 5px 12px;
}
</style> 