<template>
  <div 
    :class="[
      'custom-node', 
      `node-type-${data.type}`,
      { 'node-highlighted': data.highlighted }
    ]"
    :style="data.style"
  >
    <div class="node-header">
      <div class="node-title" :title="data.title">{{ data.title }}</div>
      <div v-if="data.tooltip" class="node-tooltip" :title="data.tooltip.content">
        <icon-exclamation-circle-fill />
      </div>
    </div>
    <div class="node-body">
      <div class="node-info-row">
        <div class="node-type">{{ data.type }}</div>
        <div class="node-id" @click="copyNodeId" :title="'点击复制ID: ' + id">ID: {{ id }}</div>
      </div>
      
      <!-- 添加耗时显示 -->
      <div class="node-cost-time" v-if="data.cost_time !== undefined">
        <span class="time-label">耗时:</span>
        <span class="time-value">{{ data.cost_time }}ms</span>
      </div>
      
      <!-- 添加查看数据按钮 -->
      <div v-if="hasData" class="view-data">
        <a-button
          class="action-btn view-data-btn"
          type="text"
          size="mini"
          @click.stop="showDataModal"
        >
          <template #icon><icon-eye /></template>
          查看数据
        </a-button>
      </div>
      
      <!-- 添加查看详情按钮 -->
      <div v-if="data.component_name && hasData" class="view-detail">
        <a-button
          class="action-btn view-detail-btn"
          type="text"
          size="mini"
          @click.stop="showNodeDetail"
        >
          <template #icon><icon-file /></template>
          查看详情
        </a-button>
      </div>
      
      <div class="node-actions">
        <a-button 
          class="action-btn copy-btn" 
          type="text" 
          size="mini"
          @click.stop="handleCopy"
          title="复制节点"
        >
          <template #icon><icon-copy /></template>
        </a-button>
        <a-button 
          v-if="data.type === 'llm' || data.type === 'method'" 
          class="action-btn debug-btn" 
          type="text"
          size="mini"
          @click.stop="handleDebug"
          title="调试"
        >
          <template #icon><icon-bug /></template>
        </a-button>
        <button v-if="data.type === 'llm' || data.type === 'answer' || data.type === 'method' || data.type === 'variable-aggregator' || data.type === 'if-else' || data.type === 'coze_bot' || data.type === 'end'" class="edit-btn" @click.stop="openEditor">
          <span class="action-text">编辑</span>
        </button>
        <button class="delete-btn" @click.stop="deleteNode">
          <span class="action-text">删除</span>
        </button>
      </div>
      <div v-if="data.type === 'llm' && showNodeContent" class="llm-info">
        <div v-if="data.model" class="model-info">
          <span class="model-label">模型:</span>
          <span class="model-name">{{ data.model.name || '未设置' }}</span>
        </div>
        <div v-if="data.model_group" class="model-group-info">
          <span class="group-label">模型组:</span>
          <div class="group-list">
            <span v-for="(_, key) in data.model_group" :key="key" class="group-item">{{ key }}</span>
          </div>
        </div>
      </div>
      
      <div v-if="data.type === 'answer' && showNodeContent" class="answer-info">
        <div class="answer-preview">
          <span class="answer-label">答案:</span>
          <span class="answer-content">{{ formatAnswerPreview(data.answer) }}</span>
        </div>
        <div v-if="data.intermediate" class="intermediate-badge">中间节点</div>
      </div>
      
      <div v-if="data.type === 'method' && showNodeContent" class="method-info">
        <div class="component-name">
          <span class="component-label">组件:</span>
          <span class="component-value">{{ data.component_name || '未设置' }}</span>
        </div>
        <div v-if="data.variables && data.variables.length > 0" class="variables-info">
          <span class="variables-label">变量:</span>
          <span class="variables-count">{{ data.variables.length }}个</span>
        </div>
        <div v-if="data.outputs" class="outputs-info">
          <span class="outputs-label">输出:</span>
          <div class="outputs-list">
            <span v-for="(_, key) in data.outputs" :key="key" class="output-item">{{ key }}</span>
          </div>
        </div>
      </div>
      
      <div v-if="data.type === 'variable-aggregator' && showNodeContent" class="aggregator-info">
        <div class="output-type-info">
          <span class="output-type-label">输出类型:</span>
          <span class="output-type-value">{{ data.output_type || 'string' }}</span>
        </div>
        <div v-if="data.variables && data.variables.length > 0" class="variables-info">
          <span class="variables-label">变量:</span>
          <span class="variables-count">{{ data.variables.length }}个</span>
        </div>
        <div v-if="data.advanced_settings && data.advanced_settings.group_enabled" class="groups-info">
          <span class="groups-label">分组:</span>
          <span class="groups-count">{{ data.advanced_settings.groups.length }}个</span>
        </div>
      </div>
      
      <!-- IF-ELSE 节点的特殊显示 -->
      <div v-if="data.type === 'if-else'" class="if-else-info">
      </div>

      <!-- COZE 节点的特殊显示 -->
      <div v-if="data.type === 'coze_bot'" class="coze-info">
        <div v-if="data.bot_info" class="bot-info">
          <span class="bot-label">机器人:</span>
          <span class="bot-name">{{ data.bot_info.bot_name || '未设置' }}</span>
        </div>
        <div v-if="data.prompt_template && data.prompt_template.length > 0" class="prompt-info">
          <span class="prompt-label">提示词:</span>
          <span class="prompt-count">{{ data.prompt_template.length }}条</span>
        </div>
        <div v-if="data.custom_variables" class="variables-info">
          <span class="variables-label">自定义变量:</span>
          <span class="variables-count">{{ Object.keys(data.custom_variables).length }}个</span>
        </div>
      </div>

      <div v-if="data.type === 'end'" class="end-info">
        <div class="end-icon">
          <icon-stop />
        </div>
        <div class="end-desc">流程结束</div>
      </div>
    </div>
    
    <!-- 普通节点的连接点 -->
    <Handle 
      v-if="data.type !== 'if-else' && data.type !== 'end'"
      type="source" 
      :position="Position.Right" 
      id="source" 
      class="source-handle"
    />
    
    <!-- 为 IF-ELSE 节点的每个分支添加单独的连接点 -->
    <template v-if="data.type === 'if-else'">
      <div class="if-else-container">
        <!-- IF 分支 -->
        <div v-if="data.cases && data.cases.length > 0" class="branch-container if-branch">
          <div class="branch-header">
            <span class="branch-type">IF</span>
            <span class="branch-id">{{ data.cases[0].case_id }}</span>
          </div>
          <div v-if="data.cases[0].conditions && data.cases[0].conditions.length > 0" class="branch-conditions">
            <div v-for="(condition, index) in data.cases[0].conditions" :key="index" class="condition-item">
              {{ formatCondition(condition) }}
            </div>
            <div class="logical-operator">
              {{ data.cases[0].logical_operator === 'and' ? '满足所有条件' : '满足任一条件' }}
            </div>
          </div>
          <Handle
            type="source"
            :position="Position.Right"
            :id="data.cases[0].case_id"
            class="case-handle if-handle"
          />
        </div>

        <!-- ELSE IF 分支 -->

        <div v-for="(caseItem, index) in data.cases.slice(1)"
             :key="caseItem.case_id"
             class="branch-container else-if-branch">
          <div class="branch-header">
            <span class="branch-type">ELSE IF {{ index + 1 }}</span>
            <span class="branch-id">{{ caseItem.case_id }}</span>
          </div>
          <div v-if="caseItem.conditions && caseItem.conditions.length > 0" class="branch-conditions">
            <div v-for="(condition, condIndex) in caseItem.conditions" :key="condIndex" class="condition-item">
              {{ formatCondition(condition) }}
            </div>
            <div class="logical-operator">
              {{ caseItem.logical_operator === 'and' ? '满足所有条件' : '满足任一条件' }}
            </div>
          </div>
          <Handle
            type="source"
            :position="Position.Right"
            :id="caseItem.case_id"
            class="case-handle else-if-handle"
          />
        </div>

        <!-- ELSE 分支 -->
        <div class="branch-container else-branch">
          <div class="branch-header">
            <span class="branch-type">ELSE</span>
          </div>
          <div class="branch-desc">默认分支</div>
          <Handle
            type="source"
            :position="Position.Right"
            id="false"
            class="case-handle else-handle"
          />
        </div>
      </div>
    </template>
    
    <Handle 
      type="target" 
      :position="Position.Left" 
      id="target" 
      class="target-handle"
    />
  </div>

  <!-- 添加数据查看弹窗 -->
  <a-modal
    v-model:visible="dataModalVisible"
    :title="`节点数据 - ${data.title || data.type}`"
    @cancel="hideDataModal"
    :footer="false"
    :mask-closable="true"
    :escape-key-closing="true"
    :auto-focus="false"
    class="data-modal"
    :width="900"
  >
    <div class="modal-content">
      <a-tabs>
        <a-tab-pane v-if="hasInputs" key="input" title="输入数据">
          <div class="data-content">
            <vue-json-pretty
              :data="formatData(data.result_inputs)"
              :deep="jsonPrettyOptions.deep"
              :show-length="jsonPrettyOptions.showLength"
              :show-line="jsonPrettyOptions.showLine"
              :show-double-quotes="jsonPrettyOptions.showDoubleQuotes"
              :selectable-type="jsonPrettyOptions.selectableType"
              :path-selectable="(path, data) => true"
              @click="(path, data) => {}"
              class="json-viewer"
            />
          </div>
        </a-tab-pane>
        <a-tab-pane v-if="hasOutputs" key="output" title="输出数据">
          <div class="data-content">
            <vue-json-pretty
              :data="formatData(data.result_outputs)"
              :deep="jsonPrettyOptions.deep"
              :show-length="jsonPrettyOptions.showLength"
              :show-line="jsonPrettyOptions.showLine"
              :show-double-quotes="jsonPrettyOptions.showDoubleQuotes"
              :selectable-type="jsonPrettyOptions.selectableType"
              :path-selectable="(path, data) => true"
              @click="(path, data) => {}"
              class="json-viewer"
            />
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-modal>

  <!-- 添加节点详情组件 -->
  <NodeDetail
    v-if="showDetail"
    :component-name="data.component_name"
    :trace-id="data.traceId"
    @close="hideNodeDetail"
  />
</template>

<script setup>
import { Handle, Position } from '@vue-flow/core'
import { ref, computed } from 'vue'
import { IconCopy, IconBug, IconExclamationCircleFill, IconEye, IconFile, IconStop } from '@arco-design/web-vue/es/icon'
import VueJsonPretty from 'vue-json-pretty'
import NodeDetail from './NodeDetail.vue'
import 'vue-json-pretty/lib/styles.css'
import { getLogTraceId } from '@/utils/storage'
import { Message } from '@arco-design/web-vue'

const props = defineProps({
  id: {
    type: String,
    required: true
  },
  data: {
    type: Object,
    required: true
  }
})

const emit = defineEmits([
  'editLlm',
  'editAnswer',
  'editMethod',
  'editVariableAggregator',
  'editIfElse',
  'editCoze',
  'editEnd',
  'deleteNode',
  'copyNode',
  'debugLlm',
  'debugMethod',
  'showNodeDetail'
])

const showNodeContent = computed(() => {
  return props.data.type !== 'if-else';
});

// 获取分支名称
const getBranchName = (caseItem, index) => {
  const totalBranches = props.data.cases.length;
  
  if (index === 0) {
    return 'IF';
  } else if (index === totalBranches - 1 && caseItem.case_id === 'true') {
    return 'ELSE';
  } else {
    return `ELSE IF ${index}`;
  }
}

// 获取分支CSS类
const getBranchClass = (caseItem, index) => {
  if (caseItem.case_id === 'true') {
    return 'default-branch'
  } else if (index === 0) {
    return 'if-branch'
  } else {
    return 'else-if-branch'
  }
}

// 获取分支连接点样式
const getBranchStyle = (index, totalBranches) => {
  // 计算垂直位置，均匀分布
  const yPercentage = 20 + index * (60 / (totalBranches - 1 || 1));
  
  return {
    top: `${yPercentage}%`,
    right: '-5px',
    transform: 'translateX(50%)'
  };
}

// 格式化条件显示
const formatCondition = (condition) => {
  if (!condition || !condition.variable_selector) {
    return '无效条件'
  }
  
  let varName = condition.variable_selector[1] || '变量'
  let operator = ''
  let value = condition.value || ''
  
  switch (condition.comparison_operator) {
    case 'equals': operator = '='; break
    case 'not equals': operator = '≠'; break
    case 'contains': operator = '包含'; break
    case 'not contains': operator = '不包含'; break
    case 'empty': return `${varName} 为空`
    case 'not empty': return `${varName} 不为空`
    case 'greater than': operator = '>'; break
    case 'less than': operator = '<'; break
    case 'greater or equal': operator = '≥'; break
    case 'less or equal': operator = '≤'; break
    case 'is true': return `${varName} 为真`
    case 'is false': return `${varName} 为假`
    default: operator = condition.comparison_operator
  }
  
  return `${varName} ${operator} ${value}`
}

const openEditor = () => {
  if (props.data.type === 'llm') {
    emit('editLlm', { id: props.id, data: props.data })
  } else if (props.data.type === 'answer') {
    emit('editAnswer', { id: props.id, data: props.data })
  } else if (props.data.type === 'method') {
    emit('editMethod', { id: props.id, data: props.data })
  } else if (props.data.type === 'variable-aggregator') {
    emit('editVariableAggregator', { id: props.id, data: props.data })
  } else if (props.data.type === 'if-else') {
    emit('editIfElse', { id: props.id, data: props.data })
  } else if (props.data.type === 'coze_bot') {
    emit('editCoze', { id: props.id, data: props.data })
  } else if (props.data.type === 'start') {
    emit('editStart', { id: props.id, data: props.data })
  } else if (props.data.type === 'end') {
    emit('editEnd', { id: props.id, data: props.data })
  }
}

const deleteNode = () => {
  // 直接触发删除事件，由父组件处理确认逻辑
  console.log('触发删除事件:', props.id)
  emit('deleteNode', props.id)
}

// 格式化答案预览
const formatAnswerPreview = (answer) => {
  if (!answer) return '未设置'
  
  // 如果是变量引用，直接显示
  if (answer.includes('{{#') && answer.includes('#}}')) {
    return answer
  }
  
  // 否则截断显示
  return answer.length > 30 ? answer.substring(0, 30) + '...' : answer
}

// 复制节点ID到剪贴板 - 兼容HTTP环境
const copyNodeId = async () => {
  try {
    // 优先使用现代 Clipboard API（HTTPS/localhost）
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(props.id)
      Message.success(`节点ID已复制: ${props.id}`)
    } else {
      // 降级方案：使用传统方法（HTTP环境）
      const textArea = document.createElement('textarea')
      textArea.value = props.id
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()

      try {
        const successful = document.execCommand('copy')
        if (successful) {
          Message.success(`节点ID已复制: ${props.id}`)
        } else {
          throw new Error('execCommand failed')
        }
      } catch (fallbackErr) {
        // 最后的降级方案：显示ID让用户手动复制
        Message.info(`请手动复制节点ID: ${props.id}`)
        console.error('复制失败，请手动复制:', fallbackErr)
      } finally {
        document.body.removeChild(textArea)
      }
    }
  } catch (err) {
    Message.error('复制失败，请手动复制')
    console.error('复制失败:', err)
  }
}

// 添加复制处理函数
const handleCopy = (event) => {
  event.stopPropagation()
  emit('copyNode', props.id)
}

// 添加调试处理函数
const handleDebug = () => {
  if (props.data.type === 'llm') {
    emit('debugLlm', { id: props.id, data: props.data })
  } else if (props.data.type === 'method') {
    emit('debugMethod', { id: props.id, data: props.data })
  }
}

// 添加 formatConditions 方法
const formatConditions = (caseItem) => {
  if (!caseItem.conditions || caseItem.conditions.length === 0) return '';
  
  return caseItem.conditions.map(cond => 
    `${cond.variable_selector.join('.')} ${cond.comparison_operator} ${cond.value}`
  ).join(caseItem.logical_operator === 'or' ? ' OR ' : ' AND ');
}

// 添加数据相关的计算属性
const hasInputs = computed(() => {
  return props.data.result_inputs && Object.keys(props.data.result_inputs).length > 0
})

const hasOutputs = computed(() => {
  return props.data.result_outputs && Object.keys(props.data.result_outputs).length > 0
})

const hasData = computed(() => {
  return hasInputs.value || hasOutputs.value
})

// 格式化数据显示
const formatData = (data) => {
  if (!data) return {}
  try {
    return typeof data === 'string' ? JSON.parse(data) : data
  } catch (e) {
    return { value: String(data) }
  }
}

// 自定义 JSON 显示主题
const jsonPrettyOptions = {
  deep: 3,
  showLength: true,
  showLine: true,
  showDoubleQuotes: false,
  selectableType: 'single'
}

// 数据弹窗控制
const dataModalVisible = ref(false)

const showDataModal = () => {
  dataModalVisible.value = true
}

const hideDataModal = () => {
  dataModalVisible.value = false
}

// 添加节点详情控制
const showDetail = ref(false)

const showNodeDetail = () => {
  console.log('点击查看详情，当前节点数据：', props.data)
  // 获取存储的 traceId
  const storedTraceId = getLogTraceId()
  
  // 克隆节点数据
  const nodeData = { ...props.data }
  
  if (storedTraceId) {
    // 如果有存储的 traceId，使用它
    emit('showNodeDetail', { 
      id: props.id, 
      data: { 
        ...nodeData,
        traceId: storedTraceId 
      } 
    })
  } else {
    // 如果没有存储的 traceId，使用原有的
    emit('showNodeDetail', { id: props.id, data: nodeData })
  }
}

const hideNodeDetail = () => {
  showDetail.value = false
}
</script>

<style scoped>
.custom-node {
  padding: 10px;
  border-radius: 5px;
  background: white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  width: 200px;
  font-size: 12px;
  position: relative;
}

.node-header {
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
  margin-bottom: 8px;
}

.node-title {
  font-weight: bold;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}

.node-body {
  font-size: 12px;
}

.node-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.node-id {
  color: #666;
  font-size: 10px;
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.node-id:hover {
  background-color: #e0e0e0;
  color: #333;
}

.node-type {
  color: #666;
  font-size: 10px;
  text-transform: uppercase;
  margin-bottom: 5px;
}

.node-desc {
  color: #666;
  margin-bottom: 8px;
  font-size: 11px;
  word-break: break-word;
}

/* LLM 节点特殊样式 */
.llm-info {
  margin-top: 8px;
  font-size: 11px;
}

.model-info {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.model-label, .group-label, .answer-label, .component-label, .variables-label, .outputs-label, .output-type-label, .groups-label {
  font-weight: bold;
  margin-right: 4px;
}

.model-name {
  color: #4caf50;
}

.model-group-info {
  display: flex;
  align-items: flex-start;
}

.group-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.group-item {
  background-color: #e8f5e9;
  color: #388e3c;
  padding: 1px 4px;
  border-radius: 3px;
  font-size: 9px;
}

/* Answer 节点特殊样式 */
.answer-info {
  margin-top: 8px;
  font-size: 11px;
}

.answer-preview {
  display: flex;
  align-items: flex-start;
  margin-bottom: 4px;
}

.answer-content {
  color: #9c27b0;
  word-break: break-all;
}

.intermediate-badge {
  display: inline-block;
  background-color: #ffecb3;
  color: #ff8f00;
  padding: 1px 4px;
  border-radius: 3px;
  font-size: 9px;
  margin-top: 4px;
}

/* Method 节点特殊样式 */
.method-info {
  margin-top: 8px;
  font-size: 11px;
}

.component-name {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.component-value {
  color: #ff9800;
}

.variables-info {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.variables-count {
  color: #2196f3;
}

.outputs-info {
  display: flex;
  align-items: flex-start;
}

.outputs-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.output-item {
  background-color: #e8f5e9;
  color: #388e3c;
  padding: 1px 4px;
  border-radius: 3px;
  font-size: 9px;
}

/* 变量聚合节点特殊样式 */
.aggregator-info {
  margin-top: 8px;
  font-size: 11px;
}

.output-type-info {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.output-type-value {
  color: #607d8b;
}

.groups-info {
  display: flex;
  align-items: center;
}

.groups-count {
  color: #ff5722;
}

/* 根据节点类型设置不同颜色 */
.node-type-llm {
  border-left: 4px solid #4caf50;
}

.node-type-if-else {
  border-left: 4px solid #2196f3;
}

.node-type-method {
  border-left: 4px solid #ff9800;
}

.node-type-answer {
  border-left: 4px solid #9c27b0;
}

.node-type-variable-aggregator {
  border-left: 4px solid #607d8b;
}

.node-type-code {
  border-left: 4px solid #e91e63;
}

/* 连接点样式 */
.source-handle {
  width: 10px;
  height: 10px;
  background: #555;
  border-radius: 50%;
}

.target-handle {
  width: 10px;
  height: 10px;
  background: #555;
  border-radius: 50%;
}

.source-handle:hover, .target-handle:hover {
  background: #2196f3;
}

/* If-Else 节点特殊样式 */
.if-else-info {
  margin-top: 8px;
  font-size: 11px;
}

.if-else-container {
  padding: 10px 5px;
  margin: 5px 0;
}

.branch-container {
  position: relative;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 8px 12px;
  margin-bottom: 12px;
  background: white;
  transition: all 0.2s ease;
}

.branch-container:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.branch-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 4px;
  border-bottom: 1px solid #eee;
}

.branch-type {
  font-weight: bold;
  font-size: 12px;
  color: #333;
}

.branch-id {
  font-size: 10px;
  color: #666;
  background: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
}

.branch-conditions {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.condition-item {
  font-size: 11px;
  color: #444;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  border-left: 2px solid #ddd;
}

.logical-operator {
  font-size: 10px;
  color: #666;
  text-align: right;
  margin-top: 4px;
  font-style: italic;
}

.branch-desc {
  font-size: 11px;
  color: #666;
  text-align: center;
  padding: 4px;
}

/* 分支特定样式 */
.if-branch {
  border-left: 3px solid #2196f3;
}

.if-branch .condition-item {
  border-left-color: #2196f3;
}

.else-if-branch {
  border-left: 3px solid #ff9800;
}

.else-if-branch .condition-item {
  border-left-color: #ff9800;
}

.else-branch {
  border-left: 3px solid #f44336;
}

/* 连接点样式 */
.case-handle {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  position: absolute;
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
  transition: all 0.2s ease;
}

.if-handle {
  background-color: #2196f3;
}

.else-if-handle {
  background-color: #ff9800;
}

.else-handle {
  background-color: #f44336;
}

.case-handle:hover {
  transform: translateY(-50%) scale(1.2);
  box-shadow: 0 0 0 4px rgba(33, 150, 243, 0.2);
}

/* 移除之前的位置特定样式 */
.branch-0 + .branch-label,
.branch-1 + .branch-label,
.branch-2 + .branch-label {
  right: -5px;
  top: auto;
  bottom: auto;
  left: auto;
  transform: translateX(100%);
}

/* 滚动条样式 */
.cases-preview::-webkit-scrollbar {
  width: 4px;
}

.cases-preview::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.cases-preview::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 4px;
}

.cases-preview::-webkit-scrollbar-thumb:hover {
  background: #999;
}

.handle-label {
  position: absolute;
  font-size: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 4px;
  border-radius: 3px;
  white-space: nowrap;
  pointer-events: none;
}

.true-label {
  top: -20px;
  right: -5px;
}

.false-label {
  bottom: -20px;
  left: -5px;
}

.false-handle {
  bottom: -5px !important;
  right: 50% !important;
  transform: translateX(50%);
  background-color: #f44336;
}

.source-handle {
  background-color: #4caf50;
}

/* COZE 节点特殊样式 */
.coze-info {
  margin-top: 8px;
  font-size: 11px;
}

.bot-info {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.bot-label {
  font-weight: bold;
  margin-right: 4px;
}

.bot-name {
  color: #9c27b0;
}

.prompt-info {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.prompt-label {
  font-weight: bold;
  margin-right: 4px;
}

.prompt-count {
  color: #ff9800;
}

/* 添加 COZE 节点的边框颜色 */
.node-type-coze_bot {
  border-left: 4px solid #9c27b0;
}

/* 高亮节点样式 */
@keyframes highlight-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(33, 150, 243, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(33, 150, 243, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(33, 150, 243, 0);
  }
}

.node-highlighted {
  animation: highlight-pulse 1.5s ease-in-out infinite;
  border: 2px solid #2196f3;
  z-index: 100;
}

/* 开始节点特殊样式 */
.start-info {
  margin-top: 8px;
  font-size: 11px;
  text-align: center;
}

.start-icon {
  font-size: 24px;
  color: #f44336;
  margin: 10px 0;
}

/* 根据节点类型设置不同颜色 */
.node-type-start {
  border-left: 4px solid #f44336;
}

.copy-btn {
  color: #2196f3;
}

.copy-btn:hover {
  color: #0d8aee;
  background-color: rgba(33, 150, 243, 0.1);
}

.node-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
  margin: 8px 0;
  padding: 8px 0;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  transition: all 0.2s;
  padding: 2px;
  margin-right: 2px;
}

.action-btn:hover {
  color: #333;
  background: #f5f5f5;
  transform: scale(1.1);
}

.debug-btn {
  color: #1976d2;
}

.debug-btn:hover {
  background-color: rgba(25, 118, 210, 0.1);
}

/* 添加新的样式 */
.if-else-container {
  padding: 10px;
}

.branch-container {
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 20px;
  background: rgba(255, 255, 255, 0.8);
}

.case-handle {
  width: 10px;
  height: 10px;
  background: #555;
  border-radius: 50%;
  position: absolute;
  right: -5px;
  top: 50%;
  transform: translateY(-50%);
}

.case-handle:hover {
  background: #2196f3;
}

/* 添加节点状态相关样式 */
.node-tooltip {
  position: absolute;
  top: 50%;
  right: -24px;
  transform: translateY(-50%);
  color: #f53f3f;
  cursor: pointer;
  font-size: 16px;
}

.node-tooltip:hover::after {
  content: attr(title);
  position: absolute;
  top: 100%;
  right: 0;
  background: #f53f3f;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
}

/* 成功状态 */
:deep(.success) {
  border: 2px solid #00b42a !important;
  background: #f0fff1 !important;
  box-shadow: 0 0 8px rgba(0, 180, 42, 0.2) !important;
}

/* 失败状态 */
:deep(.error) {
  border: 2px solid #f53f3f !important;
  background: #fff2f2 !important;
  box-shadow: 0 0 8px rgba(245, 63, 63, 0.4) !important;
}

/* 输入输出数据样式 */
.io-data {
  margin: 8px 0;
  font-size: 11px;
}

.data-preview {
  max-height: 150px;
  overflow-y: auto;
  background: #f8f9fa;
  border-radius: 4px;
  padding: 4px;
}

.data-preview pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
  font-family: monospace;
  font-size: 11px;
}

:deep(.arco-collapse) {
  border: none;
  background: transparent;
}

:deep(.arco-collapse-item) {
  border-bottom: none;
}

:deep(.arco-collapse-item-header) {
  background: transparent;
  padding: 4px 0;
  font-size: 11px;
}

:deep(.arco-collapse-item-content) {
  background: transparent;
  padding: 4px 0;
}

/* 查看数据和详情按钮样式 */
.view-data,
.view-detail {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 8px 0;
}

.view-data-btn,
.view-detail-btn {
  color: #2196f3;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 150px;
}

.view-data-btn:hover,
.view-detail-btn:hover {
  color: #1976d2;
  background-color: rgba(25, 118, 210, 0.1);
}

/* 弹窗内容样式 */
:deep(.data-modal) {
  width: 80vw !important;
  max-width: 1000px;
}

:deep(.data-modal .arco-modal-body) {
  padding: 20px;
  background: #f8f9fa;
}

.data-content {
  background: #fff;
  border-radius: 4px;
  padding: 16px;
  max-height: calc(80vh - 120px);
  overflow-y: auto;
}

.data-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
  font-family: monospace;
  font-size: 13px;
  line-height: 1.5;
}

:deep(.arco-tabs-content) {
  padding: 0;
}

/* JSON 查看器样式 */
.data-content {
  background: #fff;
  border-radius: 4px;
  padding: 16px;
  max-height: calc(80vh - 120px);
  overflow-y: auto;
}

:deep(.json-viewer) {
  font-family: 'JetBrains Mono', Menlo, Monaco, Consolas, 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  background: transparent !important;
}

:deep(.json-viewer .vjs-tree) {
  font-family: inherit;
}

:deep(.json-viewer .vjs-tree .vjs-tree__node) {
  cursor: pointer;
}

:deep(.json-viewer .vjs-tree .vjs-tree__node:hover) {
  background-color: rgba(64, 158, 255, 0.1);
}

:deep(.json-viewer .vjs-tree .vjs-value__string) {
  color: #42b983;
}

:deep(.json-viewer .vjs-tree .vjs-value__number) {
  color: #f08d49;
}

:deep(.json-viewer .vjs-tree .vjs-value__boolean) {
  color: #ff5722;
}

:deep(.json-viewer .vjs-tree .vjs-value__null) {
  color: #999;
}

:deep(.json-viewer .vjs-tree .vjs-key) {
  color: #409eff;
}

/* 优化弹窗样式 */
:deep(.data-modal .arco-modal-body) {
  padding: 20px;
  background: #f8f9fa;
}

:deep(.arco-tabs-nav) {
  margin-bottom: 16px;
}

:deep(.arco-tabs-content) {
  padding: 0;
}

/* 添加耗时样式 */
.node-cost-time {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 11px;
}

.time-label {
  font-weight: bold;
  margin-right: 4px;
  color: #666;
}

.time-value {
  color: #ff9800;
  font-weight: bold;
}

/* 添加结束节点的边框颜色 */
.node-type-end {
  border-left: 4px solid #f44336;
}

/* 结束节点特殊样式 */
.end-info {
  margin-top: 8px;
  font-size: 11px;
  text-align: center;
}

.end-icon {
  font-size: 24px;
  color: #f44336;
  margin: 10px 0;
}

.end-desc {
  color: #666;
  font-size: 12px;
}

/* 添加新的样式 */
.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  transition: all 0.2s;
  padding: 2px;
  margin-right: 2px;
}

.action-btn:hover {
  color: #333;
  background: #f5f5f5;
  transform: scale(1.1);
}

.copy-btn {
  color: #2196f3;
}

.copy-btn:hover {
  color: #0d8aee;
  background-color: rgba(33, 150, 243, 0.1);
}

.debug-btn {
  color: #1976d2;
}

.debug-btn:hover {
  background-color: rgba(25, 118, 210, 0.1);
}

/* LLM 节点的特殊样式 */
.node-type-llm .node-actions {
  display: flex;
  gap: 4px;
  justify-content: space-between;
}

.node-type-llm .node-actions::before {
  content: '';
  flex: 1;
}

.node-type-llm .node-actions::after {
  content: '';
  flex: 1;
}

.node-type-llm .action-btn {
  padding: 2px;
}

.node-type-llm .edit-btn,
.node-type-llm .delete-btn {
  margin-left: 8px;
}

.edit-btn, .delete-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  color: #333;
  transition: all 0.2s;
}

.edit-btn:hover {
  background-color: #e0e0e0;
}

.delete-btn {
  background-color: #ffebee;
  color: #d32f2f;
}

.delete-btn:hover {
  background-color: #ffcdd2;
}
</style> 